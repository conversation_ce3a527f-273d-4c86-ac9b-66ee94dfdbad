# 多模型 OpenAI 配置指南

本指南说明如何配置多个 OpenAI 提供商（Azure OpenAI + 官方 OpenAI），为不同用户层级提供服务并支持自动故障转移。

## 🎯 使用场景

### 多模型设置（推荐）

- **付费用户** → Azure GPT-4.1（高质量，成本较高）
- **免费用户** → Azure GPT-4.1-mini（良好质量，成本效益高）
- **故障转移** → OpenAI 官方 GPT-4.1-mini（当 Azure 不可用时）

### 单一 Azure 提供商设置

- **所有用户** → 单一 Azure OpenAI 部署
- **备用** → OpenAI 官方（可选）

### 传统单一提供商设置

- **所有用户** → 单一提供商（OpenAI 官方或 Azure）
- **无故障转移** → 简单配置，适合开发环境

## 🔧 配置

### 环境变量

#### 多模型配置（推荐）

| 变量名                         | 描述                    | 必需 | 示例                    |
| ------------------------------ | ----------------------- | ---- | ----------------------- |
| `OPENAI_API_KEY`               | OpenAI 官方 API 密钥    | 否\* | `sk-...`                |
| `OPENAI_AZURE_API_KEY`         | Azure OpenAI API 密钥   | 否\* | `your-azure-api-key`    |
| `OPENAI_MULTI_MODEL_ENABLED`   | 启用多模型支持          | 是   | `true`                  |
| `OPENAI_AZURE_RESOURCE_NAME`   | Azure 资源名称          | 是   | `my-openai-resource`    |
| `OPENAI_AZURE_PAID_DEPLOYMENT` | 付费用户的 Azure 部署名 | 是   | `gpt-4.1`               |
| `OPENAI_AZURE_FREE_DEPLOYMENT` | 免费用户的 Azure 部署名 | 是   | `gpt-4.1-mini`          |
| `PROXY_URL`                    | HTTP 代理（可选）       | 否   | `http://127.0.0.1:7890` |

\*至少需要一个 API 密钥（OpenAI 或 Azure）。

#### 单一 Azure 提供商配置

| 变量名                         | 描述                  | 必需 | 示例                 |
| ------------------------------ | --------------------- | ---- | -------------------- |
| `OPENAI_AZURE_API_KEY`         | Azure OpenAI API 密钥 | 是   | `your-azure-api-key` |
| `OPENAI_PROVIDER`              | 设置为 `azure`        | 是   | `azure`              |
| `OPENAI_AZURE_RESOURCE_NAME`   | Azure 资源名称        | 是   | `my-openai-resource` |
| `OPENAI_AZURE_DEPLOYMENT_NAME` | Azure 部署名称        | 是   | `gpt-4.1`            |

#### 单一 OpenAI 官方提供商配置

| 变量名            | 描述                 | 必需 | 示例                    |
| ----------------- | -------------------- | ---- | ----------------------- |
| `OPENAI_API_KEY`  | OpenAI 官方 API 密钥 | 是   | `sk-...`                |
| `OPENAI_PROVIDER` | 设置为 `openai`      | 否   | `openai`（默认值）      |
| `PROXY_URL`       | HTTP 代理（可选）    | 否   | `http://127.0.0.1:7890` |

#### 重要说明：OPENAI_MULTI_MODEL_ENABLED 的行为

- **`OPENAI_MULTI_MODEL_ENABLED=true`**：启用多模型系统，根据用户层级选择不同模型
- **`OPENAI_MULTI_MODEL_ENABLED=false`** 或未设置：使用传统单一提供商模式
  - 如果设置了 `OPENAI_PROVIDER=azure`，则所有用户使用 Azure OpenAI
  - 如果设置了 `OPENAI_PROVIDER=openai` 或未设置，则所有用户使用 OpenAI 官方
  - 如果设置了 `OPENAI_PROVIDER=custom`，则所有用户使用自定义提供商

### 配置示例

#### 多模型设置（推荐）

```bash
# 启用多模型支持
export OPENAI_MULTI_MODEL_ENABLED="true"

# Azure OpenAI 配置（主要模型）
export OPENAI_AZURE_API_KEY="your-azure-openai-api-key"
export OPENAI_AZURE_RESOURCE_NAME="ai-david5226ai452319666352"
export OPENAI_AZURE_PAID_DEPLOYMENT="uniscribe-gpt-4-1-east-us-2"
export OPENAI_AZURE_FREE_DEPLOYMENT="uniscribe-gpt-4-1-mini-east-us-2"

# OpenAI 官方配置（故障转移）
export OPENAI_API_KEY="sk-your-openai-official-api-key"

# 可选：代理配置（仅用于 OpenAI 官方）
export PROXY_URL="http://127.0.0.1:7890"

# 可选：健康检查（生产环境推荐）
export OPENAI_HEALTH_CHECK_ENABLED="true"
```

#### 单一 Azure 提供商设置

```bash
# 基础 Azure OpenAI 设置
export OPENAI_AZURE_API_KEY="your-azure-openai-api-key"
export OPENAI_PROVIDER="azure"
export OPENAI_AZURE_RESOURCE_NAME="ai-david5226ai452319666352"
export OPENAI_AZURE_DEPLOYMENT_NAME="uniscribe-gpt-4-1-east-us-2"

# 可选：禁用健康检查
export OPENAI_HEALTH_CHECK_ENABLED="false"
```

#### 单一 OpenAI 官方提供商设置

```bash
# 基础 OpenAI 官方设置
export OPENAI_API_KEY="sk-your-openai-api-key"
export OPENAI_PROVIDER="openai"  # 可选，这是默认值

# 可选：代理配置
export PROXY_URL="http://127.0.0.1:7890"

# 可选：健康检查
export OPENAI_HEALTH_CHECK_ENABLED="true"
```

#### 传统模式（OPENAI_MULTI_MODEL_ENABLED=false）

```bash
# 方式1：使用 Azure OpenAI（所有用户使用同一个部署）
export OPENAI_MULTI_MODEL_ENABLED="false"  # 或者不设置
export OPENAI_PROVIDER="azure"
export OPENAI_AZURE_API_KEY="your-azure-api-key"
export OPENAI_AZURE_RESOURCE_NAME="ai-david5226ai452319666352"
export OPENAI_AZURE_DEPLOYMENT_NAME="uniscribe-gpt-4-1-east-us-2"

# 方式2：使用 OpenAI 官方（所有用户使用官方 API）
export OPENAI_MULTI_MODEL_ENABLED="false"  # 或者不设置
export OPENAI_API_KEY="sk-your-openai-api-key"
# OPENAI_PROVIDER="openai" 是默认值，可以不设置
export PROXY_URL="http://127.0.0.1:7890"
```

## 🏗️ Azure 设置步骤

### 1. 创建 Azure OpenAI 资源

#### 方式 A：使用 Azure CLI

```bash
# 创建资源组
az group create --name openai-rg --location eastus

# 创建 Azure OpenAI 资源
az cognitiveservices account create \
  --name my-openai-resource \
  --resource-group openai-rg \
  --kind OpenAI \
  --sku S0 \
  --location eastus
```

#### 方式 B：使用 Azure 门户

1. 访问 [Azure 门户](https://portal.azure.com)
2. 点击"创建资源"
3. 搜索"Azure OpenAI"
4. 点击"创建"
5. 填写详细信息：
   - **资源组**：创建新的或选择现有的
   - **区域**：选择你偏好的区域
   - **名称**：`my-openai-resource`（必须全局唯一）
   - **定价层**：标准 S0
6. 点击"查看 + 创建"然后"创建"
7. 等待部署完成

### 2. 部署模型

你需要在 Azure OpenAI Studio 中部署**两个**模型：

#### 为付费用户部署 GPT-4.1

1. 访问 Azure OpenAI Studio
2. 导航到"部署"
3. 点击"创建新部署"
4. 选择"gpt-4.1"模型
5. 设置部署名称：`gpt-4-1-east-us-2`
6. 根据需要配置容量

#### 为免费用户部署 GPT-4.1-mini

1. 创建另一个部署
2. 选择"gpt-4.1-mini"模型
3. 设置部署名称：`gpt-4-1-mini-east-us-2`
4. 根据需要配置容量

### 3. 获取 API 密钥

#### 方式 A：使用 Azure CLI

```bash
# 获取 API 密钥
az cognitiveservices account keys list \
  --name my-openai-resource \
  --resource-group openai-rg
```

#### 方式 B：使用 Azure 门户

1. 在门户中访问你的 Azure OpenAI 资源
2. 在左侧边栏中导航到"密钥和终结点"
3. 复制其中一个 API 密钥（密钥 1 或密钥 2）
4. 记下终结点 URL（你需要从中获取资源名称）

## 💻 代码使用

### 自动集成

系统自动与现有的文本处理任务集成。当处理任务时，系统会：

1. **从数据库读取任务优先级**（`priority` 字段）
2. **确定用户层级**：`priority=0` → 免费用户，`priority=1` → 付费用户
3. **选择合适的提供商**：免费 → Azure GPT-4.1-mini，付费 → Azure GPT-4.1
4. **优雅降级**：如果 Azure 失败 → OpenAI 官方 GPT-4.1-mini

### 手动使用

```go
import (
    "context"
    "uniscribe-service/pkg/openai"
    "github.com/sashabaranov/go-openai"
)

// 获取多模型管理器
manager := openai.GetMultiModelManager()

// 为付费用户创建聊天完成
response, err := manager.CreateChatCompletionForTier(
    context.Background(),
    openai.TierPaid,  // 使用 Azure GPT-4.1
    openai.ChatCompletionRequest{
        Messages: []openai.ChatCompletionMessage{
            {Role: "user", Content: "你好！"},
        },
    },
)

// 为免费用户创建聊天完成
response, err := manager.CreateChatCompletionForTier(
    context.Background(),
    openai.TierFree,  // 使用 Azure GPT-4.1-mini
    request,
)
```

### 文本处理集成

文本处理器（`SummaryProcessor`、`OutlineProcessor`、`QAExtractionProcessor`）现在会根据用户层级自动使用合适的模型：

```go
// 这在任务处理管道中自动发生
func (p *TextTaskProcessorImpl) Process(ctx context.Context, task Task) error {
    // 系统自动从 task.Priority 检测用户层级
    userTier := getUserTierFromTask(task)  // 0 → TierFree, 1 → TierPaid

    // 处理器为用户层级使用合适的模型
    processor := &SummaryProcessor{}
    result, err := processor.ProcessWithTier(ctx, task.TranscriptionText, userTier)
    // ...
}
```

### Advanced Usage

```go
// Get specific provider for a tier
provider, err := manager.GetProviderForTier(openai.TierPaid)
if err != nil {
    // Handle error - might fallback to free tier or show error
}

// Check provider status
status := manager.GetStatus()
for name, stat := range status {
    log.Printf("Provider %s: %s", name, stat)
}
```

## 🔄 故障转移逻辑

系统实现自动故障转移：

1. **主要**：尝试层级特定的 Azure 提供商
2. **备用**：使用 OpenAI 官方 GPT-4.1-mini
3. **错误**：如果所有提供商都失败则返回错误

### 故障转移场景

| 场景           | 付费用户            | 免费用户            |
| -------------- | ------------------- | ------------------- |
| 全部健康       | Azure GPT-4.1       | Azure GPT-4.1-mini  |
| Azure 不可用   | OpenAI GPT-4.1-mini | OpenAI GPT-4.1-mini |
| 付费部署不可用 | OpenAI GPT-4.1-mini | Azure GPT-4.1-mini  |
| 免费部署不可用 | Azure GPT-4.1       | OpenAI GPT-4.1-mini |

## 🔍 技术细节

### 生成的 Base URL

系统自动为 Azure AI Foundry 生成 Azure OpenAI base URL：

```
https://{resource-name}.cognitiveservices.azure.com
```

**示例：**

- 资源：`ai-david5226ai452319666352`
- 生成的 URL：`https://ai-david5226ai452319666352.cognitiveservices.azure.com`
- 完整 API 端点：`https://ai-david5226ai452319666352.cognitiveservices.azure.com/openai/deployments/{deployment-name}/chat/completions`

**注意：** 系统支持传统 Azure OpenAI（`openai.azure.com`）和新的 Azure AI Foundry（`cognitiveservices.azure.com`）格式。

### 提供商类型

| 提供商类型       | 使用场景             | 配置                                  |
| ---------------- | -------------------- | ------------------------------------- |
| **OpenAI 官方**  | 官方 OpenAI API      | `OPENAI_PROVIDER=openai`              |
| **Azure OpenAI** | 企业 Azure 部署      | `OPENAI_PROVIDER=azure` + Azure 配置  |
| **多模型**       | 不同用户使用不同模型 | `OPENAI_MULTI_MODEL_ENABLED=true`     |
| **自定义**       | 任何 OpenAI 兼容服务 | `OPENAI_PROVIDER=custom` + 自定义 URL |

## 📊 监控

### 启动日志

```
2025/08/01 16:00:00 Initializing multi-model manager...
2025/08/01 16:00:00 Registered provider azure-paid for tier paid
2025/08/01 16:00:00 Registered provider azure-free for tier free
2025/08/01 16:00:00 Set fallback provider: openai-fallback
2025/08/01 16:00:00 Multi-model manager initialized
```

### 运行时日志

```
2025/08/01 16:00:00 Using fallback provider openai-fallback for tier paid
```

### 健康检查

```go
// 检查所有提供商状态
status := manager.GetStatus()
// 返回: map[string]string{
//   "paid_primary": "healthy",
//   "free_primary": "unhealthy",
//   "fallback": "healthy",
// }
```

## 🧪 测试

运行测试以验证配置：

```bash
cd uniscribe-service
go run cmd/test-multi-model/main.go
```

预期输出：

```
=== Testing Multi-Model Manager ===

--- Provider Status ---
paid_primary: healthy
free_primary: healthy
fallback: healthy

--- Testing Provider Selection ---
✅ Paid tier provider: Azure OpenAI (my-resource/gpt-4)
✅ Free tier provider: Azure OpenAI (my-resource/gpt-4.1-mini)
```

## 💰 成本优化

- **付费用户**：使用 GPT-4 提供优质体验
- **免费用户**：使用 GPT-4.1-mini 实现成本效益
- **故障转移**：防止服务中断
- **Azure**：大量使用时价格更优
- **监控**：按用户层级跟踪使用情况和成本

## 🚨 故障排查

### 常见问题

#### 1. 认证错误

```
Primary provider azure-paid for tier paid failed: error, status code: 401
```

**解决方案：**

- 验证你的 API 密钥是否正确
- 确保密钥有权访问指定的部署
- 检查密钥是否已过期

#### 2. 资源未找到 (404)

```
Primary provider azure-paid for tier paid failed: error, status code: 404
```

**解决方案：**

- 检查资源名称拼写：`OPENAI_AZURE_RESOURCE_NAME`
- 验证资源在你的 Azure 订阅中存在
- 确保部署名称正确：`OPENAI_AZURE_PAID_DEPLOYMENT`

#### 3. 部署未找到

```
Primary provider azure-paid for tier paid failed: deployment not found
```

**解决方案：**

- 检查部署名称拼写
- 确保模型已在 Azure OpenAI Studio 中部署并运行
- 验证部署在同一个资源中

#### 4. 健康检查失败

```
Health check failed for provider Azure OpenAI: invalid character '<' looking for beginning of value
```

**解决方案：**

- Azure OpenAI 可能有不同的端点行为
- 考虑禁用健康检查：`OPENAI_HEALTH_CHECK_ENABLED=false`
- 检查端点是否可从你的网络访问

#### 5. 多模型不工作

```
no available provider for tier paid
```

**解决方案：**

- 确保设置了 `OPENAI_MULTI_MODEL_ENABLED=true`
- 检查 `OPENAI_AZURE_PAID_DEPLOYMENT` 和 `OPENAI_AZURE_FREE_DEPLOYMENT` 都已配置
- 验证备用提供商（OpenAI 官方）是否可访问

### 调试命令

```bash
# 测试配置
OPENAI_MULTI_MODEL_ENABLED=true \
OPENAI_AZURE_RESOURCE_NAME=my-resource \
OPENAI_AZURE_PAID_DEPLOYMENT=uniscribe-gpt-4-1-east-us-2 \
OPENAI_AZURE_FREE_DEPLOYMENT=uniscribe-gpt-4-1-mini-east-us-2 \
OPENAI_HEALTH_CHECK_ENABLED=true \
go run cmd/test-multi-model/main.go

# 检查 Azure 资源
az cognitiveservices account show \
  --name my-openai-resource \
  --resource-group openai-rg

# 列出部署
az cognitiveservices account deployment list \
  --name my-openai-resource \
  --resource-group openai-rg
```

## 💡 最佳实践

1. **使用具体的部署名称**而不是通用名称
2. **在 Azure 中为 API 密钥设置适当的 RBAC**
3. **通过 Azure 门户监控使用情况**
4. **使用健康检查**及早发现问题（但如果引起问题则禁用）
5. **为你的使用场景配置适当的超时**
6. **定期测试故障转移场景**
7. **按用户层级监控成本**
8. **保持备用提供商**始终可用
