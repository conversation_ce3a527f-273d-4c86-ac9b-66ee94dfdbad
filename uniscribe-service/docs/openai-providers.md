# OpenAI Provider System

The OpenAI Provider System allows you to configure different OpenAI-compatible service providers with different base URLs, making it easy to switch between services or use custom endpoints.

## Supported Providers

### Built-in Providers

1. **OpenAI Official** (`openai`)

   - Base URL: `https://api.openai.com/v1`
   - The official OpenAI API (default)

2. **Custom** (`custom`)
   - User-defined base URL
   - For any OpenAI-compatible service

## Configuration

### Environment Variables

- `OPENAI_API_KEY`: Your OpenAI API key (required)
- `OPENAI_PROVIDER`: Provider type (`openai`, `custom`) - defaults to `openai`
- `OPENAI_CUSTOM_BASE_URL`: Custom base URL (required when `OPENAI_PROVIDER=custom`)
- `PROXY_URL`: HTTP proxy URL (optional)

### Examples

#### Using Official OpenAI (default)

```bash
export OPENAI_API_KEY="your-api-key"
export OPENAI_PROVIDER="openai"
```

#### Using UniAIX (via Custom Provider)

```bash
export OPENAI_API_KEY="your-api-key"
export OPENAI_PROVIDER="custom"
export OPENAI_CUSTOM_BASE_URL="https://www.uniaix.com"
```

#### Using Custom Provider

```bash
export OPENAI_API_KEY="your-api-key"
export OPENAI_PROVIDER="custom"
export OPENAI_CUSTOM_BASE_URL="https://your-custom-endpoint.com/v1"
```

#### With Proxy

```bash
export OPENAI_API_KEY="your-api-key"
export OPENAI_PROVIDER="openai"
export PROXY_URL="http://127.0.0.1:7890"
```

## Usage

### Basic Usage (Backward Compatible)

```go
import openaipkg "uniscribe-service/pkg/openai"

// Get the default client (backward compatible)
client := openaipkg.GetClient()

// Use the client as before
resp, err := client.CreateChatCompletion(ctx, request)
```

### Advanced Usage with Providers

```go
import (
    openaipkg "uniscribe-service/pkg/openai"
    "uniscribe-service/pkg/openai/providers"
)

// Get the default provider
defaultProvider := openaipkg.GetDefaultProvider()
resp, err := defaultProvider.CreateChatCompletion(ctx, request)

// Get a specific provider
service := openaipkg.GetService()
openaiProvider, err := service.GetProvider(providers.OpenAIOfficial)
if err == nil {
    resp, err := openaiProvider.CreateChatCompletion(ctx, request)
}

// Create a custom provider programmatically
customProvider, err := providers.NewCustomProvider(
    "My Custom Provider",
    "your-api-key",
    "https://your-endpoint.com/v1",
    "http://proxy:8080", // optional proxy
)
```

## Architecture

The provider system consists of:

1. **Provider Interface**: Defines the contract for all providers
2. **Base Provider**: Common functionality for HTTP client setup, proxy configuration
3. **Concrete Providers**: Specific implementations for each service
4. **Provider Service**: Manages multiple providers and default selection
5. **Factory**: Creates providers based on configuration

## Migration Guide

### From Old System

The old hardcoded system:

```go
// Old way - hardcoded base URL
client := openaipkg.GetClient()
```

New flexible system:

```go
// New way - configurable provider
provider := openaipkg.GetDefaultProvider()
client := provider.GetClient()

// Or use the backward-compatible method
client := openaipkg.GetClient() // Still works!
```

### Benefits

1. **Flexibility**: Easy to switch between different OpenAI-compatible services
2. **Configuration**: Environment-based configuration without code changes
3. **Extensibility**: Easy to add new providers
4. **Backward Compatibility**: Existing code continues to work
5. **Proxy Support**: Built-in proxy configuration for all providers
6. **Testing**: Easy to mock different providers for testing

## Testing

Run the provider system test:

```bash
cd uniscribe-service
go run cmd/test-providers/main.go
```

This will test:

- Provider registration and initialization
- Backward compatibility
- Different provider types
- Configuration loading
