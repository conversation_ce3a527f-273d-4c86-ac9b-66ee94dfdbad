# Environment Detection for Go Service

## Overview

The Go service now supports environment detection similar to the Python backend, allowing conditional initialization of services like Sentry based on the deployment environment.

## Environment Configuration

### Environment Variable

The service uses the `APP_SETTINGS` environment variable to determine the current environment:

- `production` - Production environment
- `development` - Development environment (default)
- `testing` - Testing environment

If `APP_SETTINGS` is not set, it defaults to `development`.

### Usage

```go
import "uniscribe-service/internal/config"

// Check environment
if config.IsProductionEnv() {
    // Production-only code
}

if config.IsDevelopmentEnv() {
    // Development-only code
}

if config.IsTestingEnv() {
    // Testing-only code
}

// Get environment string
env := config.Cfg.Environment
```

## Sentry Integration

### Conditional Initialization

Sentry is now only initialized in production environments:

```go
// In main.go
if config.IsProductionEnv() {
    err := sentry.Init(sentry.ClientOptions{
        Dsn:              "...",
        TracesSampleRate: 1.0,
    })
    if err != nil {
        log.Fatalf("sentry.Init: %s", err)
    }
    log.Println("Sentry initialized for production environment")
} else {
    log.Printf("Sentry disabled in %s environment", config.Cfg.Environment)
}
```

### Centralized Sentry Package

A centralized sentry utility package (`internal/sentry`) provides conditional Sentry operations:

```go
import sentryutil "uniscribe-service/internal/sentry"

// These functions only execute in production
sentryutil.CaptureException(err)
sentryutil.CaptureMessage("message")
sentryutil.WithScope(func(scope *sentry.Scope) {
    scope.SetExtra("key", "value")
    sentryutil.CaptureException(err)
})
sentryutil.AddBreadcrumb(&sentry.Breadcrumb{
    Message: "breadcrumb",
    Level:   sentry.LevelInfo,
})
sentryutil.Flush(2 * time.Second)
```

## Migration Guide

### For Existing Code Using Sentry

Replace direct sentry calls with the centralized package:

**Before:**

```go
import "github.com/getsentry/sentry-go"

sentry.CaptureException(err)
sentry.WithScope(func(scope *sentry.Scope) {
    sentry.CaptureException(err)
})
```

**After:**

```go
import sentryutil "uniscribe-service/internal/sentry"

sentryutil.CaptureException(err)
sentryutil.WithScope(func(scope *sentry.Scope) {
    sentryutil.CaptureException(err)
})
```

### Files Updated

The following files have been updated to use the centralized sentry package:

1. **cmd/uniscribe-service/main.go**

   - Added conditional Sentry initialization
   - Updated error capture calls to use helper function

2. **internal/httpclient/retry_client.go**

   - Replaced `sentry.WithScope` and `sentry.CaptureException` calls

3. **internal/queue/redis_consumer.go**

   - Updated all Sentry error reporting calls

4. **internal/service/speech/stt/service.go**

   - Replaced `sentry.CaptureMessage` and `sentry.CaptureException` calls

5. **internal/handlers/audio_processor.go**

   - Updated error tracking in audio processing

6. **internal/handlers/speaker_diarization_processor.go**

   - Updated error tracking in speaker diarization

7. **internal/handlers/text_processor.go**

   - Updated error reporting in text processing

8. **internal/handlers/result_sender.go**
   - Updated HTTP error reporting

### Benefits

1. **Conditional Execution**: Sentry operations only run in production
2. **Centralized Logic**: Environment checks are handled in one place
3. **Consistent Behavior**: All Sentry usage follows the same pattern
4. **Easy Testing**: No Sentry noise in development/testing environments

## Testing

Run the environment detection demo:

```bash
# Test development environment (default)
go run examples/env_demo.go

# Test production environment
APP_SETTINGS=production go run examples/env_demo.go

# Test testing environment
APP_SETTINGS=testing go run examples/env_demo.go
```

Run the unit tests:

```bash
go test ./internal/config -v
```

## Deployment

### Development

- Set `APP_SETTINGS=development` or leave unset
- Sentry will be disabled

### Production

- Set `APP_SETTINGS=production`
- Sentry will be initialized and capture exceptions

### Testing

- Set `APP_SETTINGS=testing`
- Sentry will be disabled
