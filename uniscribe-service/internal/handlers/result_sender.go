package handlers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"uniscribe-service/internal/constants"
	"uniscribe-service/internal/httpclient"
	"uniscribe-service/internal/service/speech/stt/providers"

	sentryutil "uniscribe-service/internal/sentry"

	"github.com/getsentry/sentry-go"
)

// TaskStatusUpdatePayload 任务状态更新载荷
type TaskStatusUpdatePayload struct {
	TaskID int64                  `json:"taskId"`
	Status constants.TaskStatus   `json:"status"`
	Error  string                 `json:"error,omitempty"`
}

func (r TaskStatusUpdatePayload) Validate() error {
	if r.TaskID == 0 {
		return fmt.Errorf("task_id is required")
	}
	if r.Status == 0 {
		return fmt.Errorf("status is required")
	}
	return nil
}

// TranscriptionResultPayload
type TranscriptionResultPayload struct {
	TaskID           int64                      `json:"taskId"`
	TaskType         constants.TaskType         `json:"taskType"`
	Text             string                     `json:"transcriptionText"`
	Duration         float64                    `json:"duration"`
	DetectedLanguage string                     `json:"detectedLanguage"`
	Segments         []providers.UnifiedSegment `json:"segments"`
	ServiceProvider  string                     `json:"serviceProvider"`
}

func (r TranscriptionResultPayload) Validate() error {
	if r.TaskID == 0 {
		return fmt.Errorf("task_id is required")
	}
	if r.TaskType != constants.TaskTypeTranscription {
		return fmt.Errorf("task_type must be transcription")
	}
	if r.Text == "" {
		return fmt.Errorf("The transcript is empty")
	}
	return nil
}



// SummaryResultPayload
type SummaryResultPayload struct {
	TaskID          int64              `json:"taskId"`
	TaskType        constants.TaskType `json:"taskType"`
	Summary         string             `json:"summary"`
	ServiceProvider string             `json:"serviceProvider"`
}

func (r SummaryResultPayload) Validate() error {
	if r.TaskID == 0 {
		return fmt.Errorf("task_id is required")
	}
	if r.TaskType != constants.TaskTypeSummary {
		return fmt.Errorf("task_type must be summary")
	}
	if r.Summary == "" {
		return fmt.Errorf("summary is required")
	}
	return nil
}



// OutlineResultPayload
type OutlineResultPayload struct {
	TaskID          int64              `json:"taskId"`
	TaskType        constants.TaskType `json:"taskType"`
	Outline         string             `json:"outline"`
	ServiceProvider string             `json:"serviceProvider"`
}

func (r OutlineResultPayload) Validate() error {
	if r.TaskID == 0 {
		return fmt.Errorf("task_id is required")
	}
	if r.TaskType != constants.TaskTypeOutline {
		return fmt.Errorf("task_type must be outline")
	}
	if r.Outline == "" {
		return fmt.Errorf("outline is required")
	}
	return nil
}

type QAResultPayload struct {
	TaskID          int64              `json:"taskId"`
	TaskType        constants.TaskType `json:"taskType"`
	QAExtraction    []QAPair           `json:"qaExtraction"`
	ServiceProvider string             `json:"serviceProvider"`
}

func (r QAResultPayload) Validate() error {
	if r.TaskID == 0 {
		return fmt.Errorf("task_id is required")
	}
	if r.TaskType != constants.TaskTypeQAExtraction {
		return fmt.Errorf("task_type must be qa")
	}
	if len(r.QAExtraction) == 0 {
		return fmt.Errorf("qa_extraction is required")
	}
	return nil
}

// SpeakerDiarizationResultPayload 说话人识别结果
type SpeakerDiarizationResultPayload struct {
	TaskID              int64              `json:"taskId"`
	TaskType            constants.TaskType `json:"taskType"`
	DiarizationSegments interface{}        `json:"diarizationSegments"`
}

func (r SpeakerDiarizationResultPayload) Validate() error {
	if r.TaskID == 0 {
		return fmt.Errorf("task_id is required")
	}
	if r.TaskType != constants.TaskTypeSpeakerDiarization {
		return fmt.Errorf("task_type must be speaker_diarization")
	}
	if r.DiarizationSegments == nil {
		return fmt.Errorf("diarization_segments is required")
	}
	return nil
}

// 任何任务类型失败，都可以用这个
type TaskFailedPyload struct {
	TaskID       int64              `json:"taskId"`
	TaskType     constants.TaskType `json:"taskType"`
	ErrorMessage string             `json:"errorMessage"`
}

func (r TaskFailedPyload) Validate() error {
	if r.TaskID == 0 {
		return fmt.Errorf("task_id is required")
	}
	if r.ErrorMessage == "" {
		return fmt.Errorf("error_message is required")
	}
	return nil
}

type ResultPayload interface {
	Validate() error
}

type ResultSender interface {
	Send(result ResultPayload) error
	UpdateTaskStatus(taskID int64, status constants.TaskStatus, errorMsg ...string) error
}

type HTTPResultSender struct {
	retryClient *httpclient.RetryClient
	host        string
}

func NewHTTPResultSender(host string) *HTTPResultSender {
	return &HTTPResultSender{
		host:        host,
		retryClient: httpclient.NewWebBackendClient(),
	}
}

// sendJSONRequest sends a JSON POST request to the specified endpoint with retry mechanism
func (s *HTTPResultSender) sendJSONRequest(endpoint string, payload interface{}, operation string) error {
	url := s.host + endpoint

	jsonData, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("failed to marshal payload: %v", err)
	}

	resp, err := s.retryClient.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		// Add additional context for Sentry (the retry client already handles basic Sentry reporting)
		sentryutil.WithScope(func(scope *sentry.Scope) {
			scope.SetTag("component", "result_sender")
			scope.SetTag("operation", operation)
			scope.SetExtra("payload_type", fmt.Sprintf("%T", payload))
			sentryutil.CaptureException(err)
		})
		return fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, readErr := io.ReadAll(resp.Body)
		if readErr != nil {
			return fmt.Errorf("failed to read response body: %v", readErr)
		}

		httpErr := fmt.Errorf("unexpected status code: %d, %s", resp.StatusCode, body)
		sentryutil.WithScope(func(scope *sentry.Scope) {
			scope.SetTag("component", "result_sender")
			scope.SetTag("operation", operation)
			scope.SetExtra("url", url)
			scope.SetExtra("status_code", resp.StatusCode)
			scope.SetExtra("response_body", string(body))
			scope.SetExtra("payload_type", fmt.Sprintf("%T", payload))
			sentryutil.CaptureException(httpErr)
		})
		return httpErr
	}

	return nil
}

func (s *HTTPResultSender) Send(result ResultPayload) error {
	if err := result.Validate(); err != nil {
		return fmt.Errorf("validation failed: %v", err)
	}

	return s.sendJSONRequest("/tasks/result", result, "send_result")
}

// UpdateTaskStatus 更新任务状态
func (s *HTTPResultSender) UpdateTaskStatus(taskID int64, status constants.TaskStatus, errorMsg ...string) error {
	payload := TaskStatusUpdatePayload{
		TaskID: taskID,
		Status: status,
	}

	// 如果有错误信息，添加到载荷中
	if len(errorMsg) > 0 && errorMsg[0] != "" {
		payload.Error = errorMsg[0]
	}

	if err := payload.Validate(); err != nil {
		return fmt.Errorf("invalid payload: %v", err)
	}

	return s.sendJSONRequest("/tasks/status", payload, "update_status")
}
