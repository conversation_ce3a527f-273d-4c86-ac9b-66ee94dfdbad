package handlers

import (
	"context"
	"fmt"
	"log"
	"math"
	"strings"
	"time"

	"github.com/getsentry/sentry-go"
	"github.com/sashabaranov/go-openai"
	"github.com/sashabaranov/go-openai/jsonschema"

	"uniscribe-service/internal/constants"
	sentryutil "uniscribe-service/internal/sentry"
	openaipkg "uniscribe-service/pkg/openai"
)

const GPTModel = openai.GPT4Dot1Mini



// getUserTierFromTask 根据任务的 Priority 字段确定用户层级
func getUserTierFromTask(task Task) openaipkg.UserTier {
	if task.Priority == 1 {
		return openaipkg.TierPaid // 付费用户
	}
	return openaipkg.TierFree // 免费用户（默认）
}

// getServiceProviderName 根据实际使用的 provider 名称确定 service provider
func getServiceProviderName(providerName string, model string) string {
	log.Printf("providerName: %s, model: %s", providerName, model)
	// 根据实际使用的 provider 名称来确定前缀
	if strings.Contains(providerName, "azure") {
		// 对于 Azure，直接使用 Azure 返回的实际模型名称
		// 不进行任何映射，保持模型名称的准确性
		return "azure/" + model
	}

	// 默认认为是 OpenAI 官方
	return "openai/" + model
}


// TextProcessorResult 包含处理结果和使用的 service provider
type TextProcessorResult struct {
	Result          interface{}
	ServiceProvider string
}


// TextProcessor 接口定义了支持用户层级的文本处理器
type TextProcessor interface {
	ProcessWithTier(ctx context.Context, text string, tier openaipkg.UserTier, language string) (*TextProcessorResult, error)
}

// SummaryResult 存储文本摘要结果
type SummaryResult struct {
	Explanation string `json:"explanation"`
	Output      string `json:"output"`
}

// OutlineResult 存储大纲生成结果
type OutlineResult struct {
	Explanation string `json:"explanation"`
	Output      string `json:"output"`
}

type QAResult struct {
	Explanation string   `json:"explanation"`
	Output      []QAPair `json:"output"`
}

type QAPair struct {
	Question string `json:"question"`
	Answer   string `json:"answer"`
}

// SummaryProcessor 实现了文本摘要
type SummaryProcessor struct{}

func (p *SummaryProcessor) ProcessWithTier(ctx context.Context, text string, tier openaipkg.UserTier, language string) (*TextProcessorResult, error) {
	schema, err := jsonschema.GenerateSchemaForType(SummaryResult{})
	if err != nil {
		return nil, fmt.Errorf("failed to generate schema: %w", err)
	}

	// 强化语言一致性的摘要提示词 - 直接指定语言
	systemPrompt := fmt.Sprintf(`You are a text processing assistant.

CRITICAL RULE: The summary MUST be written in %s. Never translate into another language under any circumstances.

PROCESS:
1. The input text language is: %s
2. Write the summary in %s only
3. Maintain names, numbers, and technical terms exactly as in the input

SUMMARY REQUIREMENTS:
- Concise but informative
- Balanced coverage of main points
- Maintain the original tone and context
- Never add content not present in the input
- Never translate or rephrase into another language`, language, language, language)

	// 使用统一接口，自动处理多模型和单一模型的逻辑
	completionResult, err := openaipkg.CreateChatCompletionWithProviderInfo(ctx, tier, openai.ChatCompletionRequest{
		Model:       GPTModel, // 这个会被 provider 忽略，因为 Azure 使用部署名
		Temperature: 0.1,
		Messages: []openai.ChatCompletionMessage{
			{
				Role:    openai.ChatMessageRoleSystem,
				Content: systemPrompt,
			},
			{
				Role:    openai.ChatMessageRoleUser,
				Content: fmt.Sprintf("Please summarize this text:\n%s", text),
			},
		},
		ResponseFormat: &openai.ChatCompletionResponseFormat{
			Type: openai.ChatCompletionResponseFormatTypeJSONSchema,
			JSONSchema: &openai.ChatCompletionResponseFormatJSONSchema{
				Name:   "summary_result",
				Schema: schema,
			},
		},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create chat completion: %w", err)
	}

	var result SummaryResult
	err = schema.Unmarshal(completionResult.Response.Choices[0].Message.Content, &result)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal schema: %w", err)
	}

	return &TextProcessorResult{
		Result:          result,
		ServiceProvider: getServiceProviderName(completionResult.ProviderName, completionResult.Response.Model),
	}, nil
}

// OutlineProcessor 实现了大纲生成
type OutlineProcessor struct{}

func (p *OutlineProcessor) ProcessWithTier(ctx context.Context, text string, tier openaipkg.UserTier, language string) (*TextProcessorResult, error) {
	schema, err := jsonschema.GenerateSchemaForType(OutlineResult{})
	if err != nil {
		return nil, fmt.Errorf("failed to generate schema: %w", err)
	}

	// 修改系统提示词，明确指定 markdown 格式要求和语言
	systemPrompt := fmt.Sprintf(`You are a text processing assistant.

CRITICAL RULE: The outline MUST be written in %s. Never translate into another language under any circumstances.

PROCESS:
1. The input text language is: %s
2. Write the outline in %s only
3. Maintain names, numbers, and technical terms exactly as in the input

Your task is to generate an outline in markdown format.
Follow these strict formatting rules:
1. Use only '#', '##', '###' for headings (no A., B., 1., 2.)
2. Each level should start with the appropriate number of '#'
3. Keep the structure simple and clear
4. Leave a blank line between each heading
Example format:
# Main Topic

## Subtopic 1

### Detail Point 1

### Detail Point 2

## Subtopic 2

### Detail Point 3`, language, language, language)

	// 使用统一接口，自动处理多模型和单一模型的逻辑
	completionResult, err := openaipkg.CreateChatCompletionWithProviderInfo(ctx, tier, openai.ChatCompletionRequest{
		Model:       GPTModel,
		Temperature: 0.2,
		Messages: []openai.ChatCompletionMessage{
			{
				Role:    openai.ChatMessageRoleSystem,
				Content: systemPrompt,
			},
			{
				Role:    openai.ChatMessageRoleUser,
				Content: fmt.Sprintf("Please generate an outline in the original language of the text, strictly following the markdown format:\n%s", text),
			},
		},
		ResponseFormat: &openai.ChatCompletionResponseFormat{
			Type: openai.ChatCompletionResponseFormatTypeJSONSchema,
			JSONSchema: &openai.ChatCompletionResponseFormatJSONSchema{
				Name:   "outline_result",
				Schema: schema,
			},
		},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create chat completion: %w", err)
	}

	var result OutlineResult
	err = schema.Unmarshal(completionResult.Response.Choices[0].Message.Content, &result)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal schema: %w", err)
	}

	return &TextProcessorResult{
		Result:          result,
		ServiceProvider: getServiceProviderName(completionResult.ProviderName, completionResult.Response.Model),
	}, nil
}

// QAExtractionProcessor
type QAExtractionProcessor struct{}

func (p *QAExtractionProcessor) ProcessWithTier(ctx context.Context, text string, tier openaipkg.UserTier, language string) (*TextProcessorResult, error) {
	schema, err := jsonschema.GenerateSchemaForType(QAResult{})
	if err != nil {
		return nil, fmt.Errorf("failed to generate schema: %w", err)
	}

	// 系统提示词，指定语言
	systemPrompt := fmt.Sprintf(`You are a text processing assistant.

CRITICAL RULE: The Q&A MUST be written in %s. Never translate into another language under any circumstances.

PROCESS:
1. The input text language is: %s
2. Write the Q&A in %s only
3. Maintain names, numbers, and technical terms exactly as in the input

Your task is: Generate Q&A`, language, language, language)

	// 使用统一接口，自动处理多模型和单一模型的逻辑
	completionResult, err := openaipkg.CreateChatCompletionWithProviderInfo(ctx, tier, openai.ChatCompletionRequest{
		Model:       GPTModel, // 这个会被 provider 忽略，因为 Azure 使用部署名
		Temperature: 0.2,
		Messages: []openai.ChatCompletionMessage{
			{
				Role:    openai.ChatMessageRoleSystem,
				Content: systemPrompt,
			},
			{
				Role:    openai.ChatMessageRoleUser,
				Content: fmt.Sprintf("Please generate Q&A:\n%s", text),
			},
		},
		ResponseFormat: &openai.ChatCompletionResponseFormat{
			Type: openai.ChatCompletionResponseFormatTypeJSONSchema,
			JSONSchema: &openai.ChatCompletionResponseFormatJSONSchema{
				Name:   "qa_result",
				Schema: schema,
			},
		},
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create chat completion: %w", err)
	}

	var result QAResult
	err = schema.Unmarshal(completionResult.Response.Choices[0].Message.Content, &result)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal schema: %w", err)
	}

	return &TextProcessorResult{
		Result:          result,
		ServiceProvider: getServiceProviderName(completionResult.ProviderName, completionResult.Response.Model),
	}, nil
}

// ProcessTextTask 函数并行执行所有文本处理任务

type TextTaskProcessor interface {
	Process(ctx context.Context, task Task) error
}

type TextTaskProcessorImpl struct {
	resultSender ResultSender
	retryCount   int
	baseDelay    time.Duration
}

func NewTextTaskProcessor(resultSender ResultSender) TextTaskProcessor {
	return &TextTaskProcessorImpl{resultSender: resultSender, retryCount: 3, baseDelay: 3 * time.Second}
}

func (p *TextTaskProcessorImpl) Process(ctx context.Context, task Task) error {
	log.Printf("receive text task %v\n", task)

	// 1. 检查转录文本是否为空
	if strings.TrimSpace(task.TranscriptionText) == "" {
		err := fmt.Errorf("transcription text is empty for task %d", task.TaskID)
		log.Printf("Task validation failed: %v", err)

		// 更新状态为失败 - 使用 UpdateTaskStatus 上报错误
		statusErr := p.resultSender.UpdateTaskStatus(task.TaskID, constants.TaskStatusFailed, err.Error())
		if statusErr != nil {
			log.Printf("Failed to update task status to failed: %v", statusErr)
			return fmt.Errorf("failed to update task status: %w", statusErr)
		}
		return nil
	}

	// 2. 更新任务状态为处理中
	err := p.resultSender.UpdateTaskStatus(task.TaskID, constants.TaskStatusProcessing)
	if err != nil {
		log.Printf("Failed to update task status to processing: %v", err)
		// 不因为状态更新失败而终止任务处理
	}

	// 3. 确定用户层级
	userTier := getUserTierFromTask(task)
	log.Printf("Processing task %d with user tier: %s", task.TaskID, userTier)

	// 4. 选择处理器并处理任务
	var result *TextProcessorResult
	switch task.TaskType {
	case constants.TaskTypeSummary:
		processor := &SummaryProcessor{}
		result, err = p.retryTaskWithTier(ctx, task, processor, userTier)
	case constants.TaskTypeOutline:
		processor := &OutlineProcessor{}
		result, err = p.retryTaskWithTier(ctx, task, processor, userTier)
	case constants.TaskTypeQAExtraction:
		processor := &QAExtractionProcessor{}
		result, err = p.retryTaskWithTier(ctx, task, processor, userTier)
	default:
		err := fmt.Errorf("unknown task type: %v", task.TaskType)
		log.Printf("Task type validation failed: %v", err)

		// 更新状态为失败 - 使用 UpdateTaskStatus 上报错误
		statusErr := p.resultSender.UpdateTaskStatus(task.TaskID, constants.TaskStatusFailed, err.Error())
		if statusErr != nil {
			log.Printf("Failed to update task status to failed: %v", statusErr)
			return fmt.Errorf("failed to update task status: %w", statusErr)
		}
		return nil
	}
	if err != nil {
		// 上报错误到 Sentry
		sentryutil.WithScope(func(scope *sentry.Scope) {
			scope.SetTag("component", "text_processor")
			scope.SetTag("task_type", fmt.Sprintf("%d", task.TaskType))
			scope.SetExtra("task_id", task.TaskID)
			scope.SetExtra("transcription_text_length", len(task.TranscriptionText))
			sentryutil.CaptureException(err)
		})

		// 更新状态为失败 - 使用 UpdateTaskStatus 上报错误
		statusErr := p.resultSender.UpdateTaskStatus(task.TaskID, constants.TaskStatusFailed, err.Error())
		if statusErr != nil {
			log.Printf("Failed to update task status to failed: %v", statusErr)
			return fmt.Errorf("failed to update task status: %w", statusErr)
		}
		return nil
	}

	// 5. 发送成功结果
	switch task.TaskType {
	case constants.TaskTypeSummary:
		summaryResult := result.Result.(SummaryResult)
		err = p.resultSender.Send(&SummaryResultPayload{
			TaskID:          task.TaskID,
			TaskType:        task.TaskType,
			Summary:         summaryResult.Output,
			ServiceProvider: result.ServiceProvider,
		})
	case constants.TaskTypeOutline:
		outlineResult := result.Result.(OutlineResult)
		err = p.resultSender.Send(&OutlineResultPayload{
			TaskID:          task.TaskID,
			TaskType:        task.TaskType,
			Outline:         outlineResult.Output,
			ServiceProvider: result.ServiceProvider,
		})
	case constants.TaskTypeQAExtraction:
		qaResult := result.Result.(QAResult)
		err = p.resultSender.Send(&QAResultPayload{
			TaskID:          task.TaskID,
			TaskType:        task.TaskType,
			QAExtraction:    qaResult.Output,
			ServiceProvider: result.ServiceProvider,
		})
	}

	if err != nil {
		log.Printf("Failed to send task result: %v", err)

		// 发送结果失败时，更新状态为失败 - 使用 UpdateTaskStatus 上报错误
		statusErr := p.resultSender.UpdateTaskStatus(task.TaskID, constants.TaskStatusFailed, err.Error())
		if statusErr != nil {
			log.Printf("Failed to update task status to failed: %v", statusErr)
			return fmt.Errorf("failed to update task status: %w", statusErr)
		}
		return nil
	}

	return nil
}

func (p *TextTaskProcessorImpl) retryTaskWithTier(ctx context.Context, task Task, processor TextProcessor, tier openaipkg.UserTier) (*TextProcessorResult, error) {
	var result *TextProcessorResult
	var err error

	for attempt := 0; attempt < p.retryCount; attempt++ {
		log.Printf("Processing task %d, language: %s, attempt %d", task.TaskID, task.Language, attempt)
		result, err = processor.ProcessWithTier(ctx, task.TranscriptionText, tier, task.Language)
		if err == nil {
			return result, nil
		}
		// 指数退避
		sleepTime := p.baseDelay * time.Duration(math.Pow(2, float64(attempt)))
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case <-time.After(sleepTime):
		}
	}
	return nil, err
}
