package handlers

import (
	"fmt"

	"uniscribe-service/internal/constants"
)

type Task struct {
	TaskID                   int64              `json:"taskId"`
	FileUrl                  string             `json:"fileUrl"`
	TaskType                 constants.TaskType `json:"taskType"`
	Priority                 int                `json:"priority"`                        // 0=免费用户，1=付费用户
	TranscriptionFileId      int64              `json:"transcriptionFileId"`
	TranscriptionText        string             `json:"transcriptionText"`
	FileDuration             float64            `json:"fileDuration"`
	Language                 string             `json:"language"`
	LanguageCode             string             `json:"languageCode"`
	TranscriptionType        string             `json:"transcriptionType,omitempty"`
	RequestedServiceProvider string             `json:"requestedServiceProvider,omitempty"`
}

// String 实现 fmt.Stringer 接口，返回适合日志输出的简化字符串，截断长文本字段
func (t Task) String() string {
	// 截断长文本字段，只显示长度和前面一部分
	transcriptionPreview := ""
	if len(t.TranscriptionText) > 0 {
		if len(t.TranscriptionText) <= 100 {
			transcriptionPreview = t.TranscriptionText
		} else {
			transcriptionPreview = t.TranscriptionText[:97] + "..."
		}
	}

	return fmt.Sprintf("Task{ID:%d, Type:%v, Priority:%d, FileID:%d, Duration:%.1fs, Lang:%s/%s, "+
		"Provider:%s, TranscriptionType:%s, TranscriptionText:[%d chars]%q}",
		t.TaskID, t.TaskType, t.Priority, t.TranscriptionFileId, t.FileDuration,
		t.Language, t.LanguageCode, t.RequestedServiceProvider, t.TranscriptionType,
		len(t.TranscriptionText), transcriptionPreview)
}




