package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"

	"uniscribe-service/internal/constants"
	replicatepkg "uniscribe-service/pkg/replicate"

	sentryutil "uniscribe-service/internal/sentry"

	"github.com/getsentry/sentry-go"
	"github.com/replicate/replicate-go"
)

// SpeakerDiarizationProcessor 接口定义了说话人识别处理器应该实现的方法
type SpeakerDiarizationProcessor interface {
	Process(ctx context.Context, task Task) error
}

// SpeakerDiarizationProcessorImpl 实现了 SpeakerDiarizationProcessor 接口
type SpeakerDiarizationProcessorImpl struct {
	client       *replicate.Client
	resultSender ResultSender

	// 主备模式配置
	primaryModel string
	backupModels []string
}

// SpeakerDiarizationModel 定义说话人识别模型配置
type SpeakerDiarizationModel struct {
	Model   string
	Version string
}

// 支持的说话人识别模型
var supportedSpeakerDiarizationModels = map[string]SpeakerDiarizationModel{
	"meronym": {
		Model:   "meronym/speaker-diarization",
		Version: "64b78c82f74d78164b49178443c819445f5dca2c51c8ec374783d49382342119",
	},
	"lucataco": {
		Model:   "lucataco/speaker-diarization",
		Version: "718182bfdc7c91943c69ed0ac18ebe99a76fdde67ccd01fced347d8c3b8c15a6",
	},
}

// SpeakerDiarizationResult 表示说话人识别的结果
type SpeakerDiarizationResult struct {
	Segments []SpeakerSegment `json:"segments"`
	Speakers SpeakerInfo      `json:"speakers"`
}

// SpeakerSegment 表示单个说话人段
type SpeakerSegment struct {
	Speaker string `json:"speaker"`
	Start   string `json:"start"`
	Stop    string `json:"stop"`
}

// SpeakerInfo 表示说话人信息
type SpeakerInfo struct {
	Count      int                  `json:"count"`
	Labels     []string             `json:"labels"`
	Embeddings map[string][]float64 `json:"embeddings"`
}

func NewSpeakerDiarizationProcessor(resultSender ResultSender) SpeakerDiarizationProcessor {
	return &SpeakerDiarizationProcessorImpl{
		client:       replicatepkg.GetClient(),
		resultSender: resultSender,
		// 设置主备模式
		primaryModel: "meronym",
		backupModels: []string{"lucataco"},
	}
}

func (p *SpeakerDiarizationProcessorImpl) Process(ctx context.Context, task Task) error {
	// 1. 更新任务状态为处理中
	err := p.resultSender.UpdateTaskStatus(task.TaskID, constants.TaskStatusProcessing)
	if err != nil {
		log.Printf("Failed to update task status to processing: %v", err)
		// 不因为状态更新失败而终止任务处理
	}

	// 2. 处理说话人识别
	payload, err := p.processSpeakerDiarization(ctx, task)
	if err != nil {
		return p.sendTaskFailedResult(task, err)
	}

	// 3. 发送结果（这会将状态设置为 completed）
	err = p.resultSender.Send(payload)
	if err != nil {
		return p.sendTaskFailedResult(task, err)
	}

	return nil
}

func (p *SpeakerDiarizationProcessorImpl) processSpeakerDiarization(ctx context.Context, task Task) (*SpeakerDiarizationResultPayload, error) {
	// 使用主备模式进行说话人识别
	result, usedModel, err := p.processSpeakerDiarizationWithFailover(ctx, task)
	if err != nil {
		return nil, err
	}

	payload := &SpeakerDiarizationResultPayload{
		TaskID:              task.TaskID,
		TaskType:            constants.TaskTypeSpeakerDiarization,
		DiarizationSegments: result,
	}

	fmt.Printf("Speaker diarization completed successfully using model: %s\n", usedModel)
	return payload, nil
}

// processSpeakerDiarizationWithFailover 使用主备模式进行说话人识别
func (p *SpeakerDiarizationProcessorImpl) processSpeakerDiarizationWithFailover(ctx context.Context, task Task) (*SpeakerDiarizationResult, string, error) {
	// 首先尝试主模型
	fmt.Printf("Attempting speaker diarization with primary model: %s\n", p.primaryModel)
	result, err := p.trySpeakerDiarization(ctx, p.primaryModel, task)
	if err == nil {
		fmt.Printf("Primary model %s succeeded\n", p.primaryModel)
		return result, p.primaryModel, nil
	}

	// 主模型失败，记录错误并尝试备用模型
	firstErr := fmt.Errorf("primary model %s failed: %w", p.primaryModel, err)
	fmt.Printf("Primary model %s failed: %v\n", p.primaryModel, err)
	sentryutil.CaptureException(firstErr)

	// 依次尝试备用模型
	for _, model := range p.backupModels {
		fmt.Printf("Attempting speaker diarization with backup model: %s\n", model)
		result, err := p.trySpeakerDiarization(ctx, model, task)
		if err == nil {
			fmt.Printf("Backup model %s succeeded\n", model)
			return result, model, nil
		}
		fmt.Printf("Backup model %s failed: %v\n", model, err)
	}

	return nil, "", fmt.Errorf("all speaker diarization models failed, first error: %w", firstErr)
}

// trySpeakerDiarization 尝试使用指定模型进行说话人识别
func (p *SpeakerDiarizationProcessorImpl) trySpeakerDiarization(ctx context.Context, modelName string, task Task) (*SpeakerDiarizationResult, error) {
	modelConfig, exists := supportedSpeakerDiarizationModels[modelName]
	if !exists {
		return nil, fmt.Errorf("unsupported speaker diarization model: %s", modelName)
	}

	// 构建输入参数
	input := replicate.PredictionInput{
		"audio": task.FileUrl,
	}

	// 调用 Replicate API
	prediction, err := p.client.Run(ctx, modelConfig.Model+":"+modelConfig.Version, input, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to call replicate API: %w", err)
	}

	// 解析结果
	result, err := p.parseReplicateOutput(prediction)
	if err != nil {
		return nil, fmt.Errorf("failed to parse replicate output: %w", err)
	}

	return result, nil
}

func (p *SpeakerDiarizationProcessorImpl) parseReplicateOutput(predictionOutput replicate.PredictionOutput) (*SpeakerDiarizationResult, error) {
	// 将 predictionOutput 转换为 JSON 再解析为结构体
	jsonData, err := json.Marshal(predictionOutput)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal prediction output: %w", err)
	}

	// 首先尝试解析为字符串
	var outputStr string
	if err := json.Unmarshal(jsonData, &outputStr); err == nil {
		// 检查是否是 URL
		if p.isURL(outputStr) {
			fmt.Printf("Output is a URL, downloading: %s\n", outputStr)
			return p.downloadAndParseJSON(outputStr)
		}

		// 尝试解析为 JSON 字符串
		var result SpeakerDiarizationResult
		if err := json.Unmarshal([]byte(outputStr), &result); err == nil {
			fmt.Printf("Successfully parsed as JSON string\n")
			return &result, nil
		} else {
			fmt.Printf("Failed to parse JSON string: %v\n", err)
		}
	}

	// 如果所有方式都失败，返回详细错误信息
	return nil, fmt.Errorf("failed to parse replicate output. Expected JSON string or URL. Raw data: %s", string(jsonData))
}

func (p *SpeakerDiarizationProcessorImpl) sendTaskFailedResult(task Task, err error) error {
	// 1. 添加错误跟踪
	sentryutil.WithScope(func(scope *sentry.Scope) {
		scope.SetExtra("taskID", task.TaskID)
		scope.SetExtra("taskType", "speaker_diarization")
		scope.AddBreadcrumb(&sentry.Breadcrumb{
			Message: err.Error(),
			Level:   sentry.LevelError,
		}, 0)
		sentryutil.CaptureException(err)
	})

	// 2. 更新任务状态为失败（这会自动触发文件状态更新和 webhook 通知）
	statusErr := p.resultSender.UpdateTaskStatus(task.TaskID, constants.TaskStatusFailed, err.Error())
	if statusErr != nil {
		log.Printf("Failed to update task status to failed: %v", statusErr)
		return fmt.Errorf("failed to update task status: %w", statusErr)
	}

	return nil
}

// isURL 检查字符串是否是有效的 URL
func (p *SpeakerDiarizationProcessorImpl) isURL(str string) bool {
	return len(str) > 7 && (str[:7] == "http://" || str[:8] == "https://")
}

// downloadAndParseJSON 下载 URL 内容并解析为 JSON
func (p *SpeakerDiarizationProcessorImpl) downloadAndParseJSON(url string) (*SpeakerDiarizationResult, error) {
	// 下载文件
	resp, err := http.Get(url)
	if err != nil {
		return nil, fmt.Errorf("failed to download file from URL %s: %w", url, err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to download file, status code: %d", resp.StatusCode)
	}

	// 读取内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// 解析 JSON
	var result SpeakerDiarizationResult
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("failed to parse downloaded JSON: %w", err)
	}

	fmt.Printf("Successfully downloaded and parsed JSON from URL\n")
	return &result, nil
}
