package handlers

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"

	"uniscribe-service/internal/config"
	"uniscribe-service/internal/constants"
	"uniscribe-service/internal/httpclient"
	"uniscribe-service/internal/service/speech/stt"
	"uniscribe-service/internal/service/speech/stt/providers"

	sentryutil "uniscribe-service/internal/sentry"

	"github.com/getsentry/sentry-go"
)

// AudioTaskProcessor 接口定义了音频处理器应该实现的方法
type AudioTaskProcessor interface {
	Process(ctx context.Context, task Task) error
}

// AudioProcessorImpl 实现了 AudioProcessor 接口
type AudioTaskProcessorImpl struct {
	resultSender ResultSender
}

// 添加类型别名
type sttTask = providers.Task

func NewAudioTaskProcessor(resultSender ResultSender) AudioTaskProcessor {
	return &AudioTaskProcessorImpl{resultSender: resultSender}
}

func (p *AudioTaskProcessorImpl) Process(ctx context.Context, task Task) error {
	// 1. 更新任务状态为处理中
	err := p.resultSender.UpdateTaskStatus(task.TaskID, constants.TaskStatusProcessing)
	if err != nil {
		log.Printf("Failed to update task status to processing: %v", err)
		// 不因为状态更新失败而终止任务处理
	}

	// 2. 处理转录
	payload, err := p.processTranscription(ctx, task)
	if err != nil {
		return p.sendTaskFailedResult(task, err)
	}

	// 3. 发送转录结果（这会将状态设置为 completed）
	err = p.resultSender.Send(payload)
	if err != nil {
		return p.sendTaskFailedResult(task, err)
	}

	// 4. 创建后续任务
	err = p.createSubsequentTasks(ctx, task)
	if err != nil {
		return p.sendTaskFailedResult(task, err)
	}
	return nil
}

func (p *AudioTaskProcessorImpl) processTranscription(ctx context.Context, task Task) (*TranscriptionResultPayload, error) {
	// 使用 STT Service 单例
	sttService := stt.GetService()

	// 在使用时转换类型
	handlerTask := task // 原始的 Task 类型
	sttTaskInstance := providers.Task{
		TaskID:                   handlerTask.TaskID,
		FileUrl:                  handlerTask.FileUrl,
		TaskType:                 handlerTask.TaskType,
		Priority:                 handlerTask.Priority,
		TranscriptionFileId:      handlerTask.TranscriptionFileId,
		TranscriptionText:        handlerTask.TranscriptionText,
		FileDuration:             handlerTask.FileDuration,
		Language:                 handlerTask.Language,
		LanguageCode:             handlerTask.LanguageCode,
		TranscriptionType:        handlerTask.TranscriptionType,
		RequestedServiceProvider: handlerTask.RequestedServiceProvider,
	}

	// 使用支持 failover 的转录方法
	transcriptionResult, err := sttService.TranscribeWithFailover(ctx, sttTaskInstance)
	if err != nil {
		sentryutil.CaptureException(err)
		return nil, fmt.Errorf("failed to transcribe audio: %w", err)
	}

	payload := &TranscriptionResultPayload{
		TaskID:           task.TaskID,
		TaskType:         constants.TaskTypeTranscription,
		Text:             transcriptionResult.Text,
		DetectedLanguage: transcriptionResult.DetectedLanguage,
		Segments:         transcriptionResult.Segments,
		ServiceProvider:  string(transcriptionResult.UsedModel),
	}

	return payload, nil
}

var subsequentTasksClient = httpclient.NewWebBackendClient()

func (p *AudioTaskProcessorImpl) createSubsequentTasks(ctx context.Context, task Task) error {
	apiUrl := config.Cfg.WebBackendHost + "/tasks/text"
	body := struct {
		TranscriptionFileId int64 `json:"transcriptionFileId"`
	}{
		TranscriptionFileId: task.TranscriptionFileId,
	}

	bodyBytes, err := json.Marshal(body)
	if err != nil {
		return fmt.Errorf("failed to marshal body: %w", err)
	}

	resp, err := subsequentTasksClient.Post(apiUrl, "application/json", bytes.NewBuffer(bodyBytes))
	if err != nil {
		// Log the error but don't fail the entire task
		// The backend's create_text_tasks function now handles duplicates gracefully
		log.Printf("Warning: Failed to create subsequent tasks for file %d: %v", task.TranscriptionFileId, err)

		// Add Sentry context for monitoring
		sentryutil.WithScope(func(scope *sentry.Scope) {
			scope.SetTag("component", "audio_processor")
			scope.SetTag("operation", "create_subsequent_tasks")
			scope.SetExtra("transcription_file_id", task.TranscriptionFileId)
			scope.SetExtra("task_id", task.TaskID)
			sentryutil.CaptureException(err)
		})

		// Return nil to prevent task retry - transcription is already complete
		return nil
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		// Read response body for better error context
		body, readErr := io.ReadAll(resp.Body)
		if readErr != nil {
			log.Printf("Warning: Failed to create subsequent tasks for file %d (status %d), and failed to read response body: %v",
				task.TranscriptionFileId, resp.StatusCode, readErr)
		} else {
			log.Printf("Warning: Failed to create subsequent tasks for file %d (status %d): %s",
				task.TranscriptionFileId, resp.StatusCode, string(body))
		}

		// Add Sentry context for monitoring
		sentryutil.WithScope(func(scope *sentry.Scope) {
			scope.SetTag("component", "audio_processor")
			scope.SetTag("operation", "create_subsequent_tasks")
			scope.SetExtra("transcription_file_id", task.TranscriptionFileId)
			scope.SetExtra("task_id", task.TaskID)
			scope.SetExtra("status_code", resp.StatusCode)
			if readErr == nil {
				scope.SetExtra("response_body", string(body))
			}
			sentryutil.CaptureException(fmt.Errorf("HTTP %d error creating subsequent tasks", resp.StatusCode))
		})

		// Return nil to prevent task retry - transcription is already complete
		return nil
	}

	log.Printf("Successfully created subsequent tasks for transcription file %d", task.TranscriptionFileId)
	return nil
}

func (p *AudioTaskProcessorImpl) sendTaskFailedResult(task Task, err error) error {
	// 1. 添加错误追踪
	sentryutil.WithScope(func(scope *sentry.Scope) {
		scope.SetExtra("taskID", task.TaskID)
		scope.AddBreadcrumb(&sentry.Breadcrumb{
			Message: err.Error(),
			Level:   sentry.LevelError,
		}, 0)
		sentryutil.CaptureException(err)
	})

	// 2. 更新任务状态为失败（这会自动触发文件状态更新和 webhook 通知）
	statusErr := p.resultSender.UpdateTaskStatus(task.TaskID, constants.TaskStatusFailed, err.Error())
	if statusErr != nil {
		log.Printf("Failed to update task status to failed: %v", statusErr)
		return fmt.Errorf("failed to update task status: %w", statusErr)
	}

	return nil
}
