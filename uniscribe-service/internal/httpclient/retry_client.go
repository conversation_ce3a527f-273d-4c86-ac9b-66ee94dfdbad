package httpclient

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"time"

	sentryutil "uniscribe-service/internal/sentry"

	"github.com/getsentry/sentry-go"
)

// RetryConfig 重试配置
type RetryConfig struct {
	MaxRetries int           // 最大重试次数
	RetryDelay time.Duration // 重试间隔
	Timeout    time.Duration // 请求超时时间
}

// DefaultWebBackendConfig web-backend 服务的默认重试配置
var DefaultWebBackendConfig = RetryConfig{
	MaxRetries: 4,                // 最多重试4次，总共5次尝试
	RetryDelay: 3 * time.Second,  // 重试间隔3秒，匹配重启时间
	Timeout:    10 * time.Second, // 10秒超时，适合快速状态更新
}

// DefaultExternalAPIConfig 外部API服务的默认重试配置
var DefaultExternalAPIConfig = RetryConfig{
	MaxRetries: 3,                 // 最多重试3次
	RetryDelay: 2 * time.Second,   // 重试间隔2秒
	Timeout:    30 * time.Second,  // 30秒超时，适合外部API调用
}

// RetryClient 带重试机制的HTTP客户端
type RetryClient struct {
	client *http.Client
	config RetryConfig
}

// NewRetryClient 创建新的重试客户端
func NewRetryClient(config RetryConfig) *RetryClient {
	return &RetryClient{
		client: &http.Client{
			Timeout: config.Timeout,
		},
		config: config,
	}
}

// NewWebBackendClient 创建用于web-backend的重试客户端
func NewWebBackendClient() *RetryClient {
	return NewRetryClient(DefaultWebBackendConfig)
}

// NewExternalAPIClient 创建用于外部API的重试客户端
func NewExternalAPIClient() *RetryClient {
	return NewRetryClient(DefaultExternalAPIConfig)
}

// isRetryableError 判断错误是否可重试
func (c *RetryClient) isRetryableError(err error, statusCode int) bool {
	if err != nil {
		// 网络错误、连接拒绝、超时等都可重试
		return true
	}
	// 5xx服务器错误可重试，4xx客户端错误不重试
	return statusCode >= 500 && statusCode < 600
}

// Do 执行HTTP请求，带重试机制
func (c *RetryClient) Do(req *http.Request) (*http.Response, error) {
	var lastErr error
	var lastResp *http.Response
	
	// 保存原始请求体，用于重试
	var bodyBytes []byte
	if req.Body != nil {
		bodyBytes, _ = io.ReadAll(req.Body)
		req.Body.Close()
	}
	
	for attempt := 0; attempt <= c.config.MaxRetries; attempt++ {
		// 为每次尝试重新设置请求体
		if bodyBytes != nil {
			req.Body = io.NopCloser(bytes.NewReader(bodyBytes))
		}
		
		resp, err := c.client.Do(req)
		if err != nil {
			lastErr = err
			
			// 检查是否应该重试
			if attempt < c.config.MaxRetries && c.isRetryableError(err, 0) {
				fmt.Printf("Request failed (attempt %d/%d), retrying in %v: %v\n",
					attempt+1, c.config.MaxRetries+1, c.config.RetryDelay, err)
				time.Sleep(c.config.RetryDelay)
				continue
			}
			
			// 最终失败 - 发送到Sentry
			sentryutil.WithScope(func(scope *sentry.Scope) {
				scope.SetTag("component", "retry_client")
				scope.SetTag("operation", "http_request")
				scope.SetExtra("url", req.URL.String())
				scope.SetExtra("method", req.Method)
				scope.SetExtra("attempts", attempt+1)
				sentryutil.CaptureException(err)
			})
			return nil, fmt.Errorf("request failed after %d attempts: %v", attempt+1, err)
		}
		
		// 检查响应状态码
		if resp.StatusCode < 400 {
			// 成功响应
			return resp, nil
		}
		
		// 读取错误响应体
		body, readErr := io.ReadAll(resp.Body)
		resp.Body.Close()
		
		if readErr != nil {
			lastErr = fmt.Errorf("failed to read response body: %v", readErr)
		} else {
			lastErr = fmt.Errorf("request failed with status %d: %s", resp.StatusCode, string(body))
		}
		lastResp = resp
		
		// 检查是否应该重试
		if attempt < c.config.MaxRetries && c.isRetryableError(nil, resp.StatusCode) {
			fmt.Printf("Request failed with status %d (attempt %d/%d), retrying in %v: %s\n",
				resp.StatusCode, attempt+1, c.config.MaxRetries+1, c.config.RetryDelay, string(body))
			time.Sleep(c.config.RetryDelay)
			continue
		}
		
		// 最终失败或不可重试错误 - 发送到Sentry
		sentryutil.WithScope(func(scope *sentry.Scope) {
			scope.SetTag("component", "retry_client")
			scope.SetTag("operation", "http_request")
			scope.SetExtra("url", req.URL.String())
			scope.SetExtra("method", req.Method)
			scope.SetExtra("status_code", resp.StatusCode)
			scope.SetExtra("response_body", string(body))
			scope.SetExtra("attempts", attempt+1)
			sentryutil.CaptureException(lastErr)
		})
		return nil, fmt.Errorf("request failed after %d attempts: %v", attempt+1, lastErr)
	}
	
	return lastResp, lastErr
}

// Post 发送POST请求
func (c *RetryClient) Post(url, contentType string, body io.Reader) (*http.Response, error) {
	req, err := http.NewRequest("POST", url, body)
	if err != nil {
		return nil, fmt.Errorf("failed to create POST request: %v", err)
	}
	
	if contentType != "" {
		req.Header.Set("Content-Type", contentType)
	}
	
	return c.Do(req)
}

// Get 发送GET请求
func (c *RetryClient) Get(url string) (*http.Response, error) {
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create GET request: %v", err)
	}
	
	return c.Do(req)
}
