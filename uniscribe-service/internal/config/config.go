package config

import (
	"log"
	"os"

	"github.com/joho/godotenv"
)

type Config struct {
	WebBackendHost           string `json:"web_backend_host"`
	DashscopeApiKey          string `json:"dashscope_api_key"`
	ProxyURLStr              string `json:"proxy_url"`
	OpenaiApiKey             string `json:"openai_api_key"`              // OpenAI Official API key
	OpenaiProvider           string `json:"openai_provider"`           // Provider type: openai, azure, custom
	OpenaiCustomBaseURL      string `json:"openai_custom_base_url"`    // Custom base URL when provider is "custom"
	OpenaiAzureApiKey        string `json:"openai_azure_api_key"`      // Azure OpenAI API key
	OpenaiAzureResourceName     string `json:"openai_azure_resource_name"`     // Azure resource name
	OpenaiAzureDeploymentName   string `json:"openai_azure_deployment_name"`   // Azure deployment name
	OpenaiAzurePaidDeployment   string `json:"openai_azure_paid_deployment"`   // Azure deployment for paid users
	OpenaiAzureFreeDeployment   string `json:"openai_azure_free_deployment"`   // Azure deployment for free users
	OpenaiMultiModelEnabled     bool   `json:"openai_multi_model_enabled"`     // Enable multi-model support
	OpenaiHealthCheckEnabled bool   `json:"openai_health_check_enabled"` // Enable health check on startup
	ReplicateApiToken        string `json:"replicate_api_token"`
	FalApiKey                string `json:"fal_api_key"`
	DeepInfraApiKey          string `json:"deepinfra_api_key"`
	RedisHost                string `json:"redis_host"`
	RedisPort                string `json:"redis_port"`
	RedisPassword            string `json:"redis_password"`
	RedisSSL                 bool   `json:"redis_ssl"`
	Environment              string `json:"environment"`               // Environment: production, development, testing
}

var Cfg Config

// Environment detection functions
func IsProductionEnv() bool {
	return Cfg.Environment == "production"
}

func IsDevelopmentEnv() bool {
	return Cfg.Environment == "development"
}

func IsTestingEnv() bool {
	return Cfg.Environment == "testing"
}

func LoadConfig() {
	err := godotenv.Load()
	if err != nil {
		log.Fatalf("Error loading .env file: %v", err)
	}
	log.Printf("Config loaded")

	// Load environment setting (similar to Python backend's APP_SETTINGS)
	environment := os.Getenv("APP_SETTINGS")
	if environment == "" {
		log.Printf("APP_SETTINGS not set, defaulting to development")
		environment = "development"
	}
	log.Printf("Running in %s environment", environment)

	webBackendHost := os.Getenv("WEB_BACKEND_HOST")
	if webBackendHost == "" {
		log.Fatalf("WEB_BACKEND_HOST environment variable is required")
	}

	dashscopeApiKey := os.Getenv("DASHSCOPE_API_KEY")
	if dashscopeApiKey == "" {
		log.Fatalf("DASHSCOPE_API_KEY environment variable is required")
	}

	proxyURLStr := os.Getenv("PROXY_URL")
	if proxyURLStr == "" {
		log.Printf("No proxy URL provided, using default http client")
	}

	openaiApiKey := os.Getenv("OPENAI_API_KEY")
	openaiAzureApiKey := os.Getenv("OPENAI_AZURE_API_KEY")

	// Validate API keys based on configuration
	if openaiApiKey == "" && openaiAzureApiKey == "" {
		log.Fatalf("At least one API key is required: OPENAI_API_KEY or OPENAI_AZURE_API_KEY")
	}

	// OpenAI provider configuration
	openaiProvider := os.Getenv("OPENAI_PROVIDER")
	if openaiProvider == "" {
		log.Printf("OPENAI_PROVIDER not set, using default: openai")
		openaiProvider = "openai"
	}

	openaiCustomBaseURL := os.Getenv("OPENAI_CUSTOM_BASE_URL")
	if openaiProvider == "custom" && openaiCustomBaseURL == "" {
		log.Fatalf("OPENAI_CUSTOM_BASE_URL is required when OPENAI_PROVIDER is set to 'custom'")
	}

	// Azure OpenAI configuration
	openaiAzureResourceName := os.Getenv("OPENAI_AZURE_RESOURCE_NAME")
	openaiAzureDeploymentName := os.Getenv("OPENAI_AZURE_DEPLOYMENT_NAME")
	openaiAzurePaidDeployment := os.Getenv("OPENAI_AZURE_PAID_DEPLOYMENT")
	openaiAzureFreeDeployment := os.Getenv("OPENAI_AZURE_FREE_DEPLOYMENT")

	// Multi-model configuration
	openaiMultiModelEnabled := os.Getenv("OPENAI_MULTI_MODEL_ENABLED") == "true"

	if openaiProvider == "azure" {
		if openaiAzureResourceName == "" {
			log.Fatalf("OPENAI_AZURE_RESOURCE_NAME is required when OPENAI_PROVIDER is set to 'azure'")
		}
		if !openaiMultiModelEnabled && openaiAzureDeploymentName == "" {
			log.Fatalf("OPENAI_AZURE_DEPLOYMENT_NAME is required when OPENAI_PROVIDER is set to 'azure' and multi-model is disabled")
		}
		if openaiMultiModelEnabled {
			if openaiAzurePaidDeployment == "" || openaiAzureFreeDeployment == "" {
				log.Fatalf("OPENAI_AZURE_PAID_DEPLOYMENT and OPENAI_AZURE_FREE_DEPLOYMENT are required when multi-model is enabled")
			}
		}
	}

	// OpenAI health check configuration
	openaiHealthCheckEnabled := true // Default to enabled
	if healthCheckStr := os.Getenv("OPENAI_HEALTH_CHECK_ENABLED"); healthCheckStr != "" {
		openaiHealthCheckEnabled = healthCheckStr == "true" || healthCheckStr == "1"
	}

	replicateApiToken := os.Getenv("REPLICATE_API_TOKEN")
	if replicateApiToken == "" {
		log.Fatalf("REPLICATE_API_TOKEN environment variable is required")
	}

	falApiKey := os.Getenv("FAL_API_KEY")
	if falApiKey == "" {
		log.Fatalf("FAL_KEY environment variable is required")
	}

	deepInfraApiKey := os.Getenv("DEEPINFRA_API_KEY")
	if deepInfraApiKey == "" {
		log.Printf("DEEPINFRA_API_KEY environment variable is not set, DeepInfra provider will not be available")
		deepInfraApiKey = ""
	}

	// Redis 配置 - 必须显式设置，不提供默认值
	redisHost := os.Getenv("REDIS_HOST")
	if redisHost == "" {
		log.Fatal("REDIS_HOST environment variable is required")
	}

	redisPort := os.Getenv("REDIS_PORT")
	if redisPort == "" {
		log.Fatal("REDIS_PORT environment variable is required")
	}

	redisPassword := os.Getenv("REDIS_PASSWORD")
	// Redis 密码可以为空，但如果需要认证必须设置

	// Redis SSL 配置
	redisSSL := false
	if sslStr := os.Getenv("REDIS_SSL"); sslStr != "" {
		redisSSL = sslStr == "true" || sslStr == "1"
	}

	Cfg = Config{
		WebBackendHost:            webBackendHost,
		DashscopeApiKey:           dashscopeApiKey,
		ProxyURLStr:               proxyURLStr,
		OpenaiApiKey:              openaiApiKey,
		OpenaiProvider:            openaiProvider,
		OpenaiCustomBaseURL:       openaiCustomBaseURL,
		OpenaiAzureApiKey:         openaiAzureApiKey,
		OpenaiAzureResourceName:   openaiAzureResourceName,
		OpenaiAzureDeploymentName: openaiAzureDeploymentName,
		OpenaiAzurePaidDeployment: openaiAzurePaidDeployment,
		OpenaiAzureFreeDeployment: openaiAzureFreeDeployment,
		OpenaiMultiModelEnabled:   openaiMultiModelEnabled,
		OpenaiHealthCheckEnabled:  openaiHealthCheckEnabled,
		ReplicateApiToken:         replicateApiToken,
		FalApiKey:                 falApiKey,
		DeepInfraApiKey:           deepInfraApiKey,
		RedisHost:                 redisHost,
		RedisPort:                 redisPort,
		RedisPassword:             redisPassword,
		RedisSSL:                  redisSSL,
		Environment:               environment,
	}
}
