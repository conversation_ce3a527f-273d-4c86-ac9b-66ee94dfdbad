package sentry

import (
	"time"
	"uniscribe-service/internal/config"

	"github.com/getsentry/sentry-go"
)

// CaptureException conditionally captures exceptions to Sentry only in production
func CaptureException(err error) {
	if config.IsProductionEnv() {
		sentry.CaptureException(err)
	}
}

// WithScope conditionally executes the scope function only in production
func WithScope(f func(scope *sentry.Scope)) {
	if config.IsProductionEnv() {
		sentry.WithScope(f)
	}
}

// CaptureMessage conditionally captures messages to Sentry only in production
func CaptureMessage(message string) {
	if config.IsProductionEnv() {
		sentry.CaptureMessage(message)
	}
}

// AddBreadcrumb conditionally adds breadcrumbs to Sentry only in production
func AddBreadcrumb(breadcrumb *sentry.Breadcrumb) {
	if config.IsProductionEnv() {
		sentry.AddBreadcrumb(breadcrumb)
	}
}

// Flush conditionally flushes Sentry only in production
func Flush(timeout time.Duration) bool {
	if config.IsProductionEnv() {
		return sentry.Flush(timeout)
	}
	return true
}
