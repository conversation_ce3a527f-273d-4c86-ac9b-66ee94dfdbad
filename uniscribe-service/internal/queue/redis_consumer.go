package queue

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"log"
	"strconv"
	"time"

	"github.com/getsentry/sentry-go"
	"github.com/redis/go-redis/v9"

	"uniscribe-service/internal/config"
	"uniscribe-service/internal/constants"
	"uniscribe-service/internal/handlers"
	sentryutil "uniscribe-service/internal/sentry"
)

// RedisStreamConsumer Redis Streams 消费者
type RedisStreamConsumer struct {
	client       *redis.Client
	consumerName string
	groupName    string
}

// NewRedisStreamConsumer 创建新的 Redis Streams 消费者
func NewRedisStreamConsumer() (*RedisStreamConsumer, error) {
	// 创建 Redis 客户端选项
	opts := &redis.Options{
		Addr:         fmt.Sprintf("%s:%s", config.Cfg.RedisHost, config.Cfg.RedisPort),
		Password:     config.Cfg.RedisPassword,
		DB:           0,
		DialTimeout:  10 * time.Second, // 连接超时
		ReadTimeout:  30 * time.Second, // 读取超时
		WriteTimeout: 30 * time.Second, // 写入超时
	}

	// 如果启用 SSL，添加 TLS 配置
	if config.Cfg.RedisSSL {
		opts.TLSConfig = &tls.Config{
			InsecureSkipVerify: false, // 生产环境应该验证证书
		}
	}

	rdb := redis.NewClient(opts)

	// 测试连接，增加超时时间
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()

	_, err := rdb.Ping(ctx).Result()
	if err != nil {
		sentryutil.WithScope(func(scope *sentry.Scope) {
			scope.SetTag("component", "redis_consumer")
			scope.SetTag("operation", "connection")
			scope.SetExtra("redis_host", config.Cfg.RedisHost)
			scope.SetExtra("redis_port", config.Cfg.RedisPort)
			sentryutil.CaptureException(err)
		})
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}

	log.Printf("Connected to Redis at %s:%s", config.Cfg.RedisHost, config.Cfg.RedisPort)

	consumer := &RedisStreamConsumer{
		client:       rdb,
		consumerName: "go-worker-1",
		groupName:    "workers",
	}

	// 初始化消费者组
	if err := consumer.initConsumerGroups(ctx); err != nil {
		return nil, fmt.Errorf("failed to initialize consumer groups: %w", err)
	}

	return consumer, nil
}

// initConsumerGroups 初始化消费者组
func (c *RedisStreamConsumer) initConsumerGroups(ctx context.Context) error {
	streams := []string{
		constants.StreamTranscriptionHigh,
		constants.StreamTranscriptionLow,
		constants.StreamTextHigh,
		constants.StreamTextLow,
		constants.StreamSpeakerDiarizationHigh,
		constants.StreamSpeakerDiarizationLow,
		constants.StreamRetry,
	}

	for _, stream := range streams {
		err := c.client.XGroupCreate(ctx, stream, c.groupName, "$").Err()
		if err != nil && err.Error() != "BUSYGROUP Consumer Group name already exists" {
			sentryutil.WithScope(func(scope *sentry.Scope) {
				scope.SetTag("component", "redis_consumer")
				scope.SetTag("operation", "group_create")
				scope.SetExtra("stream", stream)
				scope.SetExtra("group_name", c.groupName)
				sentryutil.CaptureException(err)
			})
			log.Printf("Warning: failed to create consumer group for %s: %v", stream, err)
		} else {
			log.Printf("Consumer group initialized for stream: %s", stream)
		}
	}

	return nil
}

// ConsumeTranscriptionTasks 消费转录任务（优先级消费）
func (c *RedisStreamConsumer) ConsumeTranscriptionTasks(ctx context.Context, taskQueue chan<- handlers.Task) {
	c.consumeWithPriority(ctx, []string{constants.StreamTranscriptionHigh, constants.StreamTranscriptionLow}, taskQueue)
}

// ConsumeTextTasks 消费文本任务（优先级消费）
func (c *RedisStreamConsumer) ConsumeTextTasks(ctx context.Context, taskQueue chan<- handlers.Task) {
	c.consumeWithPriority(ctx, []string{constants.StreamTextHigh, constants.StreamTextLow}, taskQueue)
}

// ConsumeSpeakerDiarizationTasks 消费说话人识别任务（优先级消费）
func (c *RedisStreamConsumer) ConsumeSpeakerDiarizationTasks(ctx context.Context, taskQueue chan<- handlers.Task) {
	c.consumeWithPriority(ctx, []string{constants.StreamSpeakerDiarizationHigh, constants.StreamSpeakerDiarizationLow}, taskQueue)
}

// ConsumeRetryTasks 消费重试任务
func (c *RedisStreamConsumer) ConsumeRetryTasks(ctx context.Context, taskQueue chan<- handlers.Task) {
	c.consumeFromStream(ctx, constants.StreamRetry, taskQueue)
}

// consumeWithPriority 按优先级消费多个队列（高优先级优先）
func (c *RedisStreamConsumer) consumeWithPriority(ctx context.Context, streamNames []string, taskQueue chan<- handlers.Task) {
	log.Printf("Starting priority consumption from streams: %v", streamNames)

	for {
		select {
		case <-ctx.Done():
			log.Printf("Context cancelled, stopping priority consumption from %v", streamNames)
			return
		default:
			// 按优先级顺序尝试消费（第一个是高优先级）
			consumed := false
			for _, streamName := range streamNames {
				// 尝试从当前队列读取一条消息
				streams, err := c.client.XReadGroup(ctx, &redis.XReadGroupArgs{
					Group:    c.groupName,
					Consumer: c.consumerName,
					Streams:  []string{streamName, ">"}, // ">" 表示只读取新的未处理消息
					Count:    1,
					Block:    time.Second, // 增加阻塞时间减少 CPU 使用率
				}).Result()

				if err != nil {
					if err == redis.Nil {
						// 没有新消息，尝试下一个队列
						continue
					}
					sentryutil.WithScope(func(scope *sentry.Scope) {
						scope.SetTag("component", "redis_consumer")
						scope.SetTag("operation", "read_stream")
						scope.SetExtra("stream", streamName)
						scope.SetExtra("consumer", c.consumerName)
						scope.SetExtra("group", c.groupName)
						sentryutil.CaptureException(err)
					})
					log.Printf("Error reading from stream %s: %v", streamName, err)
					continue
				}

				// 处理消息
				for _, stream := range streams {
					for _, message := range stream.Messages {
						task, err := c.parseMessage(message)
						if err != nil {
							sentryutil.WithScope(func(scope *sentry.Scope) {
								scope.SetTag("component", "redis_consumer")
								scope.SetTag("operation", "parse_message")
								scope.SetExtra("stream", streamName)
								scope.SetExtra("message_id", message.ID)
								scope.SetExtra("message_values", message.Values)
								sentryutil.CaptureException(err)
							})
							log.Printf("Error parsing message from %s: %v", streamName, err)
							// 确认并删除无效消息以避免重复处理
							c.client.XAck(ctx, streamName, c.groupName, message.ID)
							c.client.XDel(ctx, streamName, message.ID)
							continue
						}

						log.Printf("Received priority task from %s: ID=%d, Type=%d", streamName, task.TaskID, task.TaskType)

						// 发送任务到处理队列
						select {
						case taskQueue <- task:
							// 任务成功发送到队列，确认并删除消息
							err := c.client.XAck(ctx, streamName, c.groupName, message.ID).Err()
							if err != nil {
								sentryutil.WithScope(func(scope *sentry.Scope) {
									scope.SetTag("component", "redis_consumer")
									scope.SetTag("operation", "ack_message")
									scope.SetExtra("stream", streamName)
									scope.SetExtra("message_id", message.ID)
									scope.SetExtra("task_id", task.TaskID)
									sentryutil.CaptureException(err)
								})
								log.Printf("Error acknowledging message %s: %v", message.ID, err)
							} else {
								// 确认成功后立即删除消息以节省内存
								delErr := c.client.XDel(ctx, streamName, message.ID).Err()
								if delErr != nil {
									log.Printf("Warning: failed to delete message %s from %s: %v", message.ID, streamName, delErr)
								} else {
									log.Printf("Message %s acknowledged and deleted from %s", message.ID, streamName)
								}
							}
							consumed = true
						case <-ctx.Done():
							return
						}
					}
				}

				// 如果从高优先级队列消费到了任务，就不再检查低优先级队列
				if consumed {
					break
				}
			}

			// 如果所有队列都没有消息，稍微等待一下
			if !consumed {
				time.Sleep(time.Second) // 增加等待时间减少 CPU 使用率
			}
		}
	}
}

// consumeFromStream 从指定的 stream 消费消息
func (c *RedisStreamConsumer) consumeFromStream(ctx context.Context, streamName string, taskQueue chan<- handlers.Task) {
	log.Printf("Starting to consume from stream: %s", streamName)

	for {
		select {
		case <-ctx.Done():
			log.Printf("Context cancelled, stopping consumption from %s", streamName)
			return
		default:
			// 从 Redis Stream 读取消息
			streams, err := c.client.XReadGroup(ctx, &redis.XReadGroupArgs{
				Group:    c.groupName,
				Consumer: c.consumerName,
				Streams:  []string{streamName, ">"}, // ">" 表示只读取新的未处理消息
				Count:    1,
				Block:    time.Second, // 1秒超时
			}).Result()
			if err != nil {
				if err == redis.Nil {
					// 没有新消息，继续等待
					continue
				}
				log.Printf("Error reading from stream %s: %v", streamName, err)
				time.Sleep(time.Second)
				continue
			}

			// 处理消息
			for _, stream := range streams {
				for _, message := range stream.Messages {
					task, err := c.parseMessage(message)
					if err != nil {
						log.Printf("Error parsing message from %s: %v", streamName, err)
						// 确认并删除无效消息以避免重复处理
						c.client.XAck(ctx, streamName, c.groupName, message.ID)
						c.client.XDel(ctx, streamName, message.ID)
						continue
					}

					log.Printf("Received task from %s: ID=%d, Type=%d", streamName, task.TaskID, task.TaskType)

					// 发送任务到处理队列
					select {
					case taskQueue <- task:
						// 任务成功发送到队列，确认并删除消息
						err := c.client.XAck(ctx, streamName, c.groupName, message.ID).Err()
						if err != nil {
							log.Printf("Error acknowledging message %s: %v", message.ID, err)
						} else {
							// 确认成功后立即删除消息以节省内存
							delErr := c.client.XDel(ctx, streamName, message.ID).Err()
							if delErr != nil {
								log.Printf("Warning: failed to delete message %s from %s: %v", message.ID, streamName, delErr)
							} else {
								log.Printf("Message %s acknowledged and deleted from %s", message.ID, streamName)
							}
						}
					case <-ctx.Done():
						return
					}
				}
			}
		}
	}
}

// parseMessage 解析 Redis Stream 消息为 Task
func (c *RedisStreamConsumer) parseMessage(message redis.XMessage) (handlers.Task, error) {
	var task handlers.Task

	// 解析基本字段
	if idStr, ok := message.Values["task_id"].(string); ok {
		if id, err := strconv.ParseInt(idStr, 10, 64); err == nil {
			task.TaskID = id
		}
	}

	if typeStr, ok := message.Values["task_type"].(string); ok {
		if taskType, err := strconv.Atoi(typeStr); err == nil {
			task.TaskType = constants.TaskType(taskType)
		}
	}

	if fileIDStr, ok := message.Values["file_id"].(string); ok {
		if fileID, err := strconv.ParseInt(fileIDStr, 10, 64); err == nil {
			task.TranscriptionFileId = fileID
		}
	}

	// 解析优先级字段
	if priorityStr, ok := message.Values["priority"].(string); ok {
		if priority, err := strconv.Atoi(priorityStr); err == nil {
			task.Priority = priority
		}
	}

	// 解析可选字段
	if fileURL, ok := message.Values["file_url"].(string); ok {
		task.FileUrl = fileURL
	}

	if transcriptionText, ok := message.Values["transcription_text"].(string); ok {
		task.TranscriptionText = transcriptionText
	}

	if durationStr, ok := message.Values["file_duration"].(string); ok {
		if duration, err := strconv.ParseFloat(durationStr, 64); err == nil {
			task.FileDuration = duration
		}
	}

	if languageCode, ok := message.Values["language_code"].(string); ok {
		task.LanguageCode = languageCode
	}

	if language, ok := message.Values["language"].(string); ok {
		task.Language = language
	}

	if transcriptionType, ok := message.Values["transcription_type"].(string); ok {
		task.TranscriptionType = transcriptionType
	}

	if serviceProvider, ok := message.Values["requested_service_provider"].(string); ok {
		task.RequestedServiceProvider = serviceProvider
	}

	// 如果有完整的 payload，尝试解析
	if payload, ok := message.Values["payload"].(string); ok {
		var payloadData map[string]interface{}
		if err := json.Unmarshal([]byte(payload), &payloadData); err == nil {
			// 可以从 payload 中提取更多字段
			log.Printf("Parsed payload for task %d", task.TaskID)
		}
	}

	// 验证必需字段
	if task.TaskID == 0 || task.TaskType == 0 {
		return task, fmt.Errorf("missing required fields: ID=%d, TaskType=%d", task.TaskID, task.TaskType)
	}

	return task, nil
}

// Close 关闭 Redis 连接
func (c *RedisStreamConsumer) Close() error {
	return c.client.Close()
}
