package providers

// OpenAIProvider represents the official OpenAI API provider
type OpenAIProvider struct {
	*BaseProvider
}

// NewOpenAIProvider creates a new OpenAI official provider
func NewOpenAIProvider(apiKey, proxyURL string) (*OpenAIProvider, error) {
	config := ProviderConfig{
		Type:     OpenAIOfficial,
		Name:     "OpenAI Official",
		APIKey:   apiKey,
		BaseURL:  "https://api.openai.com/v1",
		ProxyURL: proxyURL,
	}

	baseProvider, err := NewBaseProvider(config)
	if err != nil {
		return nil, err
	}

	return &OpenAIProvider{
		BaseProvider: baseProvider,
	}, nil
}
