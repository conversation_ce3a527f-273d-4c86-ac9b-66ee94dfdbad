package providers

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"net/url"
	"time"

	"github.com/sa<PERSON><PERSON><PERSON>/go-openai"
)

const (
	// DefaultOpenAIURL is the default base URL for the OpenAI API
	DefaultOpenAIURL = "https://api.openai.com/v1"
)

// BaseProvider provides common functionality for all OpenAI providers
type BaseProvider struct {
	config ProviderConfig
	client *openai.Client
}

// NewBaseProvider creates a new base provider with the given configuration
func NewBaseProvider(config ProviderConfig) (*BaseProvider, error) {
	if config.APIKey == "" {
		return nil, fmt.Errorf("API key is required for provider %s", config.Name)
	}

	if config.BaseURL != "" && config.BaseURL != DefaultOpenAIURL {
		log.Printf("Initializing provider: %s (type: %s) with base URL: %s", config.Name, config.Type, config.BaseURL)
	} else {
		log.Printf("Initializing provider: %s (type: %s)", config.Name, config.Type)
	}

	provider := &BaseProvider{
		config: config,
	}

	if err := provider.initClient(); err != nil {
		return nil, fmt.Errorf("failed to initialize client for provider %s: %w", config.Name, err)
	}

	log.Printf("Provider %s initialized successfully", config.Name)
	return provider, nil
}

// initClient initializes the OpenAI client with the provider configuration
func (p *BaseProvider) initClient() error {
	var httpClient *http.Client

	// Configure proxy if provided
	if p.config.ProxyURL != "" {
		proxyUrl, err := url.Parse(p.config.ProxyURL)
		if err != nil {
			return fmt.Errorf("invalid proxy URL: %w", err)
		}
		httpClient = &http.Client{
			Transport: &http.Transport{
				Proxy: http.ProxyURL(proxyUrl),
			},
		}
	} else {
		httpClient = &http.Client{}
	}

	// Create OpenAI config
	config := openai.DefaultConfig(p.config.APIKey)
	config.HTTPClient = httpClient

	// Set base URL if provided
	if p.config.BaseURL != "" {
		config.BaseURL = p.config.BaseURL
		// Only log if it's different from the default OpenAI URL
		if p.config.BaseURL != DefaultOpenAIURL {
			log.Printf("Using custom base URL for provider %s: %s", p.config.Name, p.config.BaseURL)
		}
	}

	p.client = openai.NewClientWithConfig(config)
	return nil
}

// GetClient returns the configured OpenAI client
func (p *BaseProvider) GetClient() *openai.Client {
	return p.client
}

// Name returns the provider name
func (p *BaseProvider) Name() string {
	return p.config.Name
}

// Type returns the provider type
func (p *BaseProvider) Type() ProviderType {
	return p.config.Type
}

// CreateChatCompletion creates a chat completion using the provider's client
func (p *BaseProvider) CreateChatCompletion(ctx context.Context, request openai.ChatCompletionRequest) (openai.ChatCompletionResponse, error) {
	return p.client.CreateChatCompletion(ctx, request)
}

// CreateTranscription creates an audio transcription using the provider's client
func (p *BaseProvider) CreateTranscription(ctx context.Context, request openai.AudioRequest) (openai.AudioResponse, error) {
	return p.client.CreateTranscription(ctx, request)
}

// HealthCheck performs a basic health check on the provider
func (p *BaseProvider) HealthCheck(ctx context.Context) error {
	log.Printf("Performing health check for provider: %s", p.config.Name)

	// Create a context with timeout for health check
	checkCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	// Try to list models as a simple health check
	models, err := p.client.ListModels(checkCtx)
	if err != nil {
		log.Printf("Health check failed for provider %s: %v", p.config.Name, err)
		return fmt.Errorf("health check failed for provider %s: %w", p.config.Name, err)
	}

	// Log health check success
	log.Printf("✅ Health check passed for provider %s (%d models available)", p.config.Name, len(models.Models))

	return nil
}
