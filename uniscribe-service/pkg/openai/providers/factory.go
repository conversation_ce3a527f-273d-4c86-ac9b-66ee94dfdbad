package providers

import (
	"fmt"
	"strings"
)

// CreateProvider creates a provider based on the given configuration
func CreateProvider(config ProviderConfig) (Provider, error) {
	switch config.Type {
	case OpenAIOfficial:
		return NewOpenAIProvider(config.APIKey, config.ProxyURL)
	case AzureOpenAI:
		// For Azure, we expect the BaseURL to contain resource and deployment info
		// This is a simplified version - in practice, you might want separate fields
		return NewCustomProvider(config.Name, config.APIKey, config.BaseURL, config.ProxyURL)
	case Custom:
		return NewCustomProvider(config.Name, config.APIKey, config.BaseURL, config.ProxyURL)
	default:
		return nil, fmt.Errorf("unsupported provider type: %s", config.Type)
	}
}

// ParseProviderType parses a string into a ProviderType
func ParseProviderType(s string) (ProviderType, error) {
	switch strings.ToLower(s) {
	case "openai", "official":
		return OpenAIOfficial, nil
	case "azure", "azure-openai":
		return AzureOpenAI, nil
	case "custom":
		return Custom, nil
	default:
		return "", fmt.Errorf("unknown provider type: %s", s)
	}
}

// GetDefaultBaseURL returns the default base URL for a provider type
func GetDefaultBaseURL(providerType ProviderType) string {
	switch providerType {
	case OpenAIOfficial:
		return "https://api.openai.com/v1"
	default:
		return ""
	}
}
