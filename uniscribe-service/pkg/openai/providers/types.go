package providers

import (
	"context"

	"github.com/sa<PERSON><PERSON>nov/go-openai"
)

// ProviderType represents different OpenAI-compatible service providers
type ProviderType string

const (
	OpenAIOfficial ProviderType = "openai"        // Official OpenAI API
	AzureOpenAI    ProviderType = "azure"         // Azure OpenAI Service
	Custom         ProviderType = "custom"        // Custom base URL
)

// Provider defines the interface for OpenAI-compatible service providers
type Provider interface {
	// GetClient returns the configured OpenAI client
	GetClient() *openai.Client

	// Name returns the provider name
	Name() string

	// Type returns the provider type
	Type() ProviderType

	// CreateChatCompletion creates a chat completion
	CreateChatCompletion(ctx context.Context, request openai.ChatCompletionRequest) (openai.ChatCompletionResponse, error)

	// CreateTranscription creates an audio transcription
	CreateTranscription(ctx context.Context, request openai.AudioRequest) (openai.AudioResponse, error)

	// HealthCheck performs a health check on the provider
	HealthCheck(ctx context.Context) error
}

// ProviderConfig holds configuration for a provider
type ProviderConfig struct {
	Type     ProviderType `json:"type"`
	Name     string       `json:"name"`
	APIKey   string       `json:"api_key"`
	BaseURL  string       `json:"base_url"`
	ProxyURL string       `json:"proxy_url,omitempty"`
}

// Service defines the interface for managing OpenAI providers
type Service interface {
	// RegisterProvider registers a new provider
	RegisterProvider(providerType ProviderType, provider Provider) error

	// RegisterProviderWithName registers a provider with a custom name
	RegisterProviderWithName(name string, provider Provider) error

	// GetProvider gets a provider by type
	GetProvider(providerType ProviderType) (Provider, error)

	// GetProviderByName gets a provider by name
	GetProviderByName(name string) (Provider, error)

	// GetDefaultProvider gets the default provider
	GetDefaultProvider() Provider

	// SetDefaultProvider sets the default provider
	SetDefaultProvider(providerType ProviderType) error

	// ListProviders lists all registered providers
	ListProviders() []ProviderType

	// ListProviderNames lists all registered provider names
	ListProviderNames() []string
}
