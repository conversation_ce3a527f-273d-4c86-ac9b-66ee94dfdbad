package providers

import "fmt"

// CustomProvider represents a custom OpenAI-compatible service provider
type CustomProvider struct {
	*BaseProvider
}

// NewCustomProvider creates a new custom provider with a custom base URL
func NewCustomProvider(name, apiKey, baseURL, proxyURL string) (*CustomProvider, error) {
	if baseURL == "" {
		return nil, fmt.Errorf("base URL is required for custom provider")
	}

	config := ProviderConfig{
		Type:     Custom,
		Name:     name,
		APIKey:   apiKey,
		BaseURL:  baseURL,
		ProxyURL: proxyURL,
	}

	baseProvider, err := NewBaseProvider(config)
	if err != nil {
		return nil, err
	}

	return &CustomProvider{
		BaseProvider: baseProvider,
	}, nil
}
