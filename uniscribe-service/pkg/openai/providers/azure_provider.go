package providers

import (
	"fmt"

	"github.com/sashabaranov/go-openai"
)

// AzureOpenAIProvider represents the Azure OpenAI service provider
type AzureOpenAIProvider struct {
	*BaseProvider
}

// NewAzureOpenAIProvider creates a new Azure OpenAI provider
func NewAzureOpenAIProvider(apiKey, resourceName, deploymentName string) (*AzureOpenAIProvider, error) {
	if resourceName == "" {
		return nil, fmt.Errorf("Azure resource name is required")
	}
	if deploymentName == "" {
		return nil, fmt.Errorf("Azure deployment name is required")
	}

	// For Azure AI Foundry, use the cognitiveservices.azure.com domain
	baseURL := fmt.Sprintf("https://%s.cognitiveservices.azure.com", resourceName)

	config := ProviderConfig{
		Type:     AzureOpenAI,
		Name:     fmt.Sprintf("Azure OpenAI (%s/%s)", resourceName, deploymentName),
		APIKey:   apiKey,
		BaseURL:  baseURL,
	}

	baseProvider, err := NewBaseProvider(config)
	if err != nil {
		return nil, err
	}

	// Create Azure-specific provider
	azureProvider := &AzureOpenAIProvider{
		BaseProvider: baseProvider,
	}

	// Override the client creation to use Azure-specific configuration
	azureProvider.createAzureClient(apiKey, baseURL, deploymentName)

	return azureProvider, nil
}

// createAzureClient creates an Azure-specific OpenAI client
func (p *AzureOpenAIProvider) createAzureClient(apiKey, baseURL, deploymentName string) {
	// Create Azure OpenAI configuration with specific API version that supports json_schema
	config := openai.DefaultAzureConfig(apiKey, baseURL)
	config.APIVersion = "2025-01-01-preview" // Required for json_schema support
	config.AzureModelMapperFunc = func(model string) string {
		// Always use the deployment name for Azure
		return deploymentName
	}

	// Create the client
	p.client = openai.NewClientWithConfig(config)
}
