package providers

import (
	"fmt"
	"sync"
)

// ProviderService manages multiple OpenAI providers
type ProviderService struct {
	providers       map[ProviderType]Provider
	namedProviders  map[string]Provider  // Support for multiple providers of same type
	defaultProvider Provider
	mu              sync.RWMutex
}

// NewProviderService creates a new provider service
func NewProviderService() *ProviderService {
	return &ProviderService{
		providers:      make(map[ProviderType]Provider),
		namedProviders: make(map[string]Provider),
	}
}

// RegisterProvider registers a new provider
func (s *ProviderService) RegisterProvider(providerType ProviderType, provider Provider) error {
	if provider == nil {
		return fmt.Errorf("provider cannot be nil")
	}

	s.mu.Lock()
	defer s.mu.Unlock()

	if _, exists := s.providers[providerType]; exists {
		return fmt.Errorf("provider %s already registered", providerType)
	}

	s.providers[providerType] = provider

	// Set as default if it's the first provider
	if s.defaultProvider == nil {
		s.defaultProvider = provider
	}

	return nil
}

// GetProvider gets a provider by type
func (s *ProviderService) GetProvider(providerType ProviderType) (Provider, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	provider, exists := s.providers[providerType]
	if !exists {
		return nil, fmt.Errorf("provider %s not found", providerType)
	}

	return provider, nil
}

// GetDefaultProvider gets the default provider
func (s *ProviderService) GetDefaultProvider() Provider {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.defaultProvider
}

// SetDefaultProvider sets the default provider
func (s *ProviderService) SetDefaultProvider(providerType ProviderType) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	provider, exists := s.providers[providerType]
	if !exists {
		return fmt.Errorf("provider %s not found", providerType)
	}

	s.defaultProvider = provider
	return nil
}

// ListProviders lists all registered providers
func (s *ProviderService) ListProviders() []ProviderType {
	s.mu.RLock()
	defer s.mu.RUnlock()

	types := make([]ProviderType, 0, len(s.providers))
	for providerType := range s.providers {
		types = append(types, providerType)
	}

	return types
}

// RegisterProviderWithName registers a provider with a custom name
func (s *ProviderService) RegisterProviderWithName(name string, provider Provider) error {
	if provider == nil {
		return fmt.Errorf("provider cannot be nil")
	}

	s.mu.Lock()
	defer s.mu.Unlock()

	if _, exists := s.namedProviders[name]; exists {
		return fmt.Errorf("provider with name %s already registered", name)
	}

	s.namedProviders[name] = provider

	// Set as default if it's the first provider
	if s.defaultProvider == nil {
		s.defaultProvider = provider
	}

	return nil
}

// GetProviderByName gets a provider by name
func (s *ProviderService) GetProviderByName(name string) (Provider, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// First check named providers
	if provider, exists := s.namedProviders[name]; exists {
		return provider, nil
	}

	// Then check regular providers by name
	for _, provider := range s.providers {
		if provider.Name() == name {
			return provider, nil
		}
	}

	return nil, fmt.Errorf("provider with name %s not found", name)
}

// ListProviderNames lists all registered provider names
func (s *ProviderService) ListProviderNames() []string {
	s.mu.RLock()
	defer s.mu.RUnlock()

	names := make([]string, 0, len(s.namedProviders)+len(s.providers))

	// Add named providers
	for name := range s.namedProviders {
		names = append(names, name)
	}

	// Add regular providers
	for _, provider := range s.providers {
		names = append(names, provider.Name())
	}

	return names
}
