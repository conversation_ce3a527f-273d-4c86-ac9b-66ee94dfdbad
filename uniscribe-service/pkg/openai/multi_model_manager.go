package openai

import (
	"context"
	"fmt"
	"log"

	"uniscribe-service/pkg/openai/providers"

	"github.com/sashabaranov/go-openai"
)

// ChatCompletionResult 包含响应和实际使用的 provider 信息
type ChatCompletionResult struct {
	Response     openai.ChatCompletionResponse
	ProviderName string // 实际使用的 provider 名称，如 "azure-paid", "openai-fallback"
}

// UserTier represents different user tiers
type UserTier string

const (
	TierFree UserTier = "free"
	TierPaid UserTier = "paid"
)

// MultiModelManager manages multiple models for different user tiers with failover
type MultiModelManager struct {
	service         *providers.ProviderService
	primaryProviders map[UserTier]string  // tier -> provider name
	fallbackProvider string               // fallback provider name
}

// NewMultiModelManager creates a new multi-model manager
func NewMultiModelManager() *MultiModelManager {
	return &MultiModelManager{
		service:          GetService(),
		primaryProviders: make(map[UserTier]string),
	}
}

// RegisterTierProvider registers a provider for a specific user tier
func (m *MultiModelManager) RegisterTierProvider(tier UserTier, providerName string, provider providers.Provider) error {
	// Register the provider with a name
	if err := m.service.RegisterProviderWithName(providerName, provider); err != nil {
		return fmt.Errorf("failed to register provider %s: %w", providerName, err)
	}

	// Map the tier to the provider
	m.primaryProviders[tier] = providerName
	log.Printf("Registered provider %s for tier %s", providerName, tier)

	return nil
}

// SetFallbackProvider sets the fallback provider
func (m *MultiModelManager) SetFallbackProvider(providerName string, provider providers.Provider) error {
	// Register the fallback provider
	if err := m.service.RegisterProviderWithName(providerName, provider); err != nil {
		return fmt.Errorf("failed to register fallback provider %s: %w", providerName, err)
	}

	m.fallbackProvider = providerName
	log.Printf("Set fallback provider: %s", providerName)

	return nil
}

// GetProviderForTier gets the appropriate provider for a user tier with failover
func (m *MultiModelManager) GetProviderForTier(tier UserTier) (providers.Provider, error) {
	// Try primary provider first
	if primaryProviderName, exists := m.primaryProviders[tier]; exists {
		if provider, err := m.service.GetProviderByName(primaryProviderName); err == nil {
			return provider, nil
		} else {
			log.Printf("Primary provider %s for tier %s failed: %v", primaryProviderName, tier, err)
		}
	}

	// Fallback to fallback provider
	if m.fallbackProvider != "" {
		if provider, err := m.service.GetProviderByName(m.fallbackProvider); err == nil {
			log.Printf("Using fallback provider %s for tier %s", m.fallbackProvider, tier)
			return provider, nil
		} else {
			log.Printf("Fallback provider %s failed: %v", m.fallbackProvider, err)
		}
	}

	return nil, fmt.Errorf("no available provider for tier %s", tier)
}

// CreateChatCompletionForTier creates a chat completion for a specific user tier with automatic failover
func (m *MultiModelManager) CreateChatCompletionForTier(ctx context.Context, tier UserTier, request openai.ChatCompletionRequest) (openai.ChatCompletionResponse, error) {
	result, err := m.CreateChatCompletionWithProviderInfo(ctx, tier, request)
	if err != nil {
		return openai.ChatCompletionResponse{}, err
	}
	return result.Response, nil
}

// CreateChatCompletionWithProviderInfo creates a chat completion and returns provider information
func (m *MultiModelManager) CreateChatCompletionWithProviderInfo(ctx context.Context, tier UserTier, request openai.ChatCompletionRequest) (*ChatCompletionResult, error) {
	return m.createChatCompletionWithFailover(ctx, tier, request)
}

// createChatCompletionWithFailover implements the failover logic similar to STT service
func (m *MultiModelManager) createChatCompletionWithFailover(ctx context.Context, tier UserTier, request openai.ChatCompletionRequest) (*ChatCompletionResult, error) {
	var firstErr error

	// 1. Try primary provider first
	if primaryProviderName, exists := m.primaryProviders[tier]; exists {
		response, err := m.tryChatCompletion(ctx, primaryProviderName, request)
		if err == nil {
			return &ChatCompletionResult{
				Response:     response,
				ProviderName: primaryProviderName,
			}, nil
		}

		// Record first error for reporting
		firstErr = fmt.Errorf("primary provider %s for tier %s failed: %w", primaryProviderName, tier, err)
		log.Printf("Primary provider %s for tier %s failed: %v", primaryProviderName, tier, err)
	}

	// 2. Try fallback provider
	if m.fallbackProvider != "" {
		log.Printf("Trying fallback provider %s for tier %s", m.fallbackProvider, tier)
		response, err := m.tryChatCompletion(ctx, m.fallbackProvider, request)
		if err == nil {
			log.Printf("✅ Fallback provider %s succeeded for tier %s", m.fallbackProvider, tier)
			return &ChatCompletionResult{
				Response:     response,
				ProviderName: m.fallbackProvider,
			}, nil
		}
		log.Printf("Fallback provider %s also failed: %v", m.fallbackProvider, err)
	}

	// 3. All providers failed
	if firstErr != nil {
		return nil, fmt.Errorf("all providers failed for tier %s, first error: %w", tier, firstErr)
	}
	return nil, fmt.Errorf("no available provider for tier %s", tier)
}

// tryChatCompletion attempts to create a chat completion with a specific provider
func (m *MultiModelManager) tryChatCompletion(ctx context.Context, providerName string, request openai.ChatCompletionRequest) (openai.ChatCompletionResponse, error) {
	provider, err := m.service.GetProviderByName(providerName)
	if err != nil {
		return openai.ChatCompletionResponse{}, fmt.Errorf("provider %s not available: %w", providerName, err)
	}

	return provider.CreateChatCompletion(ctx, request)
}

// CreateTranscriptionForTier creates a transcription for a specific user tier with automatic failover
func (m *MultiModelManager) CreateTranscriptionForTier(ctx context.Context, tier UserTier, request openai.AudioRequest) (openai.AudioResponse, error) {
	return m.createTranscriptionWithFailover(ctx, tier, request)
}

// createTranscriptionWithFailover implements the failover logic for transcription
func (m *MultiModelManager) createTranscriptionWithFailover(ctx context.Context, tier UserTier, request openai.AudioRequest) (openai.AudioResponse, error) {
	var firstErr error

	// 1. Try primary provider first
	if primaryProviderName, exists := m.primaryProviders[tier]; exists {
		response, err := m.tryTranscription(ctx, primaryProviderName, request)
		if err == nil {
			return response, nil
		}

		// Record first error for reporting
		firstErr = fmt.Errorf("primary provider %s for tier %s transcription failed: %w", primaryProviderName, tier, err)
		log.Printf("Primary provider %s for tier %s transcription failed: %v", primaryProviderName, tier, err)
	}

	// 2. Try fallback provider
	if m.fallbackProvider != "" {
		log.Printf("Trying fallback provider %s for tier %s transcription", m.fallbackProvider, tier)
		response, err := m.tryTranscription(ctx, m.fallbackProvider, request)
		if err == nil {
			log.Printf("✅ Fallback provider %s succeeded for tier %s transcription", m.fallbackProvider, tier)
			return response, nil
		}
		log.Printf("Fallback provider %s transcription also failed: %v", m.fallbackProvider, err)
	}

	// 3. All providers failed
	if firstErr != nil {
		return openai.AudioResponse{}, fmt.Errorf("all providers failed for tier %s transcription, first error: %w", tier, firstErr)
	}
	return openai.AudioResponse{}, fmt.Errorf("no available provider for tier %s transcription", tier)
}

// tryTranscription attempts to create a transcription with a specific provider
func (m *MultiModelManager) tryTranscription(ctx context.Context, providerName string, request openai.AudioRequest) (openai.AudioResponse, error) {
	provider, err := m.service.GetProviderByName(providerName)
	if err != nil {
		return openai.AudioResponse{}, fmt.Errorf("provider %s not available: %w", providerName, err)
	}

	return provider.CreateTranscription(ctx, request)
}

// GetStatus returns the status of all providers
func (m *MultiModelManager) GetStatus() map[string]string {
	status := make(map[string]string)
	
	// Check primary providers
	for tier, providerName := range m.primaryProviders {
		if provider, err := m.service.GetProviderByName(providerName); err == nil {
			// Try a simple health check
			ctx := context.Background()
			if err := provider.HealthCheck(ctx); err == nil {
				status[fmt.Sprintf("%s_primary", tier)] = "healthy"
			} else {
				status[fmt.Sprintf("%s_primary", tier)] = "unhealthy"
			}
		} else {
			status[fmt.Sprintf("%s_primary", tier)] = "not_found"
		}
	}

	// Check fallback provider
	if m.fallbackProvider != "" {
		if provider, err := m.service.GetProviderByName(m.fallbackProvider); err == nil {
			ctx := context.Background()
			if err := provider.HealthCheck(ctx); err == nil {
				status["fallback"] = "healthy"
			} else {
				status["fallback"] = "unhealthy"
			}
		} else {
			status["fallback"] = "not_found"
		}
	}

	return status
}
