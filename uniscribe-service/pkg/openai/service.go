package openai

import (
	"context"
	"log"
	"sync"
	"time"

	"uniscribe-service/internal/config"
	"uniscribe-service/pkg/openai/providers"

	"github.com/sashabaranov/go-openai"
)

var (
	globalService      *providers.ProviderService
	globalMultiManager *MultiModelManager
	once               sync.Once
	multiOnce          sync.Once
)

// GetService returns the global provider service instance
func GetService() *providers.ProviderService {
	once.Do(func() {
		globalService = providers.NewProviderService()
		initializeProviders()
	})
	return globalService
}

// GetDefaultProvider returns the default OpenAI provider
func GetDefaultProvider() providers.Provider {
	return GetService().GetDefaultProvider()
}

// GetProvider returns a specific provider by type
func GetProvider(providerType providers.ProviderType) (providers.Provider, error) {
	return GetService().GetProvider(providerType)
}

// CreateChatCompletionForTier creates a chat completion using the appropriate provider based on configuration
// This method automatically handles both multi-model and single-provider modes
func CreateChatCompletionForTier(ctx context.Context, tier UserTier, request openai.ChatCompletionRequest) (openai.ChatCompletionResponse, error) {
	if config.Cfg.OpenaiMultiModelEnabled {
		// Multi-model mode: use the multi-model manager
		manager := GetMultiModelManager()
		return manager.CreateChatCompletionForTier(ctx, tier, request)
	} else {
		// Single-provider mode: use the default provider (ignores tier)
		return GetDefaultProvider().CreateChatCompletion(ctx, request)
	}
}

// CreateChatCompletionWithProviderInfo creates a chat completion and returns provider information
func CreateChatCompletionWithProviderInfo(ctx context.Context, tier UserTier, request openai.ChatCompletionRequest) (*ChatCompletionResult, error) {
	if config.Cfg.OpenaiMultiModelEnabled {
		// Multi-model mode: use the multi-model manager
		manager := GetMultiModelManager()
		return manager.CreateChatCompletionWithProviderInfo(ctx, tier, request)
	} else {
		// Single-provider mode: use the default provider (ignores tier)
		response, err := GetDefaultProvider().CreateChatCompletion(ctx, request)
		if err != nil {
			return nil, err
		}
		return &ChatCompletionResult{
			Response:     response,
			ProviderName: "default", // 单一模式下的默认 provider
		}, nil
	}
}

// GetMultiModelManager returns the global multi-model manager
func GetMultiModelManager() *MultiModelManager {
	multiOnce.Do(func() {
		globalMultiManager = NewMultiModelManager()
		if config.Cfg.OpenaiMultiModelEnabled {
			initializeMultiModelManager()
		}
	})
	return globalMultiManager
}

// initializeProviders initializes all available providers based on configuration
func initializeProviders() {
	service := globalService

	if config.Cfg.OpenaiApiKey == "" {
		log.Printf("OPENAI_API_KEY not set, no OpenAI providers will be initialized")
		return
	}

	// Parse configured provider type
	configuredProviderType, err := providers.ParseProviderType(config.Cfg.OpenaiProvider)
	if err != nil {
		log.Printf("Invalid OPENAI_PROVIDER '%s', falling back to openai: %v", config.Cfg.OpenaiProvider, err)
		configuredProviderType = providers.OpenAIOfficial
	}

	log.Printf("OpenAI provider configuration: type=%s", configuredProviderType)
	if config.Cfg.ProxyURLStr != "" {
		log.Printf("OpenAI provider proxy: %s", config.Cfg.ProxyURLStr)
	}

	// Initialize the configured provider as default
	var defaultProvider providers.Provider
	switch configuredProviderType {
	case providers.OpenAIOfficial:
		defaultProvider, err = providers.NewOpenAIProvider(config.Cfg.OpenaiApiKey, config.Cfg.ProxyURLStr)
	case providers.AzureOpenAI:
		// azure 不需要 proxy
		defaultProvider, err = providers.NewAzureOpenAIProvider(
			config.Cfg.OpenaiAzureApiKey,
			config.Cfg.OpenaiAzureResourceName,
			config.Cfg.OpenaiAzureDeploymentName,
		)
	case providers.Custom:
		defaultProvider, err = providers.NewCustomProvider(
			"Custom OpenAI Provider",
			config.Cfg.OpenaiApiKey,
			config.Cfg.OpenaiCustomBaseURL,
			config.Cfg.ProxyURLStr,
		)
	}

	if err != nil {
		log.Fatalf("Failed to initialize configured OpenAI provider '%s': %v", configuredProviderType, err)
	}

	// Register the configured provider
	if err := service.RegisterProvider(configuredProviderType, defaultProvider); err != nil {
		log.Fatalf("Failed to register configured OpenAI provider '%s': %v", configuredProviderType, err)
	}

	log.Printf("OpenAI provider ready: %s", defaultProvider.Name())

	// Perform health check on the default provider if enabled
	if config.Cfg.OpenaiHealthCheckEnabled {
		performHealthCheck(defaultProvider)
	} else {
		log.Printf("Health check disabled for provider: %s", defaultProvider.Name())
	}

	// Optionally initialize other providers for fallback (can be enabled via additional config)
	initializeAdditionalProviders(service, configuredProviderType)
}

// initializeAdditionalProviders initializes additional providers for fallback
func initializeAdditionalProviders(service *providers.ProviderService, skipType providers.ProviderType) {
	// This can be extended to initialize additional providers based on configuration
	// For now, we only initialize the configured provider to keep it simple
}

// initializeMultiModelManager initializes the multi-model manager with tier-specific providers
func initializeMultiModelManager() {
	log.Println("Initializing multi-model manager...")

	manager := globalMultiManager

	// Debug: Log Azure configuration
	log.Printf("Azure configuration check:")
	log.Printf("  - OpenaiAzureResourceName: %s", config.Cfg.OpenaiAzureResourceName)
	log.Printf("  - OpenaiAzurePaidDeployment: %s", config.Cfg.OpenaiAzurePaidDeployment)
	log.Printf("  - OpenaiAzureFreeDeployment: %s", config.Cfg.OpenaiAzureFreeDeployment)
	if config.Cfg.OpenaiAzureApiKey != "" {
		log.Printf("  - OpenaiAzureApiKey: [SET]")
	} else {
		log.Printf("  - OpenaiAzureApiKey: [NOT SET]")
	}

	// Initialize Azure providers for different tiers
	if config.Cfg.OpenaiAzureResourceName != "" {
		log.Printf("Azure resource name found, proceeding with Azure provider initialization...")
		// Paid tier provider (GPT-4)
		if config.Cfg.OpenaiAzurePaidDeployment != "" {
			paidProvider, err := providers.NewAzureOpenAIProvider(
				config.Cfg.OpenaiAzureApiKey,
				config.Cfg.OpenaiAzureResourceName,
				config.Cfg.OpenaiAzurePaidDeployment,
			)
			if err != nil {
				log.Fatalf("Failed to create paid tier provider: %v", err)
			}

			if err := manager.RegisterTierProvider(TierPaid, "azure-paid", paidProvider); err != nil {
				log.Fatalf("Failed to register paid tier provider: %v", err)
			}

			// Perform health check on the paid tier provider if enabled
			if config.Cfg.OpenaiHealthCheckEnabled {
				performHealthCheck(paidProvider)
			}
		}

		// Free tier provider (GPT-4.1-mini)
		if config.Cfg.OpenaiAzureFreeDeployment != "" {
			freeProvider, err := providers.NewAzureOpenAIProvider(
				config.Cfg.OpenaiAzureApiKey,
				config.Cfg.OpenaiAzureResourceName,
				config.Cfg.OpenaiAzureFreeDeployment,
			)
			if err != nil {
				log.Fatalf("Failed to create free tier provider: %v", err)
			}

			if err := manager.RegisterTierProvider(TierFree, "azure-free", freeProvider); err != nil {
				log.Fatalf("Failed to register free tier provider: %v", err)
			}

			// Perform health check on the free tier provider if enabled
			if config.Cfg.OpenaiHealthCheckEnabled {
				performHealthCheck(freeProvider)
			}
		}
	}

	// Initialize fallback provider (OpenAI Official GPT-4.1-mini)
	fallbackProvider, err := providers.NewOpenAIProvider(config.Cfg.OpenaiApiKey, config.Cfg.ProxyURLStr)
	if err != nil {
		log.Fatalf("Failed to create fallback provider: %v", err)
	}

	if err := manager.SetFallbackProvider("openai-fallback", fallbackProvider); err != nil {
		log.Fatalf("Failed to set fallback provider: %v", err)
	}

	// Perform health check on the fallback provider if enabled
	if config.Cfg.OpenaiHealthCheckEnabled {
		performHealthCheck(fallbackProvider)
	} else {
		log.Printf("Azure resource name not found, skipping Azure provider initialization")
	}

	log.Println("Multi-model manager initialized")
}

// performHealthCheck performs a health check on the provider
func performHealthCheck(provider providers.Provider) {
	// Create context with timeout for health check
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()

	// Perform health check (the provider will log the details)
	if err := provider.HealthCheck(ctx); err != nil {
		log.Printf("⚠️  Provider may not be accessible, but system will continue to operate")
	}
}
