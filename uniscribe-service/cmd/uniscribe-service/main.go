package main

import (
	"context"
	"log"
	"net/http"
	_ "net/http/pprof"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"github.com/getsentry/sentry-go"

	"uniscribe-service/internal/config"
	"uniscribe-service/internal/handlers"
	"uniscribe-service/internal/queue"
	sentryutil "uniscribe-service/internal/sentry"
	openaipkg "uniscribe-service/pkg/openai"
)

func main() {
	// 初始化任务队列
	log.Printf("uniscribe-service started with PID: %d\n", os.Getpid())
	config.LoadConfig()

	// 初始化 OpenAI provider 系统
	_ = openaipkg.GetService()

	// 初始化 Multi-model manager（如果启用）
	if config.Cfg.OpenaiMultiModelEnabled {
		log.Printf("Multi-model enabled, initializing multi-model manager...")
		_ = openaipkg.GetMultiModelManager()
	} else {
		log.Printf("Multi-model disabled, skipping multi-model manager initialization")
	}

	// 启动 pprof 服务器用于性能分析
	go func() {
		log.Println("Starting pprof server on :6060")
		log.Println(http.ListenAndServe("localhost:6060", nil))
	}()

	// 初始化 Sentry（仅在生产环境）
	if config.IsProductionEnv() {
		err := sentry.Init(sentry.ClientOptions{
			Dsn:              "https://<EMAIL>/4508829186064384",
			TracesSampleRate: 1.0,
		})
		if err != nil {
			log.Fatalf("sentry.Init: %s", err)
		}
		log.Println("Sentry initialized for production environment")
	} else {
		log.Printf("Sentry disabled in %s environment", config.Cfg.Environment)
	}

	MAX_AUDIO_TASK_COUNT := 10
	MAX_TEXT_TASK_COUNT := MAX_AUDIO_TASK_COUNT * 3
	MAX_SPEAKER_DIARIZATION_TASK_COUNT := 3 // 设置较小的并发度

	// 初始化 Redis Streams 消费者（同时支持两种模式）
	log.Println("Starting dual mode: HTTP polling + Redis Streams")
	redisConsumer, err := queue.NewRedisStreamConsumer()
	if err != nil {
		log.Fatalf("Failed to create Redis consumer: %v", err)
	}
	defer redisConsumer.Close()

	resultSender := handlers.NewHTTPResultSender(config.Cfg.WebBackendHost)
	audioTaskProcessor := handlers.NewAudioTaskProcessor(resultSender)
	textTaskProcessor := handlers.NewTextTaskProcessor(resultSender)
	speakerDiarizationProcessor := handlers.NewSpeakerDiarizationProcessor(resultSender)

	audioTaskQueue := make(chan handlers.Task, MAX_AUDIO_TASK_COUNT)
	textTaskQueue := make(chan handlers.Task, MAX_TEXT_TASK_COUNT)
	speakerDiarizationTaskQueue := make(chan handlers.Task, MAX_SPEAKER_DIARIZATION_TASK_COUNT)

	// 创建一个 context，用于控制 goroutine 的生命周期
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 创建一个 WaitGroup 来等待所有 goroutine 完成
	var wg sync.WaitGroup

	// 启动多个音频处理任务的 goroutine
	for i := range make([]struct{}, MAX_AUDIO_TASK_COUNT) {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			log.Printf("Starting audio processor #%d", id)
			processAudioTasks(ctx, audioTaskProcessor, audioTaskQueue)
		}(i)
	}

	// 启动多个文本处理任务的 goroutine
	for i := range make([]struct{}, MAX_TEXT_TASK_COUNT) {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			log.Printf("Starting text processor #%d", id)
			processTextTasks(ctx, textTaskProcessor, textTaskQueue)
		}(i)
	}

	// 启动多个说话人识别任务的 goroutine
	for i := range make([]struct{}, MAX_SPEAKER_DIARIZATION_TASK_COUNT) {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			log.Printf("Starting speaker diarization processor #%d", id)
			processSpeakerDiarizationTasks(ctx, speakerDiarizationProcessor, speakerDiarizationTaskQueue)
		}(i)
	}

	// 设置优雅退出
	shutdown := make(chan os.Signal, 1)
	signal.Notify(shutdown, os.Interrupt, syscall.SIGTERM)

	// 启动 Redis Streams 消费者
	go redisConsumer.ConsumeTranscriptionTasks(ctx, audioTaskQueue)
	go redisConsumer.ConsumeTextTasks(ctx, textTaskQueue)
	go redisConsumer.ConsumeSpeakerDiarizationTasks(ctx, speakerDiarizationTaskQueue)

	// 等待关闭信号
	<-shutdown
	log.Println("uniscribe-service shutdown signal received")
	// 取消 context， 让所有 goroutine 退出
	cancel()

	// 关闭任务队列
	close(audioTaskQueue)
	close(textTaskQueue)
	close(speakerDiarizationTaskQueue)

	// sentry flush (only in production)
	sentryutil.Flush(2 * time.Second)

	wg.Wait()
	log.Println("uniscribe-service shutdown complete")
}
func processAudioTasks(ctx context.Context, processor handlers.AudioTaskProcessor, taskQueue <-chan handlers.Task) {
	for {
		select {
		case <-ctx.Done():
			log.Println("processAudioTasks context done")
			return
		case task, ok := <-taskQueue:
			if !ok {
				return
			}
			if err := processor.Process(ctx, task); err != nil {
				sentryutil.CaptureException(err)
				log.Printf("failed to process audio task: %v", err)
			}
		}
	}
}

func processTextTasks(ctx context.Context, processor handlers.TextTaskProcessor, taskQueue <-chan handlers.Task) {
	for {
		select {
		case <-ctx.Done():
			log.Println("processTextTasks context done")
			return
		case task, ok := <-taskQueue:
			if !ok {
				return
			}
			if err := processor.Process(ctx, task); err != nil {
				sentryutil.CaptureException(err)
				log.Printf("failed to process text task: %v", err)
			}
		}
	}
}

func processSpeakerDiarizationTasks(ctx context.Context, processor handlers.SpeakerDiarizationProcessor, taskQueue <-chan handlers.Task) {
	for {
		select {
		case <-ctx.Done():
			log.Println("processSpeakerDiarizationTasks context done")
			return
		case task, ok := <-taskQueue:
			if !ok {
				return
			}
			if err := processor.Process(ctx, task); err != nil {
				sentryutil.CaptureException(err)
				log.Printf("failed to process speaker diarization task: %v", err)
			}
		}
	}
}


