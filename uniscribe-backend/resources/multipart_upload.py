import base64
import logging
import mimetypes
from flask import g, current_app
from flask_restful import Resource, abort, marshal_with
from datetime import datetime

from constants.storage import EXPIRE_FOR_UPLOAD_URL
from constants.task import TranscriptionType
from controllers.transcription import (
    create_transcription_file,
    complete_upload,
    format_transcription_file,
    check_free_user_transcribe_limit,
)
from controllers.usage import check_transcription_quota
from exceptions.storage import FileAlreadyExistsError
from fields.transcription import transcription_file_fields
from libs import reqparse
from models.multipart_upload import MultipartUpload, MultipartUploadStatus
from models.transcription_file import TranscriptionFile
from models import db, insert_record
from resources.auth import auth_required
from constants.transcription import MAX_FILENAME_LENGTH

logger = logging.getLogger(__name__)


class CreateMultipartUploadResource(Resource):
    @auth_required
    def post(self):
        """创建分块上传"""
        user_id = g.user.id
        parser = reqparse.RequestParser()

        # 解析请求参数（与现有上传接口保持一致）
        filename = parser.get_argument(
            "filename", type=str, required=True, location="json"
        )
        file_type = parser.get_argument(
            "fileType", type=str, required=True, location="json"
        )
        file_size = parser.get_argument(
            "fileSize", type=int, required=True, location="json"
        )
        content_md5_base_64 = parser.get_argument(
            "contentMd5Base64", type=str, required=True, location="json"
        )
        duration = parser.get_argument(
            "duration", type=float, required=False, location="json", default=0.0
        )
        force_upload = parser.get_argument(
            "forceUpload", type=bool, required=False, location="json", default=False
        )
        language_code = parser.get_argument(
            "languageCode", type=str, required=False, location="json"
        )
        transcription_type = parser.get_argument(
            "transcriptionType",
            type=str,
            required=False,
            location="json",
            default="transcript",
        )
        enable_speaker_diarization = parser.get_argument(
            "enableSpeakerDiarization",
            type=bool,
            required=False,
            location="json",
            default=False,
        )
        folder_id = parser.get_argument(
            "folderId", type=str, required=False, location="json"
        )
        needs_preprocessing = parser.get_argument(
            "needsPreprocessing", type=bool, required=False, location="json", default=False
        )

        # 转换参数
        if transcription_type == "subtitle":
            transcription_type = TranscriptionType.SUBTITLE
        else:
            transcription_type = TranscriptionType.TRANSCRIPT

        if folder_id:
            try:
                folder_id = int(folder_id)
            except ValueError:
                folder_id = None

        # 计算文件指纹
        fingerprint = base64.b64decode(content_md5_base_64).hex()

        # 检查文件名长度并截断
        if len(filename) > MAX_FILENAME_LENGTH:
            logger.warning(f"Filename too long ({len(filename)} chars), truncating to {MAX_FILENAME_LENGTH} chars: {filename}")
            filename = filename[:MAX_FILENAME_LENGTH]

        # 检查免费用户限制
        check_free_user_transcribe_limit(g.user)

        # 如果需要预处理，跳过配额检查，在预处理完成后再检查
        if needs_preprocessing:
            logger.info(f"File {filename} needs preprocessing (requested by frontend) - skipping quota check until duration is known")
        else:
            # 只有在不需要预处理时才检查配额
            check_transcription_quota(user_id, duration)

        # 检查是否已存在相同文件（除非强制上传）
        if not force_upload:
            existing_file = TranscriptionFile.query.filter_by(
                user_id=user_id, fingerprint=fingerprint, is_deleted=False
            ).first()
            if existing_file:
                raise FileAlreadyExistsError("File already exists")

        # 检查是否有活跃的分块上传会话（支持断点续传）
        existing_upload = MultipartUpload.get_active_by_user_and_fingerprint(
            user_id, fingerprint
        )
        if existing_upload and not force_upload:
            return {
                "uploadId": existing_upload.upload_id,
                "transcriptionFileId": str(existing_upload.transcription_file_id),
                "key": existing_upload.file_key,
                "resumable": True,
            }

        # 创建转录文件记录
        tf = create_transcription_file(
            user_id,
            filename,
            file_type,
            file_size,
            fingerprint,
            duration if not needs_preprocessing else 0.0,  # 如果需要预处理，设为0
            language_code,
            transcription_type,
            enable_speaker_diarization=enable_speaker_diarization,
            folder_id=folder_id,
            needs_preprocessing=needs_preprocessing,
        )

        # 创建分块上传
        storage = current_app.storage
        content_type = mimetypes.guess_type(f"file.{file_type}")[0]

        try:
            upload_id = storage.create_multipart_upload(tf.file_key, content_type)
            logger.info(f"Created multipart upload: {upload_id}")
        except Exception as e:
            logger.exception(f"Failed to create multipart upload: {e}")
            abort(500, message="Failed to create multipart upload")

        # 保存分块上传会话
        multipart_upload = MultipartUpload(
            upload_id=upload_id,
            transcription_file_id=tf.id,
            user_id=user_id,
            file_key=tf.file_key,
            filename=filename,
            file_type=file_type,
            file_size=file_size,
            fingerprint=fingerprint,
            status=MultipartUploadStatus.ACTIVE.value,
        )
        insert_record(multipart_upload)
        db.session.commit()

        return {
            "uploadId": upload_id,
            "transcriptionFileId": str(tf.id),
            "key": tf.file_key,
            "resumable": False,
        }


class ListMultipartPartsResource(Resource):
    @auth_required
    def get(self, upload_id):
        """列出已上传的分块"""
        user_id = g.user.id

        # 验证上传会话
        multipart_upload = MultipartUpload.get_by_upload_id(upload_id)
        if not multipart_upload:
            abort(404, message="Upload session not found")

        if multipart_upload.user_id != user_id:
            abort(403, message="Access denied")

        if multipart_upload.status != MultipartUploadStatus.ACTIVE.value:
            abort(400, message="Upload session is not active")

        # 获取已上传的分块
        storage = current_app.storage
        try:
            parts = storage.list_multipart_parts(multipart_upload.file_key, upload_id)
            return {"parts": parts}
        except Exception as e:
            logger.exception(f"Failed to list multipart parts: {e}")
            abort(500, message="Failed to list uploaded parts")


class SignMultipartPartResource(Resource):
    @auth_required
    def post(self, upload_id, part_number):
        """为分块生成预签名URL"""
        user_id = g.user.id

        # 验证分块编号
        try:
            part_number = int(part_number)
            if part_number < 1 or part_number > 10000:
                abort(400, message="Invalid part number")
        except ValueError:
            abort(400, message="Invalid part number")

        # 验证上传会话
        multipart_upload = MultipartUpload.get_by_upload_id(upload_id)
        if not multipart_upload:
            abort(404, message="Upload session not found")

        if multipart_upload.user_id != user_id:
            abort(403, message="Access denied")

        if multipart_upload.status != MultipartUploadStatus.ACTIVE.value:
            abort(400, message="Upload session is not active")

        # 生成预签名URL
        storage = current_app.storage
        try:
            signed_url = storage.generate_presigned_url_for_multipart_part(
                multipart_upload.file_key, upload_id, part_number, EXPIRE_FOR_UPLOAD_URL
            )
            return {"signedUrl": signed_url}
        except Exception as e:
            logger.exception(f"Failed to generate signed URL for part: {e}")
            abort(500, message="Failed to generate signed URL")


class CompleteMultipartUploadResource(Resource):
    @auth_required
    @marshal_with(transcription_file_fields)
    def post(self, upload_id):
        """完成分块上传"""
        user_id = g.user.id
        parser = reqparse.RequestParser()
        parts = parser.get_argument("parts", type=list, required=True, location="json")

        # 验证上传会话
        multipart_upload = MultipartUpload.get_by_upload_id(upload_id)
        if not multipart_upload:
            abort(404, message="Upload session not found")

        if multipart_upload.user_id != user_id:
            abort(403, message="Access denied")

        if multipart_upload.status != MultipartUploadStatus.ACTIVE.value:
            abort(400, message="Upload session is not active")

        # 完成分块上传
        storage = current_app.storage
        try:
            storage.complete_multipart_upload(
                multipart_upload.file_key, upload_id, parts
            )
        except Exception as e:
            logger.exception(f"Failed to complete multipart upload: {e}")
            abort(500, message="Failed to complete upload")

        # 更新上传会话状态
        multipart_upload.mark_completed()
        db.session.commit()

        # 完成转录文件上传
        transcription_file = complete_upload(
            user_id, str(multipart_upload.transcription_file_id)
        )
        transcription_file = format_transcription_file(transcription_file)
        return transcription_file


class AbortMultipartUploadResource(Resource):
    @auth_required
    def post(self, upload_id):
        """取消分块上传"""
        user_id = g.user.id

        # 验证上传会话
        multipart_upload = MultipartUpload.get_by_upload_id(upload_id)
        if not multipart_upload:
            abort(404, message="Upload session not found")

        if multipart_upload.user_id != user_id:
            abort(403, message="Access denied")

        if multipart_upload.status != MultipartUploadStatus.ACTIVE.value:
            abort(400, message="Upload session is not active")

        # 取消分块上传
        storage = current_app.storage
        try:
            storage.abort_multipart_upload(multipart_upload.file_key, upload_id)
        except Exception as e:
            logger.exception(f"Failed to abort multipart upload: {e}")
            # 即使S3操作失败，也要更新数据库状态

        # 更新上传会话状态
        multipart_upload.mark_aborted()
        db.session.commit()

        return {"message": "Upload aborted successfully"}
