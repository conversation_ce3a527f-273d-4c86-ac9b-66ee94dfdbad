import logging
import re
from flask import current_app, g
from flask_restful import Resource, abort, marshal_with

from constants.youtube_transcript import AVAILABLE_FORMATS
from constants.task import TranscriptionType
from constants.transcription import TranscriptionFileStatus
from constants.storage import EXPIRE_FOR_READ_URL
from exceptions.youtube import (
    YoutubeSubtitleNotFoundError,
    YoutubeSubtitleLiveChatOnlyError,
)
from libs.reqparse import RequestParser
from libs.cache import RedisCache
from resources.auth import auth_required
from controllers.youtube import (
    YoutubeInfoExtractor,
    YoutubeTranscriber,
    YoutubeTranscriptExtractor,
    YoutubeTranscriptManager,
    YoutubeTranscript,
)
from controllers.task import  create_media_preprocessing_task
from controllers.export import create_download_response
from controllers.usage import check_transcription_quota
from controllers.transcription import check_free_user_transcribe_limit
from fields.transcription import transcription_file_fields



logger = logging.getLogger(__name__)


def youtube_url_validator(url: str):
    """
    验证YouTube URL格式并提取视频ID
    支持的格式：
    - youtube.com/watch?v=VIDEO_ID
    - youtu.be/VIDEO_ID
    - youtube.com/shorts/VIDEO_ID
    - youtube.com/embed/VIDEO_ID
    - youtube.com/live/VIDEO_ID
    - 支持 www. 和 m. 前缀
    """
    patterns = [
        # youtube.com/watch?v=VIDEO_ID (支持 www. 和 m. 前缀)
        r"^(?:https?://)?(?:(?:www\.|m\.)?youtube\.com/watch\?(?=.*v=([A-Za-z0-9_-]{11,}))(?:\S+)?)$",
        # youtu.be/VIDEO_ID
        r"^(?:https?://)?(?:www\.)?youtu\.be/([A-Za-z0-9_-]{11,})(?:\?.*)?$",
        # youtube.com/embed/VIDEO_ID (支持 www. 和 m. 前缀)
        r"^(?:https?://)?(?:(?:www\.|m\.)?youtube\.com/embed/([A-Za-z0-9_-]{11,})(?:\?.*)?)$",
        # youtube.com/shorts/VIDEO_ID (支持 www. 和 m. 前缀)
        r"^(?:https?://)?(?:(?:www\.|m\.)?youtube\.com/shorts/([A-Za-z0-9_-]{11,})(?:\?.*)?)$",
        # youtube.com/live/VIDEO_ID (支持 www. 和 m. 前缀)
        r"^(?:https?://)?(?:(?:www\.|m\.)?youtube\.com/live/([A-Za-z0-9_-]{11,})(?:\?.*)?)$",
    ]

    for pattern in patterns:
        match = re.match(pattern, url)
        if match:
            video_id = match.group(1)
            # 验证视频ID格式（至少11位，包含字母数字下划线连字符）
            if video_id and re.match(r"^[A-Za-z0-9_-]{11,}$", video_id):
                logger.info(
                    f"YouTube URL validation: '{url}' -> VALID (video_id: {video_id})"
                )
                return match

    logger.info(f"YouTube URL validation: '{url}' -> INVALID")
    return None


class YoutubeInfoResource(Resource):
    def __init__(self):
        self.cache = RedisCache(prefix="youtube_info:v2")

    def post(self):
        """处理下载请求"""
        parser = RequestParser()
        url = parser.get_argument("url", type=str, required=True, location="json")

        if not youtube_url_validator(url):
            abort(400, message="Invalid YouTube URL")

        return self.get_cached_info(url)

    def get_cached_info(self, url: str):
        # 尝试获取缓存
        cached_data = self.cache.get(url)
        if cached_data is not None:
            return cached_data

        # 无缓存时获取新数据
        try:
            extractor = YoutubeInfoExtractor(url)
            result = extractor.extract()
            self.cache.set(url, result, ttl=60)
            return result
        except Exception as e:
            # 如果获取信息失败，确保不会缓存错误的数据
            # 这里不需要清理缓存，因为还没有设置缓存
            logger.error(f"Failed to extract YouTube info for URL {url}: {str(e)}")
            raise

    @staticmethod
    def clear_cache(url: str):
        """清理指定URL的缓存"""
        try:
            cache = RedisCache(prefix="youtube_info:v1")
            cache.delete(url)
            logger.info(f"Cleared YouTube info cache for URL: {url}")
        except Exception as e:
            logger.warning(
                f"Failed to clear YouTube info cache for URL {url}: {str(e)}"
            )


class CreateYoutubeTranscriptionTaskResource(Resource):
    @auth_required
    @marshal_with(transcription_file_fields)
    def post(self):
        """处理音频转录请求"""
        parser = RequestParser()
        url = parser.get_argument("url", type=str, required=True, location="json")
        title = parser.get_argument("title", type=str, required=True, location="json")
        duration = parser.get_argument(
            "duration", type=int, required=True, location="json"
        )
        transcription_type = parser.get_argument(
            "transcriptionType",
            type=str,
            required=False,
            location="json",
            default=TranscriptionType.TRANSCRIPT,
        )
        language_code = parser.get_argument(
            "languageCode", type=str, required=False, location="json", default=None
        )
        enable_speaker_diarization = parser.get_argument(
            "enableSpeakerDiarization",
            type=bool,
            required=False,
            location="json",
            default=False,
        )
        folder_id = parser.get_argument(
            "folderId", type=int, required=False, location="json", default=None
        )

        # 验证文件夹归属权限（如果指定了文件夹）
        if folder_id is not None:
            from models.folder import Folder

            folder = Folder.get_by_id(folder_id)
            if not folder or folder.user_id != g.user.id:
                abort(403, message="Invalid folder or folder does not belong to user")

        if not youtube_url_validator(url):
            abort(400, message="Invalid YouTube URL")

        # 检查免费用户限制
        check_free_user_transcribe_limit(g.user)

        # 检查转录配额
        check_transcription_quota(g.user.id, duration)

        storage = current_app.storage
        transcriber = YoutubeTranscriber(g.user.id, url, title, duration, storage)
        tf = transcriber.create_transcription_file(
            transcription_type, language_code, enable_speaker_diarization, folder_id
        )

        # 创建媒体预处理任务（统一处理YouTube下载和预处理）
        # 注意：create_media_preprocessing_task 内部已经通过事务回调入队了，不需要再次入队
        media_task = create_media_preprocessing_task(
            g.user.id, tf.id, "youtube", "youtube_download", url
        )

        # 准备响应数据
        storage = current_app.storage
        tf.file_url = storage.generate_presigned_url_for_read(
            tf.file_key,
            EXPIRE_FOR_READ_URL,
        )
        tf.status = TranscriptionFileStatus.by_id(tf.status).name

        # 返回转录文件信息
        return tf


class YoutubeTranscriptionTaskStatusResource(Resource):
    @auth_required
    def get(self, task_id):
        """获取任务状态"""
        status = current_app.task_manager.get_task_status(task_id)
        if not status:
            abort(404, message="Task not found")
        return status


class YoutubeSubtitleListResource(Resource):
    """获取视频的所有字幕并保存到数据库"""

    def post(self):
        parser = RequestParser()
        url = parser.get_argument("url", type=str, required=True, location="json")

        if not youtube_url_validator(url):
            abort(400, message="Invalid YouTube URL")

        video_id = YoutubeTranscript.extract_video_id(url)
        if not video_id:
            abort(400, message="Invalid YouTube URL")

        # 1. 先查询数据库中已有的字幕
        existing_transcripts = YoutubeTranscript.query.filter_by(
            video_id=video_id
        ).all()

        if existing_transcripts:
            # 如果数据库中有字幕，直接返回
            return {
                "video_id": video_id,
                "subtitles": [
                    {
                        "langCode": t.lang_code,
                        "name": t.lang_name,
                        "vttContent": t.vtt_content,
                        "availableFormats": AVAILABLE_FORMATS,
                        "isAuto": t.is_auto,
                    }
                    for t in existing_transcripts
                ],
            }

        # 2. 如果数据库中没有，则从 YouTube 获取
        extractor = YoutubeTranscriptExtractor(url)
        available_subtitles = extractor.list_subtitles()

        subtitles = available_subtitles["subtitles"]

        # 过滤掉 live_chat，只保留真正的字幕
        real_subtitles = [s for s in subtitles if s["lang_code"] != "live_chat"]

        if not real_subtitles:
            # 如果过滤后没有真正的字幕
            if any(s["lang_code"] == "live_chat" for s in subtitles):
                # 但原始列表中有 live_chat
                raise YoutubeSubtitleLiveChatOnlyError(
                    message="This video only has live chat records, no subtitles are available.",
                    details={
                        "video_id": video_id,
                        "url": url,
                    },
                )
            else:
                # 完全没有任何字幕
                raise YoutubeSubtitleNotFoundError(
                    message="The video does not have any subtitles.",
                    details={
                        "video_id": video_id,
                        "url": url,
                    },
                )

        saved_subtitles = []
        for subtitle in real_subtitles:
            try:
                vtt_content = extractor.download_subtitle(subtitle["lang_code"])
                transcript = YoutubeTranscriptManager.save_transcript(
                    url,
                    subtitle["lang_code"],
                    subtitle["name"],
                    vtt_content,
                    subtitle["is_auto"],
                )
                saved_subtitles.append(
                    {
                        "langCode": transcript.lang_code,
                        "name": transcript.lang_name,
                        "vttContent": vtt_content,
                        "availableFormats": AVAILABLE_FORMATS,
                        "isAuto": transcript.is_auto,
                    }
                )
            except Exception as e:
                logger.error(
                    f"Failed to save subtitle {subtitle['lang_code']}: {str(e)}"
                )
                continue

        if not saved_subtitles:
            abort(
                404,
                message="Failed to save any subtitles",
            )

        return {"video_id": video_id, "subtitles": saved_subtitles}


class YoutubeSubtitleDownloadResource(Resource):
    """下载指定格式的字幕文件"""

    def post(self):
        parser = RequestParser()
        video_id = parser.get_argument(
            "videoId", type=str, required=True, location="json"
        )
        lang_code = parser.get_argument(
            "langCode", type=str, required=True, location="json"
        )
        format = parser.get_argument(
            "format",
            type=str,
            default="vtt",
            choices=AVAILABLE_FORMATS,
            location="json",
        )

        try:
            content = YoutubeTranscriptManager.get_transcript(
                video_id, lang_code, format
            )

            if not content:
                abort(404, message="Subtitle not found")

            filename = f"{video_id}_{lang_code}.{format}"
            return create_download_response(content.encode("utf-8"), filename)

        except ValueError as e:
            abort(400, message=str(e))
        except Exception as e:
            logger.exception(f"Failed to download subtitle: {str(e)}")
            abort(500, message=str(e))
