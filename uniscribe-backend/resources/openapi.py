"""
OpenAPI resources for external integrations (n8n, <PERSON>apier, etc.)
Provides simplified API endpoints for core transcription functionality.
"""

import hashlib
import logging
import os
import time
import urllib.parse
import uuid
from datetime import datetime, timedelta

from flask import g, request, current_app
from flask_restful import Resource, marshal

from constants.transcription import TranscriptionFileSourceType
from constants.common import DEFAULT_LIMIT, MAX_LIMIT
from constants.transcription import SUPPORTED_MEDIA_FORMATS
from constants.task import TranscriptionType
from libs.url_validator import validate_url_for_media_download, get_suggested_filename_from_url
from constants.file_storage import FileState
from controllers.transcription import (
    list_transcription_files,
    get_transcription_file_with_result,
    create_transcription_file,
)
from controllers.task import create_media_preprocessing_task
from controllers.youtube import YoutubeTranscriber
from fields.openapi import (
    openapi_transcription_fields,
    openapi_transcription_list_fields,
    openapi_status_fields,
)
from libs import reqparse
from libs.id_generator import id_generator
from models import db
from models.transcription_file import TranscriptionFile
from models.file_storage import FileStorage
from resources.openapi_auth import openapi_auth_required
from controllers.usage import check_transcription_quota_for_openapi
from libs.openapi_rate_limit import openapi_rate_limit_standard
from utils.api_response import success_response, paginated_response
from exceptions.openapi import (
    InvalidRequestParametersError,
    TranscriptionNotFoundError,
    InvalidURLError,
    URLNotAccessibleError,
    URLNotMediaFileError,
    UnsupportedURLServiceError,
)
from constants.transcription import MAX_FILENAME_LENGTH

logger = logging.getLogger(__name__)

FILE_SIZE_LIMIT_GB = 5
FILE_SIZE_LIMIT = FILE_SIZE_LIMIT_GB * 1024 * 1024 * 1024  # 5GB


class OpenAPITranscriptionListResource(Resource):
    """
    OpenAPI endpoint for listing transcriptions and creating new ones
    GET /api/v1/transcriptions - List transcriptions
    POST /api/v1/transcriptions - Create new transcription from URL
    """

    @openapi_auth_required
    @openapi_rate_limit_standard
    def get(self):
        """List user's transcriptions with pagination"""
        user_id = g.user.id
        parser = reqparse.RequestParser()

        # Cursor-based pagination
        cursor = parser.get_argument(
            "cursor", type=int, location="args", default=id_generator.get_id()
        )
        limit = parser.get_argument(
            "limit", type=int, location="args", default=DEFAULT_LIMIT
        )

        # Limit range
        limit = min(max(1, limit), MAX_LIMIT)

        result = list_transcription_files(user_id, cursor, limit)

        # Format response using OpenAPI fields
        items = [
            marshal(item, openapi_transcription_list_fields) for item in result["items"]
        ]

        return paginated_response(
            items=items,
            has_more=result["hasMore"],
            next_cursor=result.get("nextCursor")
        )

    @openapi_auth_required
    @openapi_rate_limit_standard
    def post(self):
        """Create new transcription from URL or uploaded file"""
        parser = reqparse.RequestParser()

        # Either file_url or file_key is required (mutually exclusive)
        file_url = parser.get_argument("file_url", type=str, location="json")
        file_key = parser.get_argument("file_key", type=str, location="json")
        # Required parameter: file URL
        language_code = parser.get_argument("language_code", required=True, type=str, location="json", default=None)

        # Optional parameters
        filename = parser.get_argument("filename", type=str, location="json")
        webhook_url = parser.get_argument("webhook_url", type=str, location="json")
        transcription_type = parser.get_argument(
            "transcription_type",
            type=str,
            location="json",
            default=TranscriptionType.TRANSCRIPT,
        )
        enable_speaker_diarization = parser.get_argument(
            "enable_speaker_diarization", type=bool, location="json", default=False
        )

        # Validate that exactly one of file_url or file_key is provided
        if not file_url and not file_key:
            raise InvalidRequestParametersError("Either file_url or file_key must be provided")

        if file_url and file_key:
            raise InvalidRequestParametersError("Cannot provide both file_url and file_key")
        
        # 检查转录配额 (OpenAPI users are paid users, so only check basic quota)
        # Duration is unknown for URL downloads, so check with 60s (1 minute) as estimate
        # This ensures user has at least 1 minute of quota before starting expensive download/processing
        check_transcription_quota_for_openapi(g.user.id, 60)

        try:
            if file_url:
                # Handle external URL download
                transcription_file = self._handle_url_download(
                    file_url,
                    filename,
                    webhook_url,
                    language_code,
                    transcription_type,
                    enable_speaker_diarization,
                )
            else:
                # Handle uploaded file
                transcription_file = self._handle_uploaded_file(
                    file_key,
                    webhook_url,
                    language_code,
                    transcription_type,
                    enable_speaker_diarization,
                    filename,
                )

            # 用 openapi_status_fields marshal
            return success_response(marshal(transcription_file, openapi_status_fields))

        except Exception as e:
            logger.error(f"Transcription creation failed: {str(e)}")
            # Re-raise as a generic server error - this will be handled by the exception handler
            raise



    def _is_youtube_url(self, url: str) -> bool:
        """Check if the URL is a YouTube URL"""
        import re

        patterns = [
            # youtube.com/watch?v=VIDEO_ID (支持 www. 和 m. 前缀)
            r"^(?:https?://)?(?:(?:www\.|m\.)?youtube\.com/watch\?(?=.*v=([A-Za-z0-9_-]{11,}))(?:\S+)?)$",
            # youtu.be/VIDEO_ID
            r"^(?:https?://)?(?:www\.)?youtu\.be/([A-Za-z0-9_-]{11,})(?:\?.*)?$",
            # youtube.com/embed/VIDEO_ID (支持 www. 和 m. 前缀)
            r"^(?:https?://)?(?:(?:www\.|m\.)?youtube\.com/embed/([A-Za-z0-9_-]{11,})(?:\?.*)?)$",
            # youtube.com/shorts/VIDEO_ID (支持 www. 和 m. 前缀)
            r"^(?:https?://)?(?:(?:www\.|m\.)?youtube\.com/shorts/([A-Za-z0-9_-]{11,})(?:\?.*)?)$",
            # youtube.com/live/VIDEO_ID (支持 www. 和 m. 前缀)
            r"^(?:https?://)?(?:(?:www\.|m\.)?youtube\.com/live/([A-Za-z0-9_-]{11,})(?:\?.*)?)$",
        ]

        for pattern in patterns:
            match = re.match(pattern, url)
            if match:
                video_id = match.group(1)
                # 验证视频ID格式（至少11位，包含字母数字下划线连字符）
                if video_id and re.match(r"^[A-Za-z0-9_-]{11,}$", video_id):
                    return True
        return False

    def _create_transcription_with_preprocessing(
        self,
        source_url,
        filename,
        file_type,
        file_size,
        source_type,
        preprocessing_reason,
        webhook_url,
        language_code,
        transcription_type,
        enable_speaker_diarization,
        original_file_key=None,  # Add parameter for original file_key
    ):
        """Common logic for creating transcription with preprocessing"""

        # Create transcription file record
        transcription_file = create_transcription_file(
            user_id=g.user.id,
            filename=filename,
            file_type=file_type,
            file_size=file_size,
            fingerprint="",  # Will be updated after processing
            duration=0,  # Will be updated after media processing
            source_type=source_type,
            source_url=source_url,
            language_code=language_code,
            transcription_type=transcription_type,
            enable_speaker_diarization=enable_speaker_diarization,
        )

        # If original_file_key is provided (from OpenAPI upload), update the file_key
        # This ensures the media preprocessing can find the correct temporary FileStorage record
        if original_file_key:
            transcription_file.file_key = original_file_key
            # Also update file_url to match
            storage = current_app.storage
            transcription_file.file_url = storage.generate_file_url(original_file_key)
            db.session.commit()

        # Store webhook URL if provided
        if webhook_url:
            transcription_file.webhook_url = webhook_url
            db.session.commit()

        # Create media preprocessing task (unified processing)
        create_media_preprocessing_task(
            user_id=g.user.id,
            transcription_file_id=transcription_file.id,
            preprocessing_type="url_download",  # Unified processing type
            preprocessing_reason=preprocessing_reason  # Distinguish the source
        )

        return transcription_file

    def _handle_url_download(
        self,
        file_url,
        filename,
        webhook_url,
        language_code,
        transcription_type,
        enable_speaker_diarization,
    ):
        """Handle file URL download with preprocessing support"""


        # Validate URL
        if not file_url.startswith(('http://', 'https://')):
            raise InvalidRequestParametersError("Invalid URL format. URL must start with http:// or https://")

        # Check if this is a YouTube URL and provide helpful error message
        if self._is_youtube_url(file_url):
            raise InvalidRequestParametersError(
                "YouTube URLs are not supported in this endpoint. "
                "Please use the dedicated YouTube transcription endpoint: POST /api/v1/transcriptions/youtube"
            )

        # Validate URL accessibility and media file type
        logger.info(f"Validating URL before processing: {file_url}")
        validation_result = validate_url_for_media_download(file_url)

        if not validation_result.is_valid:
            logger.warning(f"URL validation failed: {validation_result.error_message}")
            # Determine the appropriate exception type based on the error message
            error_msg = validation_result.error_message or "URL validation failed"
            error_msg_lower = error_msg.lower()

            if "not accessible" in error_msg_lower or "connect" in error_msg_lower or "timeout" in error_msg_lower:
                raise URLNotAccessibleError(error_msg)
            elif "not a media file" in error_msg_lower or "web page" in error_msg_lower or "document" in error_msg_lower:
                raise URLNotMediaFileError(error_msg)
            elif "not currently supported" in error_msg_lower or "onedrive" in error_msg_lower:
                raise UnsupportedURLServiceError(error_msg)
            else:
                raise InvalidURLError(error_msg)

        logger.info(f"URL validation successful - Content-Type: {validation_result.content_type}, "
                   f"Content-Length: {validation_result.content_length}, "
                   f"Extension: {validation_result.detected_extension}")

        # Use normalized URL if available (for file sharing services)
        download_url = validation_result.normalized_url or file_url
        if validation_result.normalized_url:
            logger.info(f"Using normalized URL for download: {download_url}")

        try:
            # Use provided filename if available, otherwise extract from URL
            if filename:
                # Use provided filename and truncate if necessary
                final_filename = filename
                if len(final_filename) > MAX_FILENAME_LENGTH:
                    logger.warning(f"Filename too long ({len(final_filename)} chars), truncating to {MAX_FILENAME_LENGTH} chars: {final_filename}")
                    final_filename = final_filename[:MAX_FILENAME_LENGTH]
                # Get file extension from provided filename
                file_ext = os.path.splitext(filename)[1].lower()
                if not file_ext:
                    file_ext = '.unknown'
            else:
                # Use URL validation result to generate better filename
                final_filename = get_suggested_filename_from_url(file_url, validation_result.detected_extension)

                # Truncate filename if necessary
                if len(final_filename) > MAX_FILENAME_LENGTH:
                    logger.warning(f"Generated filename too long ({len(final_filename)} chars), truncating to {MAX_FILENAME_LENGTH} chars: {final_filename}")
                    final_filename = final_filename[:MAX_FILENAME_LENGTH]

                # Get file extension from validation result or filename
                file_ext = validation_result.detected_extension or os.path.splitext(final_filename)[1].lower()
                if not file_ext:
                    file_ext = '.unknown'

            file_type = file_ext[1:] if file_ext.startswith('.') else file_ext

            # Validate file type against supported formats (if not unknown)
            # Note: file_type will be updated after download based on actual file content
            if file_type != 'unknown' and file_type not in SUPPORTED_MEDIA_FORMATS:
                raise InvalidRequestParametersError(
                    f"Unsupported file type: {file_type}. Supported formats: {', '.join(SUPPORTED_MEDIA_FORMATS)}"
                )

            # Create transcription using common logic with normalized URL
            return self._create_transcription_with_preprocessing(
                source_url=download_url,  # Use normalized URL for actual download
                filename=final_filename,
                file_type=file_type,
                file_size=0,  # Will be determined during preprocessing
                source_type=TranscriptionFileSourceType.UPLOAD,  # Use UPLOAD for external URLs too
                preprocessing_reason="external_url",
                webhook_url=webhook_url,
                language_code=language_code,
                transcription_type=transcription_type,
                enable_speaker_diarization=enable_speaker_diarization,
            )

        except Exception as e:
            logger.error(f"Error handling URL download: {e}")
            raise InvalidRequestParametersError(f"Failed to process URL: {str(e)}")

    def _handle_uploaded_file(
        self,
        file_key,
        webhook_url,
        language_code,
        transcription_type,
        enable_speaker_diarization,
        filename=None,
    ):
        """Handle uploaded file transcription"""

        # Find the temporary file storage record
        temp_file_storage = FileStorage.query.filter_by(
            file_key=file_key,
            state=FileState.TEMPORARY.value
        ).first()

        if not temp_file_storage:
            raise InvalidRequestParametersError(f"File not found: {file_key}")

        # Verify the file belongs to the current user
        if temp_file_storage.user_id != g.user.id:
            raise InvalidRequestParametersError("File does not belong to current user")

        # Check if file exists in storage
        storage = current_app.storage
        try:
            storage.check_file_exist(file_key)
        except Exception:
            raise InvalidRequestParametersError("File not found in storage")

        # Generate long-lived download URL for async processing (24 hours)
        try:
            download_url = storage.generate_presigned_url_for_read(file_key, 24*3600)  # 24 hours
        except Exception as e:
            logger.error(f"Failed to generate download URL for file {file_key}: {e}")
            raise InvalidRequestParametersError(f"Failed to generate download URL: {str(e)}")

        # Use provided filename or extract from file_key
        if not filename:
            filename = os.path.basename(file_key)

        # Truncate filename if necessary
        if len(filename) > MAX_FILENAME_LENGTH:
            logger.warning(f"Filename too long ({len(filename)} chars), truncating to {MAX_FILENAME_LENGTH} chars: {filename}")
            filename = filename[:MAX_FILENAME_LENGTH]

        # Get file info from temp storage
        file_size = temp_file_storage.file_size
        file_type = temp_file_storage.file_type

        # Create transcription using common logic
        return self._create_transcription_with_preprocessing(
            source_url=download_url,
            filename=filename,
            file_type=file_type,
            file_size=file_size,
            source_type=TranscriptionFileSourceType.UPLOAD,
            preprocessing_reason="uploaded_file",
            webhook_url=webhook_url,
            language_code=language_code,
            transcription_type=transcription_type,
            enable_speaker_diarization=enable_speaker_diarization,
            original_file_key=file_key,  # Pass the original file_key for cleanup
        )


class OpenAPITranscriptionResource(Resource):
    """
    OpenAPI endpoint for individual transcription operations
    GET /api/v1/transcriptions/{id} - Get transcription with results
    """

    @openapi_auth_required
    @openapi_rate_limit_standard
    def get(self, transcription_id):
        """Get transcription file with results"""
        user_id = g.user.id
        transcription = get_transcription_file_with_result(user_id, transcription_id)

        response_data = marshal(transcription, openapi_transcription_fields)
        return success_response(response_data)


class OpenAPITranscriptionStatusResource(Resource):
    """
    OpenAPI endpoint for transcription status
    GET /api/v1/transcriptions/{id}/status - Get transcription status
    """

    @openapi_auth_required
    @openapi_rate_limit_standard
    def get(self, transcription_id):
        """Get transcription status"""
        user_id = g.user.id
        transcription_file = TranscriptionFile.get_by_id(transcription_id)

        if not transcription_file:
            raise TranscriptionNotFoundError("Transcription not found")

        if transcription_file.user_id != user_id:
            raise TranscriptionNotFoundError("Transcription not found")  # Don't reveal existence

        # 直接 marshal TranscriptionFile
        return success_response(marshal(transcription_file, openapi_status_fields))

   

class OpenAPIYoutubeTranscriptionResource(Resource):
    """
    OpenAPI endpoint for YouTube transcription
    POST /api/v1/transcriptions/youtube - Create transcription from YouTube URL
    """

    @openapi_auth_required
    @openapi_rate_limit_standard
    def post(self):
        """Create transcription from YouTube URL"""
        user_id = g.user.id
        parser = reqparse.RequestParser()

        # Required parameters
        url = parser.get_argument("url", type=str, required=True, location="json")
        language_code = parser.get_argument("language_code", type=str, required=True, location="json", default=None)
        
        # Optional parameters
        webhook_url = parser.get_argument("webhook_url", type=str, location="json")
        transcription_type = parser.get_argument(
            "transcription_type",
            type=str,
            location="json",
            default=TranscriptionType.TRANSCRIPT,
        )
        enable_speaker_diarization = parser.get_argument(
            "enable_speaker_diarization", type=bool, location="json", default=False
        )

        # Extract video information from URL
        from controllers.youtube import YoutubeInfoExtractor

        try:
            extractor = YoutubeInfoExtractor(url)
            video_info = extractor.extract()

            # Get title and duration from extracted info
            title = video_info.get("title", "Unknown Title")
            duration = video_info.get("duration", 0)

            # Ensure duration is a number (convert from string if necessary)
            try:
                duration = int(float(duration)) if duration else 0
            except (ValueError, TypeError):
                duration = 0

            if not duration or duration <= 0:
                raise InvalidRequestParametersError("Could not determine video duration")

        except Exception as e:
            logger.error(f"Failed to extract YouTube video info: {str(e)}")
            raise InvalidRequestParametersError(f"Failed to extract video information: {str(e)}")

        check_transcription_quota_for_openapi(user_id, duration)
        storage = current_app.storage
        transcriber = YoutubeTranscriber(user_id, url, title, duration, storage)

        transcription_file = transcriber.create_transcription_file(
            transcription_type, language_code, enable_speaker_diarization, None
        )

        # Store webhook URL if provided
        if webhook_url:
            transcription_file.webhook_url = webhook_url
            from models import db
            db.session.commit()

        # Create media preprocessing task for YouTube download (async processing)
        create_media_preprocessing_task(
            user_id=user_id,
            transcription_file_id=transcription_file.id,
            preprocessing_type="youtube",
            preprocessing_reason="youtube_download",
            youtube_url=url,
            title=title
        )

        # Return transcription status instead of processing synchronously
        return success_response(marshal(transcription_file, openapi_status_fields))


class OpenAPIFileUploadResource(Resource):
    """
    OpenAPI endpoint for file upload
    POST /api/v1/files/upload-url - Generate pre-signed URLs for file upload
    """

    # Maximum expiration times (in seconds)
    MAX_UPLOAD_EXPIRES = 24 * 3600    # 24 hours
    MAX_DOWNLOAD_EXPIRES = 2 * 3600   # 2 hours

    @openapi_auth_required
    def post(self):
        """Generate pre-signed URLs for file upload"""
        parser = reqparse.RequestParser()

        # Required parameters
        filename = parser.get_argument("filename", type=str, required=True, location="json")
        file_size = parser.get_argument("file_size", type=int, required=True, location="json")

        # Optional parameters
        upload_expires_in = parser.get_argument("upload_expires_in", type=int, location="json", default=3600)  # 1 hour
        download_expires_in = parser.get_argument("download_expires_in", type=int, location="json", default=1800)  # 30 minutes

        # Validate parameters
        self._validate_file_parameters(filename, file_size)
        self._validate_expires_in(upload_expires_in, download_expires_in)

        # Generate file key and temporary fingerprint
        file_ext = os.path.splitext(filename)[1].lower()
        if not file_ext:
            file_ext = '.unknown'

        # Generate UUID for both fingerprint and file_key
        fingerprint = uuid.uuid4().hex
        file_key = f"{g.user.id}-{fingerprint}{file_ext}"

        # Create temporary FileStorage record
        file_storage = FileStorage(
            user_id=g.user.id,
            fingerprint=fingerprint,
            file_type=file_ext[1:] if file_ext.startswith('.') else file_ext,
            file_key=file_key,
            file_size=file_size,
            reference_count=0,  # Temporary files start with 0 references
            state=FileState.TEMPORARY.value
        )
        db.session.add(file_storage)
        db.session.commit()

        # Generate pre-signed URLs
        storage = current_app.storage

        try:
            upload_url = storage.generate_presigned_url_for_upload(
                file_key,
                upload_expires_in
            )

            download_url = storage.generate_presigned_url_for_read(
                file_key,
                download_expires_in
            )

            return success_response({
                "upload_url": upload_url,
                "download_url": download_url,
                "file_key": file_key,
                "upload_expires_at": (datetime.now() + timedelta(seconds=upload_expires_in)).isoformat() + "Z",
                "download_expires_at": (datetime.now() + timedelta(seconds=download_expires_in)).isoformat() + "Z"
            })

        except Exception as e:
            logger.error(f"Failed to generate pre-signed URLs: {str(e)}")
            # Clean up the FileStorage record if URL generation fails
            db.session.delete(file_storage)
            db.session.commit()
            raise InvalidRequestParametersError(f"Failed to generate upload URLs: {str(e)}")

    def _validate_file_parameters(self, filename: str, file_size: int):
        """Validate file parameters"""
        if not filename or not filename.strip():
            raise InvalidRequestParametersError("Filename cannot be empty")

        # Check filename length
        if len(filename) > MAX_FILENAME_LENGTH:
            raise InvalidRequestParametersError(f"Filename too long. Maximum length is {MAX_FILENAME_LENGTH} characters")

        if file_size <= 0:
            raise InvalidRequestParametersError("File size must be greater than 0")

        if file_size > FILE_SIZE_LIMIT:
            raise InvalidRequestParametersError(f"File size cannot exceed {FILE_SIZE_LIMIT_GB}GB")

        # Validate file extension
        file_ext = os.path.splitext(filename)[1].lower()
        if file_ext:
            file_type = file_ext[1:]  # Remove the dot
            if file_type not in SUPPORTED_MEDIA_FORMATS:
                raise InvalidRequestParametersError(
                    f"Unsupported file type: {file_type}. Supported formats: {', '.join(SUPPORTED_MEDIA_FORMATS)}"
                )

    def _validate_expires_in(self, upload_expires: int, download_expires: int):
        """Validate expiration times"""
        if upload_expires <= 0 or upload_expires > self.MAX_UPLOAD_EXPIRES:
            raise InvalidRequestParametersError(
                f"upload_expires_in must be between 1 and {self.MAX_UPLOAD_EXPIRES} seconds"
            )

        if download_expires <= 0 or download_expires > self.MAX_DOWNLOAD_EXPIRES:
            raise InvalidRequestParametersError(
                f"download_expires_in must be between 1 and {self.MAX_DOWNLOAD_EXPIRES} seconds"
            )
