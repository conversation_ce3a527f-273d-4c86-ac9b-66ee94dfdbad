"""Sentry 配置模块"""

import logging
import sentry_sdk
from sentry_sdk.integrations.logging import LoggingIntegration
from bdb import BdbQuit
from yt_dlp.utils import ExtractorError, DownloadError

from exceptions.youtube import YoutubeExtractError

# YouTube 错误关键词列表，用于 Sentry 过滤
YOUTUBE_ERROR_KEYWORDS = [
    "Private video",
    "Sign in to confirm",
    "Video unavailable",
    "Incomplete YouTube ID",
    "looks truncated",
    "The uploader has not made this video available in your country",
    "Join this channel to get access to members-only content",
    "This video is available to this channel's members on level",
    "This live event will begin in",
]

# 额外的错误关键词（用于日志和面包屑过滤）
ADDITIONAL_ERROR_KEYWORDS = ["ExtractorError", "DownloadError", "YoutubeExtractError"]

# 完整的错误关键词列表
ALL_ERROR_KEYWORDS = YOUTUBE_ERROR_KEYWORDS + ADDITIONAL_ERROR_KEYWORDS


def create_before_send_filter():
    """创建 Sentry before_send 过滤函数"""

    def before_send(event, hint):
        """Sentry 事件过滤函数"""
        try:
            # 处理异常事件
            if "exception" in event:
                exception = event["exception"]["values"][0]
                exc_type = exception.get("type", "")
                exc_value = exception.get("value", "")

                # 简单的类型匹配
                if exc_type in [
                    "BdbQuit",
                    "YoutubeExtractError",
                    "ExtractorError",
                    "DownloadError",
                ]:
                    logging.info("Sentry: Ignoring exception by type: %s", exc_type)
                    return None

                # 检查异常消息
                if any(keyword in exc_value for keyword in YOUTUBE_ERROR_KEYWORDS):
                    logging.info(
                        "Sentry: Ignoring exception by message: %s...", exc_value[:50]
                    )
                    return None

            # 处理日志事件
            elif "logentry" in event:
                message = event.get("logentry", {}).get("message", "")

                # 检查日志消息
                if any(keyword in message for keyword in ALL_ERROR_KEYWORDS):
                    logging.info("Sentry: Ignoring log by message: %s...", message[:50])
                    return None

            # 检查面包屑中的日志
            breadcrumbs = event.get("breadcrumbs", {}).get("values", [])
            for crumb in breadcrumbs:
                if crumb.get("type") == "log" and crumb.get("level") == "error":
                    message = crumb.get("message", "")
                    if any(keyword in message for keyword in ALL_ERROR_KEYWORDS):
                        print(
                            f"Sentry: Ignoring event with breadcrumb: {message[:50]}..."
                        )
                        return None
        except Exception as e:
            # 避免任何错误影响应用
            print(f"Error in before_send: {e}")

        return event

    return before_send


def init_sentry(env: str):
    """初始化 Sentry"""
    sentry_sdk.init(
        dsn="https://<EMAIL>/4508829108404224",
        environment=env,
        traces_sample_rate=1.0,
        _experiments={
            "continuous_profiling_auto_start": True,
        },
        ignore_errors=[
            BdbQuit,
            YoutubeExtractError,
            ExtractorError,
            DownloadError,
        ],
        before_send=create_before_send_filter(),
        integrations=[
            LoggingIntegration(level=logging.ERROR, event_level=logging.ERROR)
        ],
        shutdown_timeout=0.5,
    )
