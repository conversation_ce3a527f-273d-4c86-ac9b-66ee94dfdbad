#!/usr/bin/env python3
"""
ffmpeg preprocessing related utility classes
"""

import os
import json
import time
import shutil
import tempfile
import subprocess
import logging
from typing import Tuple, Optional, Dict, Any

from constants.transcription import (
    FFMPEG_PREPROCESSING_SIZE_THRESHOLD,
    VIDEO_FORMATS_REQUIRING_PREPROCESSING,
    AUDIO_FORMATS_REQUIRING_PREPROCESSING
)

logger = logging.getLogger(__name__)

# 默认音频输出格式
DEFAULT_AUDIO_FORMAT = "wav"


class FFmpegPreprocessingTrigger:
    """ffmpeg preprocessing trigger condition detection"""
    
    @staticmethod
    def should_preprocess(transcription_file) -> Tuple[bool, Optional[str]]:
        """
        Determine if ffmpeg preprocessing is needed

        Args:
            transcription_file: TranscriptionFile instance

        Returns:
            Tuple[bool, Optional[str]]: (whether preprocessing is needed, reason)
        """
        # 1. File size check
        if transcription_file.file_size and transcription_file.file_size > FFMPEG_PREPROCESSING_SIZE_THRESHOLD:
            logger.info(f"File size {transcription_file.file_size} exceeds threshold {FFMPEG_PREPROCESSING_SIZE_THRESHOLD}, preprocessing required")
            return True, "large_file"
        
        # 2. File format check
        if transcription_file.filename:
            file_ext = os.path.splitext(transcription_file.filename)[1].lower()
            if file_ext in VIDEO_FORMATS_REQUIRING_PREPROCESSING:
                logger.info(f"Video format {file_ext} requires preprocessing")
                return True, "video_format"
            elif file_ext in AUDIO_FORMATS_REQUIRING_PREPROCESSING:
                logger.info(f"Complex audio format {file_ext} requires preprocessing")
                return True, "complex_audio_format"

        return False, None


class FFmpegProcessor:
    """ffmpeg audio processor"""
    
    def __init__(self, storage, parent_dir=None):
        """
        Initialize ffmpeg processor

        Args:
            storage: Storage service instance
            parent_dir: Optional parent directory to create temp dir in. If None, uses system temp.
        """
        self.storage = storage
        if parent_dir:
            # 在指定的父目录下创建 ffmpeg 子目录
            self.temp_dir = tempfile.mkdtemp(prefix="ffmpeg_", dir=parent_dir)
            logger.info(f"Created ffmpeg temporary directory in parent: {self.temp_dir}")
        else:
            # 在系统临时目录下创建 ffmpeg 目录
            self.temp_dir = tempfile.mkdtemp(prefix="ffmpeg_")
            logger.info(f"Created ffmpeg temporary directory: {self.temp_dir}")
    
    def extract_audio(self, input_file_path: str, output_format: str = 'wav', input_format: Optional[str] = None) -> str:
        """
        Extract audio using ffmpeg

        Args:
            input_file_path: Input file path
            output_format: Output format, default wav
            input_format: Optional input format hint (e.g., 'aac', 'mp3')

        Returns:
            str: Output file path

        Raises:
            Exception: ffmpeg processing failed
        """
        output_file = os.path.join(
            self.temp_dir,
            f"extracted_audio.{output_format}"
        )

        # ffmpeg command: extract audio, choose encoding based on format
        if output_format.lower() == 'mp3':
            # MP3 format: lossy compression, smaller file size
            cmd = ['ffmpeg']

            # Add input format hint if provided
            if input_format:
                cmd.extend(['-f', input_format])
                logger.info(f"Using input format hint: {input_format}")

            cmd.extend([
                '-i', input_file_path,
                '-vn',  # No video
                '-acodec', 'libmp3lame',  # MP3 encoding
                '-b:a', '128k',  # 128kbps bitrate
                '-ar', '16000',  # Sample rate 16kHz
                '-ac', '1',  # Mono
                '-y',  # Overwrite output file
                output_file
            ])
        else:
            # WAV format (default): lossless, best transcription quality
            cmd = ['ffmpeg']

            # Add input format hint if provided
            if input_format:
                cmd.extend(['-f', input_format])
                logger.info(f"Using input format hint: {input_format}")

            cmd.extend([
                '-i', input_file_path,
                '-vn',  # No video
                '-acodec', 'pcm_s16le',  # PCM lossless encoding
                '-ar', '16000',  # Sample rate 16kHz
                '-ac', '1',  # Mono
                '-y',  # Overwrite output file
                output_file
            ])
        
        logger.info(f"Executing ffmpeg command: {' '.join(cmd)}")
        
        try:
            start_time = time.time()
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=3600  # 1 hour timeout
            )
            
            processing_time = time.time() - start_time
            logger.info(f"ffmpeg processing time: {processing_time:.2f} seconds")
            
            if result.returncode != 0:
                logger.error(f"ffmpeg stderr: {result.stderr}")
                raise Exception(f"ffmpeg failed with return code {result.returncode}: {result.stderr}")
            
            if not os.path.exists(output_file):
                raise Exception(f"Output file does not exist: {output_file}")
            
            output_size = os.path.getsize(output_file)
            logger.info(f"Audio extraction completed, output file size: {output_size} bytes")
            
            return output_file
            
        except subprocess.TimeoutExpired:
            logger.error("ffmpeg processing timeout")
            raise Exception("ffmpeg processing timeout")
        except Exception as e:
            logger.error(f"ffmpeg processing failed: {e}")
            raise
    
    def get_media_info(self, file_path: str) -> Dict[str, Any]:
        """
        Get media file information

        Args:
            file_path: File path

        Returns:
            Dict[str, Any]: Media information

        Raises:
            Exception: ffprobe execution failed
        """
        # Add detailed file validation before running ffprobe
        logger.info(f"Checking file before ffprobe: {file_path}")

        if not os.path.exists(file_path):
            raise Exception(f"File does not exist: {file_path}")

        file_size = os.path.getsize(file_path)
        logger.info(f"File size: {file_size} bytes")

        if file_size == 0:
            raise Exception(f"File is empty: {file_path}")

        # Check file permissions
        if not os.access(file_path, os.R_OK):
            raise Exception(f"File is not readable: {file_path}")

        # Try to guess format from file extension for better format hints
        guessed_format = self._guess_format_from_extension(file_path)
        if guessed_format:
            logger.info(f"Guessed format from extension: {guessed_format}")

        # Try robust ffprobe methods for problematic files

        # Method 1: Try with warning level verbosity (allows low-confidence detection)
        try:
            cmd_warning = ['ffprobe', '-v', 'warning', '-print_format', 'json', '-show_format', '-show_streams', file_path]
            result_warning = subprocess.run(cmd_warning, capture_output=True, text=True, timeout=30)
            if result_warning.returncode == 0 and len(result_warning.stdout) > 10:
                logger.info("Successfully obtained media info using warning level")
                return json.loads(result_warning.stdout)
        except Exception as e:
            logger.debug(f"Warning level method failed: {e}")

        # Method 2: Try with enhanced probing parameters
        try:
            cmd_robust = ['ffprobe', '-v', 'warning', '-probesize', '10M', '-analyzeduration', '10M',
                         '-print_format', 'json', '-show_format', '-show_streams', file_path]
            result_robust = subprocess.run(cmd_robust, capture_output=True, text=True, timeout=30)
            if result_robust.returncode == 0 and len(result_robust.stdout) > 10:
                logger.info("Successfully obtained media info using enhanced probing")
                return json.loads(result_robust.stdout)
        except Exception as e:
            logger.debug(f"Enhanced probing method failed: {e}")

        # Method 3: Try with format hints for problematic files
        # Start with guessed format if available, then try common formats
        format_hints = []
        if guessed_format:
            format_hints.append(guessed_format)

        # Add other common formats (excluding the guessed one to avoid duplication)
        common_formats = ['aac', 'mp3', 'mp4', 'mpeg', 'wav', 'flac', 'ogg', 'webm', 'asf', 'avi', 'mov', 'matroska']
        for fmt in common_formats:
            if fmt != guessed_format and fmt not in format_hints:
                format_hints.append(fmt)

        for format_hint in format_hints:
            try:
                cmd_format_hint = ['ffprobe', '-v', 'warning', '-f', format_hint, '-print_format', 'json',
                                  '-show_format', '-show_streams', file_path]
                result_format = subprocess.run(cmd_format_hint, capture_output=True, text=True, timeout=30)
                if result_format.returncode == 0 and len(result_format.stdout) > 10:
                    logger.info(f"Successfully obtained media info using {format_hint.upper()} format hint")
                    return json.loads(result_format.stdout)
            except Exception as e:
                logger.debug(f"{format_hint.upper()} format hint method failed: {e}")
                continue

        # Final fallback: standard method with enhanced parameters

        cmd = [
            'ffprobe', '-v', 'warning',  # Changed from 'quiet' to 'warning' to allow low-confidence detection
            '-probesize', '10M',         # Increase probe size for better detection
            '-analyzeduration', '10M',   # Increase analysis duration
            '-print_format', 'json',
            '-show_format', '-show_streams',
            file_path
        ]

        logger.info(f"Executing ffprobe command: {' '.join(cmd)}")

        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

            if result.returncode != 0:
                logger.error(f"ffprobe failed with return code {result.returncode}: {result.stderr}")
                raise Exception(f"ffprobe failed with return code {result.returncode}: {result.stderr}")

            if not result.stdout.strip():
                raise Exception("ffprobe returned empty output")

            media_info = json.loads(result.stdout)
            logger.info("Successfully obtained media information")
            return media_info

        except subprocess.TimeoutExpired:
            logger.error("ffprobe execution timeout")
            raise Exception("ffprobe timeout")
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse ffprobe output: {e}")
            logger.error(f"Raw stdout: {repr(result.stdout)}")
            raise Exception(f"Failed to parse ffprobe output: {e}")
        except Exception as e:
            logger.error(f"ffprobe execution failed: {e}")
            raise
    
    def extract_duration_from_media_info(self, media_info: Dict[str, Any]) -> float:
        """
        Extract duration from media information

        Args:
            media_info: Media information returned by ffprobe

        Returns:
            float: Duration (seconds)
        """
        try:
            # Prefer to get duration from format
            if 'format' in media_info and 'duration' in media_info['format']:
                duration = float(media_info['format']['duration'])
                logger.info(f"Duration obtained from format: {duration} seconds")
                return duration
            
            # Get duration from audio stream
            if 'streams' in media_info:
                for stream in media_info['streams']:
                    if stream.get('codec_type') == 'audio' and 'duration' in stream:
                        duration = float(stream['duration'])
                        logger.info(f"Duration obtained from audio stream: {duration} seconds")
                        return duration
            
            logger.warning("Unable to extract duration from media information, returning 0")
            return 0.0
            
        except (ValueError, KeyError) as e:
            logger.error(f"Failed to extract duration: {e}")
            return 0.0
    
    def _guess_format_from_extension(self, file_path: str) -> Optional[str]:
        """
        Guess format from file extension

        Args:
            file_path: Path to media file

        Returns:
            Optional[str]: Guessed format or None
        """
        _, ext = os.path.splitext(file_path.lower())
        ext = ext.lstrip('.')

        # Map common extensions to ffmpeg format names
        format_map = {
            'aac': 'aac',
            'mp3': 'mp3',
            'mp4': 'mp4',
            'm4a': 'mp4',
            'mpeg': 'mpeg',
            'mpg': 'mpeg',
            'wav': 'wav',
            'flac': 'flac',
            'ogg': 'ogg',
            'oga': 'ogg',
            'webm': 'webm',
            'wma': 'asf',
            'wmv': 'asf',
            'avi': 'avi',
            'mov': 'mov',
            'mkv': 'matroska'
        }

        return format_map.get(ext)

    def _detect_input_format_hint(self, media_info: dict) -> Optional[str]:
        """
        Detect input format hint from media info

        Args:
            media_info: Media information from ffprobe

        Returns:
            Optional[str]: Format hint or None
        """
        if not media_info or 'format' not in media_info:
            return None

        format_name = media_info['format'].get('format_name', '').lower()

        # 检测常见的问题格式并提供格式提示
        if 'aac' in format_name:
            return 'aac'
        elif 'mp3' in format_name:
            return 'mp3'
        elif 'mp4' in format_name or 'mpeg' in format_name:
            return 'mp4'
        elif 'wav' in format_name:
            return 'wav'
        elif 'flac' in format_name:
            return 'flac'
        elif 'ogg' in format_name:
            return 'ogg'
        elif 'webm' in format_name:
            return 'webm'

        return None

    def cleanup(self):
        """Clean up temporary files"""
        try:
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
                logger.info(f"Cleaned up temporary directory: {self.temp_dir}")
        except Exception as e:
            logger.error(f"Failed to clean up temporary directory: {e}")


def generate_processed_file_key(original_file_key: str, output_format: str = 'wav') -> str:
    """
    Generate storage key for processed file

    Args:
        original_file_key: Original file key
        output_format: Output format, default wav

    Returns:
        str: Processed file key
    """
    # Add _processed suffix to original file key
    name, _ = os.path.splitext(original_file_key)
    return f"{name}_processed.{output_format}"


def check_ffmpeg_availability() -> bool:
    """
    检查 ffmpeg 是否可用
    
    Returns:
        bool: ffmpeg 是否可用
    """
    try:
        result = subprocess.run(['ffmpeg', '-version'], capture_output=True, timeout=10)
        if result.returncode == 0:
            logger.info("ffmpeg available")
            return True
        else:
            logger.error("ffmpeg not available")   
            return False
    except Exception as e:
        logger.error(f"Failed to check ffmpeg availability: {e}")
        return False
