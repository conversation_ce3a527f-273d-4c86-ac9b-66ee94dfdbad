#!/usr/bin/env python3
"""
URL validation utility for checking if URLs point to valid downloadable media files
"""

import logging
import requests
import mimetypes
from typing import Tuple, Optional, Dict, Any
from urllib.parse import urlparse

from constants.transcription import SUPPORTED_MEDIA_EXTENSIONS, SUPPORTED_MIME_TYPES

logger = logging.getLogger(__name__)

# Timeout settings for URL validation
VALIDATION_TIMEOUT = 10  # seconds
MAX_CONTENT_LENGTH = 5 * 1024 * 1024 * 1024  # 5GB

# Use constants from transcription module for consistency
VALID_MIME_TYPES = SUPPORTED_MIME_TYPES

# Use constants from transcription module for consistency
VALID_EXTENSIONS = SUPPORTED_MEDIA_EXTENSIONS


class URLValidationResult:
    """Result of URL validation"""

    def __init__(self, is_valid: bool, error_message: Optional[str] = None,
                 content_type: Optional[str] = None, content_length: Optional[int] = None,
                 detected_extension: Optional[str] = None, normalized_url: Optional[str] = None):
        self.is_valid = is_valid
        self.error_message = error_message
        self.content_type = content_type
        self.content_length = content_length
        self.detected_extension = detected_extension
        self.normalized_url = normalized_url  # The URL that should be used for actual download


def normalize_file_sharing_url(url: str) -> str:
    """
    Normalize file sharing URLs to direct download format

    Args:
        url: Original URL from user

    Returns:
        str: Normalized URL for direct download
    """
    from urllib.parse import urlparse, parse_qs, urlencode, urlunparse

    parsed_url = urlparse(url)
    domain = parsed_url.netloc.lower()

    # Dropbox URL normalization
    if 'dropbox.com' in domain:
        # Convert dl=0 to dl=1 for direct download
        query_params = parse_qs(parsed_url.query)

        # If dl parameter exists and is 0, change to 1
        if 'dl' in query_params:
            query_params['dl'] = ['1']
        else:
            # If no dl parameter, add dl=1
            query_params['dl'] = ['1']

        # Reconstruct URL with updated query parameters
        new_query = urlencode(query_params, doseq=True)
        normalized_url = urlunparse((
            parsed_url.scheme,
            parsed_url.netloc,
            parsed_url.path,
            parsed_url.params,
            new_query,
            parsed_url.fragment
        ))

        if normalized_url != url:
            logger.info(f"Normalized Dropbox URL: {url} -> {normalized_url}")

        return normalized_url

    # Google Drive URL normalization
    elif 'drive.google.com' in domain or 'docs.google.com' in domain:
        # Convert sharing URLs to direct download format
        if '/file/d/' in parsed_url.path and '/view' in parsed_url.path:
            # Extract file ID from sharing URL
            path_parts = parsed_url.path.split('/')
            if 'file' in path_parts and 'd' in path_parts:
                try:
                    file_index = path_parts.index('d') + 1
                    file_id = path_parts[file_index]
                    normalized_url = f"https://drive.google.com/uc?id={file_id}&export=download"
                    logger.info(f"Normalized Google Drive URL: {url} -> {normalized_url}")
                    return normalized_url
                except (IndexError, ValueError):
                    pass

    # OneDrive URLs are not supported - return error
    elif 'onedrive.live.com' in domain or '1drv.ms' in domain:
        # OneDrive URLs require complex authentication and are not currently supported
        logger.warning(f"OneDrive URL detected but not supported: {url}")
        # We'll handle this in the validation function to provide a proper error message
        pass

    # Return original URL if no normalization needed
    return url


def validate_url_for_media_download(url: str) -> URLValidationResult:
    """
    Validate if a URL points to a downloadable media file

    Args:
        url: The URL to validate

    Returns:
        URLValidationResult: Validation result with details
    """
    logger.info(f"Validating URL for media download: {url}")

    try:
        # 0. Normalize file sharing URLs for direct download
        normalized_url = normalize_file_sharing_url(url)

        # 1. Basic URL format validation
        parsed_url = urlparse(normalized_url)
        if not parsed_url.scheme or not parsed_url.netloc:
            return URLValidationResult(
                is_valid=False,
                error_message="Invalid URL format. URL must include protocol and domain."
            )
        
        if parsed_url.scheme not in ['http', 'https']:
            return URLValidationResult(
                is_valid=False,
                error_message="URL must use HTTP or HTTPS protocol."
            )

        # Check for unsupported OneDrive URLs
        domain = parsed_url.netloc.lower()
        if 'onedrive.live.com' in domain or '1drv.ms' in domain:
            return URLValidationResult(
                is_valid=False,
                error_message="OneDrive URLs are not currently supported. Please use Dropbox, Google Drive, or direct file URLs instead."
            )
        
        # 2. Check file extension from URL path
        path = parsed_url.path.lower()
        detected_extension = None
        for ext in VALID_EXTENSIONS:
            if path.endswith(ext):
                detected_extension = ext
                break
        
        # 3. Make HEAD request to check if URL is accessible and get metadata
        logger.info(f"Making HEAD request to: {normalized_url}")

        # Use browser-like User-Agent for file sharing services
        parsed_url = urlparse(normalized_url)
        domain = parsed_url.netloc.lower()
        is_file_sharing_service = any(domain.endswith(service) for service in [
            'dropbox.com', 'drive.google.com', 'docs.google.com'
        ])

        if is_file_sharing_service:
            user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        else:
            user_agent = 'UniScribe-Bot/1.0 (Media Validation)'

        try:
            response = requests.head(
                normalized_url,
                timeout=VALIDATION_TIMEOUT,
                allow_redirects=True,
                headers={'User-Agent': user_agent}
            )

            # If HEAD is not allowed or forbidden, try GET with range header
            if response.status_code in [405, 403]:  # Method Not Allowed or Forbidden
                logger.info(f"HEAD returned {response.status_code}, trying GET with range header")
                response = requests.get(
                    normalized_url,
                    timeout=VALIDATION_TIMEOUT,
                    allow_redirects=True,
                    headers={
                        'User-Agent': user_agent,
                        'Range': 'bytes=0-1023'  # Only get first 1KB
                    },
                    stream=True
                )
                # Close the connection immediately to avoid downloading the full file
                response.close()
            
            response.raise_for_status()
            
        except requests.exceptions.Timeout:
            return URLValidationResult(
                is_valid=False,
                error_message="URL request timed out. The server may be slow or unreachable."
            )
        except requests.exceptions.ConnectionError:
            return URLValidationResult(
                is_valid=False,
                error_message="Cannot connect to the URL. Please check if the URL is accessible."
            )
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 401:
                return URLValidationResult(
                    is_valid=False,
                    error_message="URL requires authentication. Please provide a publicly accessible URL."
                )
            elif e.response.status_code == 403:
                return URLValidationResult(
                    is_valid=False,
                    error_message="Access to URL is forbidden. Please check URL permissions."
                )
            elif e.response.status_code == 404:
                return URLValidationResult(
                    is_valid=False,
                    error_message="URL not found (404). Please check if the URL is correct."
                )
            else:
                return URLValidationResult(
                    is_valid=False,
                    error_message=f"HTTP error {e.response.status_code}: {e.response.reason}"
                )
        except requests.exceptions.RequestException as e:
            return URLValidationResult(
                is_valid=False,
                error_message=f"Request failed: {str(e)}"
            )
        
        # 4. Check Content-Type header
        content_type = response.headers.get('Content-Type', '').lower()
        if content_type:
            # Remove charset and other parameters
            content_type = content_type.split(';')[0].strip()
        
        logger.info(f"Response Content-Type: {content_type}")
        
        # 5. Check Content-Length header
        content_length = None
        content_length_header = response.headers.get('Content-Length')
        if content_length_header:
            try:
                content_length = int(content_length_header)
                logger.info(f"Response Content-Length: {content_length} bytes")
                
                # Check if file is too large
                if content_length > MAX_CONTENT_LENGTH:
                    return URLValidationResult(
                        is_valid=False,
                        error_message=f"File is too large ({content_length / (1024**3):.1f}GB). Maximum allowed size is {MAX_CONTENT_LENGTH / (1024**3):.0f}GB."
                    )
                
                # Check if file is suspiciously small (likely not a media file)
                if content_length < 1024:  # Less than 1KB
                    return URLValidationResult(
                        is_valid=False,
                        error_message="File is too small to be a valid media file."
                    )
                    
            except ValueError:
                logger.warning(f"Invalid Content-Length header: {content_length_header}")
        
        # 6. First check if MIME type suggests it's not a media file, but allow exceptions for known file sharing services
        if content_type.startswith(('text/', 'application/json', 'application/xml')):
            # Check if this is a known file sharing service that might return non-media content types
            parsed_url = urlparse(normalized_url)  # Use normalized URL for domain check
            domain = parsed_url.netloc.lower()

            # Known file sharing services that return JSON/XML/HTML but can serve files directly
            file_sharing_domains = {
                'dropbox.com', 'www.dropbox.com',
                'drive.google.com', 'docs.google.com',
            }

            is_file_sharing_service = any(domain.endswith(service) for service in file_sharing_domains)

            # For file sharing services, allow even without extension (they may not show extension in URL)
            if is_file_sharing_service:
                if detected_extension:
                    logger.info(f"Allowing file sharing service URL with extension {detected_extension}: {domain}")
                else:
                    logger.info(f"Allowing file sharing service URL without extension: {domain}")
                    logger.info(f"Note: File sharing service returned {content_type}, assuming it's a valid file")
            else:
                return URLValidationResult(
                    is_valid=False,
                    error_message=f"URL points to a web page or document, not a media file. Content-Type: {content_type}"
                )

        # 7. Validate content type and extension
        is_valid_mime = content_type in VALID_MIME_TYPES
        is_valid_extension = detected_extension is not None

        # Special handling for generic MIME types
        if content_type == 'application/octet-stream' and is_valid_extension:
            logger.info("Generic MIME type with valid extension - accepting")
            is_valid_mime = True

        # If we have neither valid MIME type nor valid extension, check if it's a file sharing service
        if not is_valid_mime and not is_valid_extension:
            # Check if this is a known file sharing service (allow without extension)
            parsed_url = urlparse(normalized_url)
            domain = parsed_url.netloc.lower()
            file_sharing_domains = {
                'dropbox.com', 'www.dropbox.com',
                'drive.google.com', 'docs.google.com',
            }

            is_file_sharing_service = any(domain.endswith(service) for service in file_sharing_domains)

            if not is_file_sharing_service:
                return URLValidationResult(
                    is_valid=False,
                    error_message=f"URL does not appear to point to a supported media file. "
                                 f"Content-Type: {content_type or 'unknown'}, "
                                 f"Detected extension: {detected_extension or 'none'}"
                )
            else:
                logger.info(f"Allowing file sharing service without clear media indicators: {domain}")
        
        logger.info(f"URL validation successful - Content-Type: {content_type}, Extension: {detected_extension}")

        return URLValidationResult(
            is_valid=True,
            content_type=content_type,
            content_length=content_length,
            detected_extension=detected_extension,
            normalized_url=normalized_url if normalized_url != url else None
        )
        
    except Exception as e:
        logger.error(f"Unexpected error during URL validation: {e}")
        return URLValidationResult(
            is_valid=False,
            error_message=f"Validation failed due to unexpected error: {str(e)}"
        )


def get_suggested_filename_from_url(url: str, detected_extension: Optional[str] = None) -> str:
    """
    Extract or generate a filename from URL
    
    Args:
        url: The URL to extract filename from
        detected_extension: Extension detected during validation
        
    Returns:
        str: Suggested filename
    """
    try:
        parsed_url = urlparse(url)
        path = parsed_url.path
        
        # Try to get filename from URL path
        if path and path != '/':
            filename = path.split('/')[-1]
            if filename and '.' in filename:
                return filename
        
        # Generate filename based on domain and extension
        domain = parsed_url.netloc.replace('www.', '')
        if detected_extension:
            return f"download_from_{domain}{detected_extension}"
        else:
            return f"download_from_{domain}.unknown"
            
    except Exception:
        # Fallback to generic filename
        import time
        timestamp = int(time.time())
        if detected_extension:
            return f"download_{timestamp}{detected_extension}"
        else:
            return f"download_{timestamp}.unknown"
