"""
OpenAPI Rate Limiting module
Provides API Key based rate limiting for OpenAPI endpoints
"""

import logging
import functools
from typing import Callable
from flask import g

from libs.rate_limit import (
    RateLimiter,
    SlidingWindowStrategy,
    FixedWindowStrategy,
    RedisRateLimitBackend,
    RateLimitExceeded
)

logger = logging.getLogger(__name__)





def api_key_rate_limit(
    per_minute: bool = True,
    per_day: bool = False,
    strategy: str = "sliding_window",
    backend: RedisRateLimitBackend = None
):
    """
    API Key based rate limiting decorator for OpenAPI endpoints
    
    Args:
        per_minute: Whether to apply per-minute rate limiting
        per_day: Whether to apply per-day rate limiting  
        strategy: Rate limiting strategy ("sliding_window" or "fixed_window")
        backend: Redis backend instance
    
    Usage:
        @api_key_rate_limit(per_minute=True, per_day=True)
        @openapi_auth_required
        def my_api_endpoint():
            pass
    """
    
    if backend is None:
        backend = RedisRateLimitBackend(prefix="openapi_rate_limit")
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # API Key should be available from openapi_auth_required decorator
            if not hasattr(g, 'api_key') or not g.api_key:
                logger.warning("api_key_rate_limit used without openapi_auth_required")
                return func(*args, **kwargs)

            logger.info(f"🔍 Rate limit check starting for API key {g.api_key.id}")
            
            api_key_record = g.api_key
            api_key_id = str(api_key_record.id)
            
            # Check per-day rate limit
            if per_day and api_key_record.rate_limit_per_day > 0:
                logger.info(f"🔍 Checking per-day rate limit: {api_key_record.rate_limit_per_day} requests/day")
                _check_rate_limit(
                    backend=backend,
                    key=f"api_key:{api_key_id}:day",
                    max_requests=api_key_record.rate_limit_per_day,
                    window_size=86400,  # 24 hours
                    strategy=strategy,
                    limit_type="per day"
                )
                logger.info(f"✅ Per-day rate limit check passed")

            # Check per-minute rate limit
            if per_minute and api_key_record.rate_limit_per_minute > 0:
                logger.info(f"🔍 Checking per-minute rate limit: {api_key_record.rate_limit_per_minute} requests/minute")
                _check_rate_limit(
                    backend=backend,
                    key=f"api_key:{api_key_id}:minute",
                    max_requests=api_key_record.rate_limit_per_minute,
                    window_size=60,  # 1 minute
                    strategy=strategy,
                    limit_type="per minute"
                )
                logger.info(f"✅ Per-minute rate limit check passed")
            
            logger.info(f"✅ All rate limit checks passed for API key {api_key_id}")
            return func(*args, **kwargs)
        
        return wrapper
    return decorator


def _check_rate_limit(
    backend: RedisRateLimitBackend,
    key: str,
    max_requests: int,
    window_size: float,
    strategy: str,
    limit_type: str
):
    """Internal function to check rate limit"""
    
    # Create strategy
    if strategy == "fixed_window":
        rate_strategy = FixedWindowStrategy(max_requests, window_size)
    else:
        # Default to sliding window for better user experience
        rate_strategy = SlidingWindowStrategy(max_requests, window_size)
    
    # Create limiter
    limiter = RateLimiter(rate_strategy, backend, "")
    
    # Check if allowed
    allowed, wait_time = limiter.is_allowed(key)
    
    if not allowed:
        retry_after = wait_time if wait_time else window_size
        error_message = (
            f"API rate limit exceeded ({limit_type}). "
            f"Maximum {max_requests} requests {limit_type} allowed. "
            f"Try again in {retry_after:.1f} seconds."
        )
        
        logger.info(
            f"Rate limit exceeded for API key {g.api_key.id}: "
            f"{limit_type}, max={max_requests}, retry_after={retry_after:.1f}s"
        )
        
        from exceptions.openapi import APIRateLimitExceededError
        raise APIRateLimitExceededError(error_message, retry_after=retry_after)


def get_api_key_rate_limit_status(api_key_id: str) -> dict:
    """
    Get current rate limit status for an API key
    
    Returns:
        dict: Rate limit status with remaining requests and reset times
    """
    backend = RedisRateLimitBackend(prefix="openapi_rate_limit")
    
    # This is a simplified version - in practice you'd need to implement
    # a method to get current usage from the backend
    return {
        "per_minute": {
            "limit": "N/A",
            "remaining": "N/A", 
            "reset_time": "N/A"
        },
        "per_day": {
            "limit": "N/A",
            "remaining": "N/A",
            "reset_time": "N/A"
        }
    }


# Convenience decorators for common use cases
def openapi_rate_limit_standard(func: Callable) -> Callable:
    """
    Standard rate limiting for OpenAPI endpoints
    Applies both per-minute and per-day limits
    """
    return api_key_rate_limit(per_minute=True, per_day=True)(func)


def openapi_rate_limit_per_minute_only(func: Callable) -> Callable:
    """
    Per-minute only rate limiting for OpenAPI endpoints
    """
    return api_key_rate_limit(per_minute=True, per_day=False)(func)


def openapi_rate_limit_per_day_only(func: Callable) -> Callable:
    """
    Per-day only rate limiting for OpenAPI endpoints
    """
    return api_key_rate_limit(per_minute=False, per_day=True)(func)
