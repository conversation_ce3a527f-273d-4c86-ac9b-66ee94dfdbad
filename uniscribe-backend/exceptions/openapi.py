"""
OpenAPI specific exceptions
"""

from flask import jsonify
from .base import BaseAPIException
from .error_codes import ErrorCode
from .decorators import register_exception
from utils.api_response import error_response


class OpenAPIException(BaseAPIException):
    """Base exception for OpenAPI endpoints with unified response format"""
    
    def get_response(self, environ=None):
        """Return unified error response format"""
        response_data = error_response(
            message=self.description,
            code=self.error_code.value if self.error_code else None,
            details=self.details if hasattr(self, 'details') else None
        )
        response = jsonify(response_data)
        response.status_code = self.code or 400
        return response


@register_exception
class APIAccessDeniedError(OpenAPIException):
    """Exception for API access denied (free users)"""
    code = 403
    error_code = ErrorCode.API_ACCESS_DENIED


@register_exception
class InvalidAPIKeyError(OpenAPIException):
    """Exception for invalid API key"""
    code = 401
    error_code = ErrorCode.INVALID_API_KEY


@register_exception
class APIKeyRequiredError(OpenAPIException):
    """Exception for missing API key"""
    code = 401
    error_code = ErrorCode.API_KEY_REQUIRED


@register_exception
class APIRateLimitExceededError(OpenAPIException):
    """Exception for API rate limit exceeded"""
    code = 429
    error_code = ErrorCode.RATE_LIMIT_EXCEEDED

    def __init__(self, message: str = None, retry_after: float = None, **kwargs):
        super().__init__(message, **kwargs)
        self.retry_after = retry_after

    def get_response(self, environ=None):
        """Return rate limit error response with Retry-After header"""
        response = super().get_response(environ)

        # Add Retry-After header if available
        if self.retry_after:
            response.headers['Retry-After'] = str(int(self.retry_after))
            response.headers['X-RateLimit-Retry-After'] = str(self.retry_after)

        return response


@register_exception
class FileSizeExceededError(OpenAPIException):
    """Exception for file size limit exceeded"""
    code = 413
    error_code = ErrorCode.FILE_SIZE_EXCEEDED


@register_exception
class InvalidRequestParametersError(OpenAPIException):
    """Exception for invalid request parameters"""
    code = 400
    error_code = ErrorCode.INVALID_PARAMS


@register_exception
class MissingRequiredParameterError(OpenAPIException):
    """Exception for missing required parameter"""
    code = 400
    error_code = ErrorCode.INVALID_PARAMS


@register_exception
class InvalidFileFormatError(OpenAPIException):
    """Exception for invalid file format"""
    code = 400
    error_code = ErrorCode.INVALID_FILE_FORMAT


@register_exception
class InvalidURLError(OpenAPIException):
    """Exception for invalid or inaccessible URLs"""
    code = 400
    error_code = ErrorCode.INVALID_URL


@register_exception
class URLNotAccessibleError(OpenAPIException):
    """Exception for URLs that cannot be accessed"""
    code = 400
    error_code = ErrorCode.URL_NOT_ACCESSIBLE


@register_exception
class URLNotMediaFileError(OpenAPIException):
    """Exception for URLs that don't point to media files"""
    code = 400
    error_code = ErrorCode.URL_NOT_MEDIA_FILE


@register_exception
class UnsupportedURLServiceError(OpenAPIException):
    """Exception for URLs from unsupported services"""
    code = 400
    error_code = ErrorCode.UNSUPPORTED_URL_SERVICE


@register_exception
class TranscriptionNotFoundError(OpenAPIException):
    """Exception for transcription not found"""
    code = 404
    error_code = ErrorCode.TASK_NOT_FOUND


@register_exception
class WebhookDeliveryError(OpenAPIException):
    """Exception for webhook delivery failures"""
    code = 500
    error_code = ErrorCode.WEBHOOK_DELIVERY_ERROR


@register_exception
class APIKeyNotFoundError(OpenAPIException):
    """Exception for API key not found"""
    code = 404
    error_code = ErrorCode.API_KEY_NOT_FOUND


@register_exception
class APIKeyLimitExceededError(OpenAPIException):
    """Exception for API key limit exceeded"""
    code = 409
    error_code = ErrorCode.API_KEY_LIMIT_EXCEEDED


@register_exception
class APIKeyNameExistsError(OpenAPIException):
    """Exception for API key name already exists"""
    code = 409
    error_code = ErrorCode.API_KEY_NAME_EXISTS


@register_exception
class InsufficientTranscriptionQuotaError(OpenAPIException):
    """OpenAPI-specific exception for insufficient transcription quota"""
    code = 403
    error_code = ErrorCode.INSUFFICIENT_TRANSCRIPTION_QUOTA
