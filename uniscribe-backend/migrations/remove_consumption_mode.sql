-- 移除消费模式字段的数据库迁移脚本
-- 执行时间：预计 < 1 分钟（取决于数据量）

-- 1. 删除相关索引
DROP INDEX  idx_task_consumption_mode on task;
DROP INDEX  idx_task_status_mode_priority on task

-- 2. 删除 consumption_mode 字段
ALTER TABLE task DROP COLUMN consumption_mode;

-- 3. 重新创建优化的索引（不包含 consumption_mode）
CREATE INDEX idx_task_status_priority ON task(status, priority DESC, created_time ASC);

-- 4. 验证字段已删除
DESCRIBE task;

-- 5. 显示当前任务统计
SELECT 
    status,
    task_type,
    COUNT(*) as count
FROM task 
GROUP BY status, task_type
ORDER BY status, task_type;
