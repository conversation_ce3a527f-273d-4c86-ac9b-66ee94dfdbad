from enum import Enum
from libs.typed_enum import TypedEnum


# 支持的音频格式（与前端保持一致）
SUPPORTED_AUDIO_FORMATS = [
    "mp3",
    "m4a",
    "wav",
    "aac",
    "flac",
    "ogg",
    "oga",
    "opus",
    "webm",  # WebM audio
    "weba",  # WebM audio
    "amr",
    "awb",   # AMR-WB
    "wma",
    "mka",   # Matroska audio
]

# 支持的视频格式（会提取音频）
SUPPORTED_VIDEO_FORMATS = [
    "mp4",
    "mov",
    "webm",  # WebM video
    "mpg",
    "mpeg",
    "3gp",
    "wmv",
    "mkv",   # <PERSON>roska video
]

# 所有支持的媒体格式（音频+视频）
SUPPORTED_MEDIA_FORMATS = SUPPORTED_AUDIO_FORMATS + SUPPORTED_VIDEO_FORMATS

# 支持的媒体格式扩展名（用于URL验证等）
SUPPORTED_MEDIA_EXTENSIONS = {f'.{fmt}' for fmt in SUPPORTED_MEDIA_FORMATS}

# 支持的MIME类型（基于前端MIME_TO_EXTENSION映射）
SUPPORTED_MIME_TYPES = {
    # Audio MIME types
    'audio/mpeg',               # mp3
    'audio/mp4',                # m4a
    'audio/x-m4a',              # m4a
    'audio/wav',                # wav
    'audio/x-wav',              # wav
    'audio/webm',               # webm
    'audio/weba',               # weba
    'audio/amr',                # amr
    'audio/amr-wb',             # awb
    'audio/aac',                # aac
    'audio/vnd.dlna.adts',      # aac
    'audio/ogg',                # ogg
    'audio/oga',                # oga
    'audio/opus',               # opus
    'audio/flac',               # flac
    'audio/x-flac',             # flac
    'audio/x-matroska',         # mka
    'audio/x-ms-wma',           # wma

    # Video MIME types (we'll extract audio from these)
    'video/mp4',                # mp4
    'video/webm',               # webm
    'video/quicktime',          # mov
    'video/mpeg',               # mpg
    'video/3gpp',               # 3gp
    'video/x-ms-wmv',           # wmv
    'video/x-matroska',         # mkv

    # Generic types that might be valid
    'application/octet-stream',  # Generic binary, need to check extension
}

# ffmpeg 预处理相关常量
FFMPEG_PREPROCESSING_SIZE_THRESHOLD = 256 * 1024 * 1024  #  256MB

# 需要预处理的视频格式
VIDEO_FORMATS_REQUIRING_PREPROCESSING = {
    '.mp4', '.mov', '.avi', '.mkv', '.wmv', '.flv', '.webm', '.m4v'
}

# 需要预处理的复杂音频格式（前端可能处理困难）
AUDIO_FORMATS_REQUIRING_PREPROCESSING = {
    '.ape', '.wv', '.tta', '.dts', '.ac3', '.amr'
}

# 免费用户每天限制文件数量
FREE_USER_MAX_FILE_COUNT_PER_DAY = 3
ANONYMOUS_USER_MAX_FILE_COUNT_PER_DAY = 2
MAX_FILENAME_LENGTH = 100


class TranscriptionFileSourceType(str, Enum):
    """转录文件来源类型"""

    UPLOAD = "upload"  # 用户直接上传的文件
    YOUTUBE = "youtube"  # 从 YouTube 下载的文件
    # 未来可能的其他来源
    # SPOTIFY = "spotify"
    # PODCAST = "podcast"
    # 等等


class TranscriptionFileStatus(TypedEnum):
    # TODO: 改成大写
    uploading = 1  # 上传中
    uploaded = 2  # 上传成功，但未支付。 所有任务尚未开始处理
    processing = 3  # 处理中， 至少有一个任务正在处理（此时没有任何任务完成）
    partially_completed = 4  # 部分任务完成。 所有任务中，有一部分任务已成功完成（可能有部分非关键任务任务失败），用户可以查看部分处理结果。允许对部分任务进行重试。
    completed = 5  # 全部任务完成。 所有任务都已成功完成，用户可以查看全部处理结果。
    failed = 6  # 文件处理失败，且所有任务都失败或部分关键任务失败，导致无法继续处理。可以重试。
    completed_with_errors = 7  # 所有任务都已处于最终状态，但有部分非关键任务失败。用户可以查看部分处理结果，不需要继续轮询。
    preprocessing = 8  # 媒体预处理中
    preprocessing_failed = 9  # 媒体预处理失败


# 允许删除的文件状态列表（与前端保持一致）
DELETABLE_FILE_STATUSES = [
    TranscriptionFileStatus.uploading.id,
    TranscriptionFileStatus.uploaded.id,
    TranscriptionFileStatus.preprocessing_failed.id,
    TranscriptionFileStatus.partially_completed.id,
    TranscriptionFileStatus.completed.id,
    TranscriptionFileStatus.completed_with_errors.id,
    TranscriptionFileStatus.failed.id,
]


class OriginalFileDeleteReason(Enum):
    """原文件删除原因枚举"""

    USER_MANUAL = "user_manual"  # 用户手动删除
    AUTO_CLEANUP = "auto_cleanup"  # 系统自动清理（如免费用户30天清理）
    ADMIN_OPERATION = "admin_operation"  # 管理员操作