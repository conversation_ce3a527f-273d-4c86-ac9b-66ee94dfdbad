"""
免费用户过期原始文件清理定时任务

该脚本用于定期清理免费用户超过指定天数的原始文件，但保留转录记录。
建议每天运行一次。

使用方法：
python -m crontab.free_user_file_cleanup
python -m crontab.free_user_file_cleanup --days 30 --dry-run
"""

import logging
import click
from datetime import datetime, timedelta
from app import app
from models.user import User
from models.transcription_file import TranscriptionFile
from models.file_storage import FileStorage
from models.purchase import Purchase
from models.subscription import Subscription
from models.appsumo_license import AppSumoLicense
from models import db, db_transaction
from constants.transcription import OriginalFileDeleteReason

logger = logging.getLogger(__name__)


def has_recent_payment(user_id, days=180):
    """
    检查用户在指定天数内是否有付费记录
    
    Args:
        user_id: 用户ID
        days: 检查的天数，默认180天
        
    Returns:
        bool: 如果有付费记录返回True，否则返回False
    """
    cutoff_date = datetime.now() - timedelta(days=days)
    
    # 检查购买记录
    recent_purchase = Purchase.query.filter(
        Purchase.user_id == user_id,
        Purchase.created_time >= cutoff_date
    ).first()
    
    if recent_purchase:
        logger.info(f"User {user_id} has recent purchase: {recent_purchase.id}")
        return True
    
    # 检查订阅记录
    recent_subscription = Subscription.query.filter(
        Subscription.user_id == user_id,
        Subscription.created_time >= cutoff_date
    ).first()
    
    if recent_subscription:
        logger.info(f"User {user_id} has recent subscription: {recent_subscription.id}")
        return True
    
    # 检查AppSumo激活记录
    recent_appsumo = AppSumoLicense.query.filter(
        AppSumoLicense.user_id == user_id,
        AppSumoLicense.activation_time >= cutoff_date
    ).first()
    
    if recent_appsumo:
        logger.info(f"User {user_id} has recent AppSumo activation: {recent_appsumo.id}")
        return True
    
    return False


def _cleanup_anonymous_user_files(cutoff_date, batch_size, dry_run):
    """
    清理匿名用户的过期转录记录（软删除整个记录）

    Args:
        cutoff_date: 截止日期
        batch_size: 批处理大小
        dry_run: 是否为试运行模式

    Returns:
        dict: 包含处理统计信息的字典
    """
    stats = {'processed': 0, 'cleaned': 0, 'skipped': 0}

    # 直接 JOIN User 表，一步到位查询匿名用户的文件
    query = db.session.query(TranscriptionFile).join(
        User, TranscriptionFile.user_id == User.id
    ).filter(
        TranscriptionFile.created_time < cutoff_date,
        TranscriptionFile.original_file_deleted == False,
        TranscriptionFile.is_deleted == False,
        TranscriptionFile.fingerprint.isnot(None),
        TranscriptionFile.fingerprint != "",
        User.is_anonymous == True  # 只处理匿名用户
    ).order_by(TranscriptionFile.id)

    total_files = query.count()
    logger.info(f"Found {total_files} anonymous user transcriptions to delete")

    if total_files == 0:
        return stats

    # 使用游标分页处理
    last_id = 0
    batch_num = 1

    while True:
        batch_query = query.filter(TranscriptionFile.id > last_id).limit(batch_size)
        batch = batch_query.all()

        if not batch:
            break

        logger.info(f"Processing anonymous user batch {batch_num}, transcriptions {batch[0].id}-{batch[-1].id} ({len(batch)} records)")

        for file in batch:
            try:
                if not dry_run:
                    with db_transaction():
                        # 对于匿名用户，直接软删除整个转录记录
                        # 减少文件存储引用计数（只有在原文件未被单独删除时才减少）
                        if file.fingerprint and not file.original_file_deleted:
                            FileStorage.decrement_reference(file.user_id, file.fingerprint)

                        # 标记原文件已删除（用于审计追踪）
                        file.original_file_deleted = True
                        file.original_file_delete_reason = OriginalFileDeleteReason.AUTO_CLEANUP.value
                        file.original_file_deleted_at = datetime.now()

                        # 软删除转录记录
                        file.is_deleted = True

                        logger.info(f"Deleted anonymous user transcription: {file.id} ({file.filename})")
                else:
                    logger.info(f"[DRY RUN] Would delete anonymous user transcription: {file.id} ({file.filename})")

                stats['cleaned'] += 1

            except Exception as e:
                logger.error(f"Error processing anonymous user transcription {file.id}: {e}")
                stats['skipped'] += 1

        stats['processed'] += len(batch)
        last_id = batch[-1].id
        batch_num += 1

        logger.info(f"Anonymous user batch completed. Processed: {stats['processed']}, Deleted: {stats['cleaned']}, Skipped: {stats['skipped']}")

    logger.info(f"Anonymous user transcription cleanup completed: {stats}")
    return stats


def _cleanup_regular_free_user_files(cutoff_date, batch_size, dry_run):
    """
    清理普通免费用户的过期文件（排除匿名用户）

    Args:
        cutoff_date: 截止日期
        batch_size: 批处理大小
        dry_run: 是否为试运行模式

    Returns:
        dict: 包含处理统计信息的字典
    """
    stats = {'processed': 0, 'cleaned': 0, 'skipped': 0}

    # 查询需要处理的文件，排除匿名用户
    query = db.session.query(TranscriptionFile).join(
        User, TranscriptionFile.user_id == User.id
    ).filter(
        TranscriptionFile.created_time < cutoff_date,
        TranscriptionFile.original_file_deleted == False,
        TranscriptionFile.is_deleted == False,
        TranscriptionFile.fingerprint.isnot(None),
        TranscriptionFile.fingerprint != "",
        User.is_anonymous == False  # 排除匿名用户
    ).order_by(TranscriptionFile.id)

    total_files = query.count()
    logger.info(f"Found {total_files} regular free user files to check")

    if total_files == 0:
        return stats

    # 使用游标分页避免offset问题
    last_id = 0
    batch_num = 1

    while True:
        # 使用ID游标而不是offset，避免数据变更导致的跳过问题
        batch_query = query.filter(TranscriptionFile.id > last_id).limit(batch_size)
        batch = batch_query.all()

        if not batch:
            break

        logger.info(f"Processing regular free user batch {batch_num}, files {batch[0].id}-{batch[-1].id} ({len(batch)} files)")

        # 按用户分组处理
        user_files = {}
        for file in batch:
            if file.user_id not in user_files:
                user_files[file.user_id] = []
            user_files[file.user_id].append(file)

        for user_id, files in user_files.items():
            try:
                # 获取用户信息
                user = User.query.get(user_id)
                if not user:
                    logger.warning(f"User {user_id} not found, skipping files")
                    stats['skipped'] += len(files)
                    continue

                # 检查是否为付费用户
                if user.has_paid_plan:
                    logger.debug(f"User {user_id} has paid plan, skipping {len(files)} files")
                    stats['skipped'] += len(files)
                    continue

                # 检查是否最近 180 天内有付费记录
                if has_recent_payment(user_id, 180):
                    logger.info(f"User {user_id} has recent payment, skipping {len(files)} files")
                    stats['skipped'] += len(files)
                    continue

                # 处理该用户的文件
                logger.info(f"Processing {len(files)} files for free user {user_id}")

                for file in files:
                    if not dry_run:
                        with db_transaction():
                            # 减少文件存储引用计数
                            if file.fingerprint:
                                FileStorage.decrement_reference(file.user_id, file.fingerprint)

                            # 标记原文件已删除
                            file.original_file_deleted = True
                            file.original_file_delete_reason = OriginalFileDeleteReason.AUTO_CLEANUP.value
                            file.original_file_deleted_at = datetime.now()

                            # 清空文件相关字段
                            file.file_url = ""
                            file.file_key = ""

                            logger.info(f"Cleaned expired file: {file.id} ({file.filename})")
                    else:
                        logger.info(f"[DRY RUN] Would clean file: {file.id} ({file.filename})")

                    stats['cleaned'] += 1

            except Exception as e:
                logger.error(f"Error processing files for user {user_id}: {e}")
                stats['skipped'] += len(files)

        stats['processed'] += len(batch)
        last_id = batch[-1].id  # 更新游标为当前批次最后一个ID
        batch_num += 1

        logger.info(f"Regular free user batch completed. Processed: {stats['processed']}, Cleaned: {stats['cleaned']}, Skipped: {stats['skipped']}, Last ID: {last_id}")

    logger.info(f"Regular free user file cleanup completed: {stats}")
    return stats


def cleanup_free_user_expired_files(days=30, anonymous_days=1, dry_run=False):
    """
    清理免费用户过期的原始文件

    Args:
        days: 普通免费用户文件过期天数，默认30天
        anonymous_days: 匿名用户文件过期天数，默认1天
        dry_run: 是否为试运行模式
    """
    logger.info(f"Starting cleanup of free user expired files")
    logger.info(f"  Regular free users: delete original files older than {days} days")
    logger.info(f"  Anonymous users: delete entire transcriptions older than {anonymous_days} days")

    batch_size = 100
    total_processed = 0
    total_cleaned = 0
    total_skipped = 0

    # 处理匿名用户转录记录（更激进的策略：直接软删除）
    anonymous_cutoff_date = datetime.now() - timedelta(days=anonymous_days)
    logger.info(f"Processing anonymous user transcriptions for deletion (cutoff: {anonymous_cutoff_date})")

    anonymous_stats = _cleanup_anonymous_user_files(anonymous_cutoff_date, batch_size, dry_run)
    total_processed += anonymous_stats['processed']
    total_cleaned += anonymous_stats['cleaned']
    total_skipped += anonymous_stats['skipped']

    # 处理普通免费用户文件（原有策略：只删除原文件）
    regular_cutoff_date = datetime.now() - timedelta(days=days)
    logger.info(f"Processing regular free user files for original file cleanup (cutoff: {regular_cutoff_date})")

    regular_stats = _cleanup_regular_free_user_files(regular_cutoff_date, batch_size, dry_run)
    total_processed += regular_stats['processed']
    total_cleaned += regular_stats['cleaned']
    total_skipped += regular_stats['skipped']

    logger.info(f"Free user cleanup completed:")
    logger.info(f"  Total records processed: {total_processed}")
    logger.info(f"  Records cleaned/deleted: {total_cleaned}")
    logger.info(f"  Records skipped: {total_skipped}")
    logger.info(f"  Mode: {'DRY RUN' if dry_run else 'LIVE'}")


@click.command()
@click.option('--days', default=30, help='Regular free user: delete original files older than this many days (default: 30)')
@click.option('--anonymous-days', default=1, help='Anonymous user: delete entire transcriptions older than this many days (default: 1)')
@click.option('--dry-run', is_flag=True, help='Run in dry-run mode without making changes')
def main(days, anonymous_days, dry_run):
    """免费用户过期原始文件清理任务"""
    logging.basicConfig(
        level=logging.INFO, 
        format="%(asctime)s - %(levelname)s - %(message)s"
    )
    
    with app.app_context():
        try:
            logger.info("Free user file cleanup cron job started")
            logger.info(f"Parameters: days={days}, anonymous_days={anonymous_days}, dry_run={dry_run}")

            cleanup_free_user_expired_files(days=days, anonymous_days=anonymous_days, dry_run=dry_run)

            logger.info("Free user file cleanup cron job completed")
        except Exception as e:
            logger.exception(f"Free user file cleanup cron job failed with error: {str(e)}")
        finally:
            # 清理数据库会话
            db.session.remove()


if __name__ == "__main__":
    main()
