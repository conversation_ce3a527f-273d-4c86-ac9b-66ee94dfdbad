"""
Simplified field definitions for OpenAPI responses
"""

from flask_restful import fields
from fields.custom_fields import ISO8601DateTime
from constants.transcription import TranscriptionFileStatus


class OpenAPIStatusField(fields.Raw):
    """Custom field to map internal status to OpenAPI status"""
    
    @staticmethod
    def map_internal_status_to_openapi(internal_status_id):
        """
        Map internal transcription file status to OpenAPI status

        Args:
            internal_status_id: Internal status ID from TranscriptionFileStatus

        Returns:
            str: OpenAPI status string
        """
        status_mapping = {
            TranscriptionFileStatus.uploading.id: "queued",
            TranscriptionFileStatus.uploaded.id: "queued",
            TranscriptionFileStatus.preprocessing.id: "preprocessing",
            TranscriptionFileStatus.processing.id: "processing",
            TranscriptionFileStatus.partially_completed.id: "processing",
            TranscriptionFileStatus.completed.id: "completed",
            TranscriptionFileStatus.failed.id: "failed",
            TranscriptionFileStatus.completed_with_errors.id: "completed",
            TranscriptionFileStatus.preprocessing_failed.id: "failed"
        }

        return status_mapping.get(internal_status_id, "unknown")

    def format(self, value):
        """Map internal status ID or name to OpenAPI status string"""
        if value is None:
            return "unknown"

        # If value is already a string (status name), convert it back to ID first
        if isinstance(value, str):
            try:
                # Try to find the status by name and get its ID
                for status in TranscriptionFileStatus:
                    if status.name == value:
                        value = status.id
                        break
                else:
                    # If name not found, return unknown
                    return "unknown"
            except:
                return "unknown"

        return self.map_internal_status_to_openapi(value)


class OpenAPIErrorMessageField(fields.Raw):
    """Custom field to get error message for failed transcriptions"""

    def format(self, value):
        """Get error message for the transcription file"""
        if value is None:
            return None

        # Import here to avoid circular imports
        from controllers.task import get_transcription_file_error_message
        return get_transcription_file_error_message(value)


# Simplified word fields for segments
openapi_word_fields = {
    "start": fields.Float(attribute="start_time"),
    "end": fields.Float(attribute="end_time"),
    "text": fields.String(attribute="text"),
}

# Simplified segment fields
openapi_segment_fields = {
    "start": fields.Float(attribute="start_time"),
    "end": fields.Float(attribute="end_time"),
    "text": fields.String(attribute="text"),
    "speaker": fields.String(attribute="speaker"),
    "words": fields.List(fields.Nested(openapi_word_fields), attribute="words"),
}

# Simplified result fields
openapi_result_fields = {
    "text": fields.String(attribute="text"),
    "summary": fields.String(attribute="summary"),
    "outline": fields.String(attribute="outline"),
    "segments": fields.List(
        fields.Nested(openapi_segment_fields), attribute="segments"
    ),
}

# Core transcription fields for OpenAPI
openapi_transcription_fields = {
    "id": fields.String(attribute="id"),
    "filename": fields.String(attribute="filename"),
    "status": OpenAPIStatusField(attribute="status"),
    "duration": fields.Float(attribute="duration"),
    "language_code": fields.String(attribute="language_code"),
    "transcription_type": fields.String(attribute="transcription_type"),
    "created_at": ISO8601DateTime(attribute="created_time"),
    "completed_at": ISO8601DateTime(attribute="updated_time"),
    "file_size": fields.Integer(attribute="file_size"),
    "source_type": fields.String(attribute="source_type"),
    "source_url": fields.String(attribute="source_url"),
    "result": fields.Nested(openapi_result_fields, attribute="result"),
    "error_message": OpenAPIErrorMessageField(attribute="id"),
}

# Simplified transcription list fields (without result)
openapi_transcription_list_fields = {
    "id": fields.String(attribute="id"),
    "filename": fields.String(attribute="filename"),
    "status": OpenAPIStatusField(attribute="status"),
    "duration": fields.Float(attribute="duration"),
    "language_code": fields.String(attribute="language_code"),
    "transcription_type": fields.String(attribute="transcription_type"),
    "created_at": ISO8601DateTime(attribute="created_time"),
    "completed_at": ISO8601DateTime(attribute="updated_time"),
    "file_size": fields.Integer(attribute="file_size"),
    "source_type": fields.String(attribute="source_type"),
}

# Status response fields
openapi_status_fields = {
    "id": fields.String(attribute="id"),
    "status": OpenAPIStatusField(attribute="status"),
    "created_at": ISO8601DateTime(attribute="created_time"),
    "updated_at": ISO8601DateTime(attribute="updated_time"),
    "error_message": OpenAPIErrorMessageField(attribute="id"),
}

# Pagination fields for OpenAPI
openapi_pagination_fields = {
    "items": fields.List(fields.Nested(openapi_transcription_list_fields)),
    "has_more": fields.Boolean,
    "next_cursor": fields.String,
    "total": fields.Integer,
}
