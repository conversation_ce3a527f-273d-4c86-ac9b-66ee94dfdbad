from datetime import datetime

from constants.task import TaskStatus, TaskType
from models import db


class Task(db.Model):
    __tablename__ = "task"
    __table_args__ = (
        db.PrimaryKeyConstraint("id"),
        db.UniqueConstraint("file_id", "task_type", name="uniq_fileId_type"),
        db.Index("idx_createdTime", "created_time"),
        db.Index("idx_status", "status"),
    )

    id = db.Column(db.BigInteger, primary_key=True)
    file_id = db.Column(db.BigInteger)
    task_type = db.Column(db.Integer, nullable=False, default=1, comment="任务类型")
    status = db.Column(db.Integer, nullable=False, default=1, comment="任务状态")
    priority = db.Column(
        db.Integer,
        nullable=False,
        default=0,
        comment="任务优先级，0=免费用户，1=付费用户",
    )
    started_time = db.Column(db.TIMESTAMP, nullable=True, comment="任务开始时间")
    completed_time = db.Column(db.TIMESTAMP, nullable=True, comment="任务完成时间")
    error_message = db.Column(db.Text, nullable=True, comment="错误信息")
    created_time = db.Column(
        db.TIMESTAMP, nullable=False, default=datetime.now, comment="创建时间"
    )
    updated_time = db.Column(
        db.TIMESTAMP,
        nullable=False,
        default=datetime.now,
        onupdate=datetime.now,
        comment="更新时间",
    )
    transcription_type = db.Column(
        db.String(20),
        nullable=True,
        comment="转录类型：transcript/subtitle, 仅转录任务有效",
    )
    requested_service_provider = db.Column(
        db.String(50),
        nullable=True,
        comment="请求使用的服务提供商/模型，值应与Go服务中的AudioModel一致",
    )
    actual_service_provider = db.Column(
        db.String(50),
        nullable=True,
        comment="实际使用的服务提供商/模型，值应与Go服务中的AudioModel一致",
    )

    retry_count = db.Column(
        db.Integer,
        nullable=False,
        default=0,
        comment="重试次数"
    )

    def __repr__(self):
        return f"<TranscriptionTask {self.id}>"

    @classmethod
    def get_by_id(cls, id_):
        return cls.query.filter_by(id=id_).first()

    @classmethod
    def get_by_file_id(cls, file_id):
        return cls.query.filter_by(file_id=file_id).first()

    @classmethod
    def get_all_by_file_id(cls, file_id):
        return cls.query.filter_by(file_id=file_id).all()



    @classmethod
    def get_by_ids(cls, ids):
        # 按原顺序返回
        tasks = cls.query.filter(cls.id.in_(ids)).all()
        tasks_dict = {task.id: task for task in tasks}
        return [tasks_dict[id_] for id_ in ids]

    @classmethod
    def get_by_file_id_and_type(cls, file_id, task_type):
        """根据文件ID和任务类型获取任务"""
        return cls.query.filter_by(file_id=file_id, task_type=task_type).first()
