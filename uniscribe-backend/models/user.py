from sqlalchemy import func, text
from functools import lru_cache

from . import db, insert_record
from .subscription import Subscription
from .plan import Plan, PlanType
from .entitlement import Entitlement, EntitlementSource
from .purchase import Purchase
from constants.transcription import (
    FREE_USER_MAX_FILE_COUNT_PER_DAY,
    ANONYMOUS_USER_MAX_FILE_COUNT_PER_DAY,
)
from constants.subscription import SubscriptionStatus


class User(db.Model):
    __tablename__ = "user"
    __table_args__ = (
        db.PrimaryKeyConstraint("id"),
        db.UniqueConstraint("supabase_id"),
        {"comment": "用户信息表"},
    )

    id = db.Column(
        db.BigInteger, primary_key=True, autoincrement=True, comment="用户ID"
    )
    full_name = db.Column(db.String(255), nullable=False, comment="supabase 全名")
    supabase_id = db.Column(db.String(50), nullable=False, comment="supabase 用户ID")
    email = db.Column(db.String(50), nullable=False, comment="supabase 邮箱")
    avatar_url = db.Column(db.String(255), nullable=False, comment="supabase 用户头像")
    stripe_customer_id = db.Column(
        db.String(255), unique=True, nullable=True, comment="Stripe 客户 ID"
    )
    stripe_customer_source = db.Column(
        db.String(100), nullable=True, comment="Stripe 客户创建来源/入口统计"
    )
    is_deactivated = db.Column(
        db.Boolean, nullable=False, server_default=text("false"), comment="是否注销"
    )
    created_time = db.Column(
        db.TIMESTAMP, nullable=False, server_default=func.now(), comment="创建时间"
    )
    updated_time = db.Column(
        db.TIMESTAMP,
        nullable=False,
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间",
    )
    first_name = db.Column(db.String(255), nullable=False, default="")
    last_name = db.Column(db.String(255), nullable=False, default="")
    provider = db.Column(
        db.String(20),
        nullable=False,
        server_default="google",
    )
    is_anonymous = db.Column(
        db.Boolean, nullable=False, server_default=text("false"), comment="是否匿名"
    )

    # 用户偏好设置关联
    preferences = db.relationship(
        "UserPreferences",
        uselist=False,
        backref="user",
        lazy="joined",
        cascade="all, delete-orphan",
    )



    def __repr__(self):
        return f"<User {self.id}>"

    def to_dict(self, include_plan_details=False):
        """将用户对象转换为字典

        Args:
            include_plan_details: 是否包含计划详细信息

        Returns:
            dict: 用户信息字典
        """
        result = {c.name: getattr(self, c.name) for c in self.__table__.columns}

        # 添加基本计划信息
        result["primary_plan"] = self.primary_plan
        result["has_paid_plan"] = self.has_paid_plan

        # 如果需要，添加详细的计划信息
        if include_plan_details:
            result["primary_plan_detail"] = self.primary_plan_detail
            result["active_plans_detail"] = self.active_plans_detail

        return result

    @classmethod
    def create(cls, user):
        id_ = insert_record(user)
        return id_

    @classmethod
    def get_by_supabase_id(cls, supabase_id):
        return cls.query.filter_by(supabase_id=supabase_id).first()

    @classmethod
    def get_by_id(cls, user_id):
        return cls.query.get(user_id)

    @classmethod
    def set_stripe_customer_id(cls, user_id, stripe_customer_id):
        user = cls.get_by_id(user_id)
        user.stripe_customer_id = stripe_customer_id
        db.session.commit()

    @classmethod
    def set_stripe_customer_source(cls, user_id, source):
        """Set the Stripe customer creation source for a user"""
        user = cls.get_by_id(user_id)
        if user and not user.stripe_customer_source:  # Only set if not already set
            user.stripe_customer_source = source
            db.session.commit()

    @classmethod
    def get_by_stripe_customer_id(cls, stripe_customer_id):
        """Get user by Stripe customer ID"""
        return cls.query.filter_by(stripe_customer_id=stripe_customer_id).first()

    @lru_cache(maxsize=1)
    def _get_active_entitlements(self):
        """获取用户的所有有效权益（内部方法，用于缓存查询结果）
        使用 lru_cache 装饰器缓存结果，避免重复查询
        """
        return Entitlement.get_active_entitlements(self.id)

    @property
    def active_plans_detail(self):
        """返回用户所有有效计划的详细信息列表，按优先级排序
        优先级顺序：LTD > 订阅 > 一次性付费(按credit_amount降序) > Free > bonus

        Returns:
            list: 包含计划详细信息的字典列表，每个字典包括：
                - tier: 计划等级
                - plan_type: 计划类型 (subscription, ltd, one_time, free)
                - name: 计划名称
                - credit_amount: 计划积分额度
                - is_appsumo: 是否是 AppSumo 计划
        """
        # 获取所有有效权益
        active_entitlements = self._get_active_entitlements()

        # 创建不同类型计划的详细信息列表
        subscription_plans_info = []
        ltd_plans_info = []
        one_time_plans_info = []
        free_plan = False
        bonus_plans_info = []

        # 分类收集不同类型的计划详细信息
        for entitlement in active_entitlements:
            if entitlement.source_type == EntitlementSource.SUBSCRIPTION:
                subscription = Subscription.get_by_id(entitlement.source_id)
                if subscription:
                    plan = Plan.get_by_id(subscription.plan_id)
                    if plan and plan.tier not in [
                        p["tier"] for p in subscription_plans_info
                    ]:
                        subscription_plans_info.append(
                            {
                                "tier": plan.tier,
                                "plan_type": plan.plan_type,
                                "name": plan.name,
                                "credit_amount": plan.credit_amount,
                                "is_appsumo": False,  # 订阅计划不是 AppSumo
                            }
                        )
            elif entitlement.source_type == EntitlementSource.LTD:
                purchase = Purchase.get_by_id(entitlement.source_id)
                if purchase:
                    plan = Plan.get_by_id(purchase.plan_id)
                    if plan and plan.tier not in [p["tier"] for p in ltd_plans_info]:
                        # 检查是否是 AppSumo 计划
                        is_appsumo = (
                            plan.plan_type == PlanType.LTD
                            and plan.name.startswith("appsumo_")
                        )

                        ltd_plans_info.append(
                            {
                                "tier": plan.tier,
                                "plan_type": plan.plan_type,
                                "name": plan.name,
                                "credit_amount": plan.credit_amount,
                                "is_appsumo": is_appsumo,
                            }
                        )
            elif entitlement.source_type == EntitlementSource.ONE_TIME:
                purchase = Purchase.get_by_id(entitlement.source_id)
                if purchase:
                    plan = Plan.get_by_id(purchase.plan_id)
                    if plan and plan.tier not in [
                        p["tier"] for p in one_time_plans_info
                    ]:
                        one_time_plans_info.append(
                            {
                                "tier": plan.tier,
                                "plan_type": plan.plan_type,
                                "name": plan.name,
                                "credit_amount": plan.credit_amount,
                                "is_appsumo": False,  # 一般一次性计划不是 AppSumo
                            }
                        )
            elif entitlement.source_type == EntitlementSource.FREE_PLAN:
                free_plan = True
            elif entitlement.source_type in [
                EntitlementSource.BONUS,
                EntitlementSource.REFERRAL,
            ]:
                # 如果需要处理 bonus 和 referral 类型的计划
                pass

        # 按照优先级顺序构建最终的计划详细信息列表
        result_plans_info = []

        # 1. 添加 LTD 计划，按 credit_amount 降序排序
        if ltd_plans_info:
            ltd_plans_info.sort(key=lambda p: p["credit_amount"], reverse=True)
            result_plans_info.extend(ltd_plans_info)

        # 2. 添加订阅计划
        result_plans_info.extend(subscription_plans_info)

        # 3. 添加一次性付费计划，按 credit_amount 降序排序
        if one_time_plans_info:
            one_time_plans_info.sort(key=lambda p: p["credit_amount"], reverse=True)
            result_plans_info.extend(one_time_plans_info)

        # 4. 添加免费计划
        if free_plan and "Free" not in [p["tier"] for p in result_plans_info]:
            result_plans_info.append(
                {
                    "tier": "Free",
                    "plan_type": PlanType.FREE.value,  # 使用枚举的值 "free"
                    "name": "Free Plan",
                    "credit_amount": 0,  # 免费计划的积分额度可能需要从配置中获取
                    "is_appsumo": False,
                }
            )

        # 5. 添加奖励计划
        result_plans_info.extend(bonus_plans_info)

        # 如果没有任何计划，默认为免费计划
        if not result_plans_info:
            result_plans_info.append(
                {
                    "tier": "Free",
                    "plan_type": PlanType.FREE.value,  # 使用枚举的值 "free"
                    "name": "Free Plan",
                    "credit_amount": 0,
                    "is_appsumo": False,
                }
            )

        return result_plans_info

    @property
    def active_plans(self):
        """返回用户所有有效计划的列表，按优先级排序
        优先级顺序：LTD > 订阅 > 一次性付费(按credit_amount降序) > Free > bonus
        """
        return [plan["tier"] for plan in self.active_plans_detail]

    @property
    def primary_plan_detail(self):
        """根据优先级返回最高优先级的计划的详细信息
        优先级顺序：LTD > 订阅 > 一次性付费 > Free > bonus
        对于 LTD 和一次性付费计划，优先选择 credit_amount 更大的

        Returns:
            dict: 包含计划详细信息的字典，包括：
                - tier: 计划等级
                - plan_type: 计划类型 (subscription, ltd, one_time, free)
                - name: 计划名称
                - credit_amount: 计划积分额度
                - is_appsumo: 是否是 AppSumo 计划
        """
        # 获取所有有效权益
        active_entitlements = self._get_active_entitlements()

        # 检查是否有 LTD 计划，并选择 credit_amount 最大的
        ltd_plans_info = []
        for entitlement in active_entitlements:
            if entitlement.source_type == EntitlementSource.LTD:
                purchase = Purchase.get_by_id(entitlement.source_id)
                if purchase:
                    plan = Plan.get_by_id(purchase.plan_id)
                    if plan:
                        # 检查是否是 AppSumo 计划
                        is_appsumo = (
                            plan.plan_type == PlanType.LTD
                            and plan.name.startswith("appsumo_")
                        )

                        ltd_plans_info.append(
                            {
                                "tier": plan.tier,
                                "plan_type": plan.plan_type,
                                "name": plan.name,
                                "credit_amount": plan.credit_amount,
                                "is_appsumo": is_appsumo,
                            }
                        )

        # 如果有多个 LTD 计划，按 credit_amount 降序排序并返回最大的
        if ltd_plans_info:
            ltd_plans_info.sort(key=lambda p: p["credit_amount"], reverse=True)
            return ltd_plans_info[0]

        # 检查是否有订阅计划
        for entitlement in active_entitlements:
            if entitlement.source_type == EntitlementSource.SUBSCRIPTION:
                subscription = Subscription.get_by_id(entitlement.source_id)
                if subscription:
                    plan = Plan.get_by_id(subscription.plan_id)
                    if plan:
                        return {
                            "tier": plan.tier,
                            "plan_type": plan.plan_type,
                            "name": plan.name,
                            "credit_amount": plan.credit_amount,
                            "is_appsumo": False,  # 订阅计划不是 AppSumo
                        }

        # 检查是否有一次性付费计划，并选择 credit_amount 最大的
        one_time_plans_info = []
        for entitlement in active_entitlements:
            if entitlement.source_type == EntitlementSource.ONE_TIME:
                purchase = Purchase.get_by_id(entitlement.source_id)
                if purchase:
                    plan = Plan.get_by_id(purchase.plan_id)
                    if plan:
                        one_time_plans_info.append(
                            {
                                "tier": plan.tier,
                                "plan_type": plan.plan_type,
                                "name": plan.name,
                                "credit_amount": plan.credit_amount,
                                "is_appsumo": False,  # 一般一次性计划不是 AppSumo
                            }
                        )

        # 如果有多个一次性计划，按 credit_amount 降序排序并返回最大的
        if one_time_plans_info:
            one_time_plans_info.sort(key=lambda p: p["credit_amount"], reverse=True)
            return one_time_plans_info[0]

        # 如果没有付费计划，返回免费计划
        return {
            "tier": "Free",
            "plan_type": PlanType.FREE.value,  # 使用枚举的值 "free"
            "name": "Free Plan",
            "credit_amount": 0,  # 免费计划的积分额度可能需要从配置中获取
            "is_appsumo": False,
        }

    @property
    def primary_plan(self):
        """根据优先级返回最高优先级的计划
        优先级顺序：LTD > 订阅 > 一次性付费 > Free > bonus
        对于 LTD 和一次性付费计划，优先选择 credit_amount 更大的
        """
        return self.primary_plan_detail["tier"]

    @property
    def active_plan(self):
        """为了向后兼容，返回主要计划"""
        return self.primary_plan

    @property
    def has_paid_plan(self):
        """检查用户是否有付费计划"""
        return self.primary_plan != "Free"

    @property
    def has_active_subscription(self):
        """检查用户是否有活跃订阅（不包括一次性付费计划）"""
        # 获取所有有效权益
        active_entitlements = self._get_active_entitlements()

        # 检查是否有订阅类型的权益
        for entitlement in active_entitlements:
            if entitlement.source_type == EntitlementSource.SUBSCRIPTION:
                subscription = Subscription.get_by_id(entitlement.source_id)
                if subscription.status == SubscriptionStatus.ACTIVE.value:
                    return True

        return False

    @property
    def display_name(self):
        """Return the best available name for display"""
        if self.full_name:
            return self.full_name
        elif self.first_name or self.last_name:
            return f"{self.first_name} {self.last_name}".strip()
        return self.email.split("@")[0]

    @classmethod
    def get_by_email(cls, email):
        return cls.query.filter_by(email=email).first()

    @property
    def daily_transcribe_limit(self):
        """
        获取用户每日上传文件数量限制
        Returns:
            int: 用户每日可上传的最大文件数
        """
        if self.is_anonymous:
            return ANONYMOUS_USER_MAX_FILE_COUNT_PER_DAY
        if self.has_paid_plan:  # 使用新的 has_paid_plan 属性
            return float("inf")  # 无限制
        return FREE_USER_MAX_FILE_COUNT_PER_DAY

    def get_preferences(self):
        """获取用户偏好设置，如果不存在则创建默认设置

        Returns:
            UserPreferences: 用户偏好设置对象
        """
        if not self.preferences:
            from .user_preferences import UserPreferences

            preferences = UserPreferences(user_id=self.id)
            db.session.add(preferences)
            db.session.flush()
            db.session.commit()
            self.preferences = preferences
        return self.preferences

    @property
    def has_api_access(self):
        """检查用户是否有API访问权限

        Returns:
            bool: 仅订阅和LTD用户有API权限
        """
        active_plans = self.active_plans_detail

        # 检查是否有订阅或LTD计划
        for plan in active_plans:
            if plan["plan_type"] in [PlanType.SUBSCRIPTION.value, PlanType.LTD.value]:
                return True

        return False
