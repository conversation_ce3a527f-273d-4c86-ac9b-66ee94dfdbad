from datetime import datetime

from models import db
from constants.transcription import TranscriptionFileStatus


class TranscriptionFile(db.Model):
    __tablename__ = "transcription_file"
    __table_args__ = (
        db.PrimaryKeyConstraint("id"),
        db.Index("idx_userId", "user_id"),
        db.Index("idx_fingerprint", "fingerprint"),
        db.Index("idx_createdTime", "created_time"),
    )

    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    user_id = db.Column(db.BigInteger, nullable=False)
    filename = db.Column(db.String(100), nullable=False)
    file_type = db.Column(db.String(20), nullable=False)
    file_url = db.Column(db.String(255), nullable=False)
    file_key = db.Column(db.String(255), nullable=True)
    file_size = db.Column(db.Integer, nullable=False)
    fingerprint = db.Column(db.String(32), nullable=False)
    duration = db.Column(db.Float, nullable=False)
    language_code = db.Column(db.String(20), nullable=True)
    language = db.Column(db.String(40), nullable=True)
    status = db.Column(db.Integer, nullable=False, default=0)
    uploaded_time = db.Column(db.TIMESTAMP, nullable=True)
    is_deleted = db.Column(db.Boolean, nullable=False, default=False)
    created_time = db.Column(db.TIMESTAMP, nullable=False)
    updated_time = db.Column(
        db.TIMESTAMP,
        nullable=False,
        default=datetime.now,
        onupdate=datetime.now,
    )
    transcription_type = db.Column(
        db.String(20),
        nullable=False,
        default="transcript",
        comment="转录类型：transcript/subtitle",
    )
    insufficient_minutes = db.Column(
        db.Integer,
        nullable=False,
        default=0,
        comment="欠费分钟数。0表示未欠费，>0表示欠费分钟数",
    )

    source_type = db.Column(
        db.Enum(
            "upload",
            "youtube",
            name="source_type_enum",
        ),
        nullable=False,
        default="upload",
        comment="文件来源类型：upload/youtube 等",
    )

    source_url = db.Column(
        db.Text,
        nullable=True,
        comment="媒体来源的URL，根据source_type不同而有不同含义",
    )

    media_download_time = db.Column(
        db.Integer,
        nullable=False,
        default=0,
        comment="媒体下载和上传的总时间（秒），适用于YouTube等外部媒体源",
    )

    media_processing_time = db.Column(
        db.Integer,
        nullable=False,
        default=0,
        comment="媒体预处理时间（秒），适用于需要预处理的媒体源",
    )

    enable_speaker_diarization = db.Column(
        db.Boolean,
        nullable=False,
        default=False,
        comment="是否开启说话人识别",
    )
    
    folder_id = db.Column(
        db.BigInteger,
        nullable=True,
        comment="所属文件夹ID，NULL表示在默认文件夹",
    )

    webhook_url = db.Column(
        db.String(500),
        nullable=True,
        comment="Webhook URL for completion notifications (OpenAPI)",
    )

    needs_preprocessing = db.Column(
        db.Boolean,
        nullable=False,
        default=False,
        comment="是否需要预处理（如提取duration等）",
    )

    original_file_deleted = db.Column(
        db.Boolean,
        nullable=False,
        default=False,
        comment="原文件是否已被删除，用于标识只保留转录记录的情况",
    )

    original_file_delete_reason = db.Column(
        db.Enum(
            "user_manual",
            "auto_cleanup",
            "admin_operation",
            name="delete_reason_enum",
        ),
        nullable=True,
        comment="原文件删除原因",
    )

    original_file_deleted_at = db.Column(
        db.TIMESTAMP,
        nullable=True,
        comment="原文件删除时间",
    )

    def __repr__(self):
        return f"<TranscriptionFile {self.id}>"

    @property
    def can_play(self):
        """Check if the original file can be played (not deleted)"""
        return not self.original_file_deleted and bool(self.file_url)

    @classmethod
    def get_by_id(cls, id_):
        return cls.query.filter_by(id=id_, is_deleted=False).first()

    @classmethod
    def get_by_fingerprint(cls, fingerprint):
        return cls.query.filter_by(fingerprint=fingerprint, is_deleted=False).first()

    @classmethod
    def get_by_user_id(cls, user_id):
        return cls.query.filter_by(user_id=user_id, is_deleted=False).all()

    @classmethod
    def get_by_user_id_and_fingerprint(cls, user_id, fingerprint):
        return cls.query.filter_by(
            user_id=user_id, fingerprint=fingerprint, is_deleted=False
        ).first()

    @classmethod
    def get_by_user_id_and_status(cls, user_id, status):
        return cls.query.filter_by(
            user_id=user_id, status=status, is_deleted=False
        ).all()

    @classmethod
    def list(cls, user_id, cursor, limit, status_list):
        return (
            cls.query.filter(
                cls.id < cursor,
                cls.user_id == user_id,
                cls.status.in_(status_list),
                cls.is_deleted == False,
            )
            .order_by(cls.id.desc())
            .limit(limit)
            .all()
        )

    @classmethod
    def get_files_by_status(cls, status, min_created_time):
        return (
            cls.query.filter(
                cls.status == status,
                cls.created_time >= min_created_time,
                cls.is_deleted == False,
            )
            .order_by(cls.id.desc())
            .all()
        )

    # 计算用户当天转录的文件数量
    @classmethod
    def get_user_today_transcribe_file_count(cls, user_id):
        completed_statuses = [
            TranscriptionFileStatus.preprocessing.id,
            TranscriptionFileStatus.processing.id,
            TranscriptionFileStatus.completed.id,
            TranscriptionFileStatus.partially_completed.id,
            TranscriptionFileStatus.completed_with_errors.id,
        ]

        return cls.query.filter(
            cls.user_id == user_id,
            cls.created_time >= datetime.now().date(),
            cls.status.in_(completed_statuses),
            # cls.is_deleted == False,  删除的文件也算
        ).count()

    @classmethod
    def search_by_filename(cls, user_id, filename_keyword, limit=20):
        """
        Search files by filename using MySQL's built-in full Unicode support

        Args:
            user_id: User ID
            filename_keyword: Search keyword (supports all Unicode characters)
            limit: Maximum number of results to return
        """
        return (
            cls.query.filter(
                cls.user_id == user_id,
                cls.filename.like(
                    f"%{filename_keyword}%"
                ),  # MySQL 8.0's ai_ci collation handles case-insensitive
                cls.is_deleted == False,
            )
            .order_by(cls.created_time.desc())
            .limit(limit)
            .all()
        )
