#!/usr/bin/env python3
"""
媒体预处理消费者
独立的 Python 进程，消费 media_preprocessing 队列并处理媒体预处理任务
"""

import os
import sys
import json
import logging
import time
import signal
import threading
import queue
import atexit
from datetime import datetime, timezone
from contextlib import contextmanager
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from flask import Flask
from config import CONFIG
from models import db
from app import app as shared_app
from models.task import Task
from models.transcription_file import TranscriptionFile
from models.user import User
from models.user_preferences import UserPreferences
from constants.task import TaskStatus, TaskType
from controllers.youtube import YoutubeTranscriber
from controllers.task import update_file_status
from services.task_queue_service import TaskQueueService
from libs.storage.factory import StorageFactory
from libs.ffmpeg_preprocessing import (
    FFmpegProcessor,
    generate_processed_file_key,
    check_ffmpeg_availability,
    DEFAULT_AUDIO_FORMAT,
)
from constants.transcription import (
    VIDEO_FORMATS_REQUIRING_PREPROCESSING,
    FFMPEG_PREPROCESSING_SIZE_THRESHOLD,
)
from constants.transcription import TranscriptionFileStatus
from constants.storage import DOWNLOAD_TIMEOUT
import redis
from redis.exceptions import ResponseError
import requests
import tempfile
import shutil

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class MediaPreprocessingConsumer:
    """媒体预处理消费者 - 支持并发处理"""

    def __init__(self, max_workers=3):
        # 首先检查 ffmpeg 可用性
        if not check_ffmpeg_availability():
            logger.error("ffmpeg is not available on this system")
            logger.error(
                "Please install ffmpeg before starting the media preprocessing consumer"
            )
            logger.error("Installation instructions:")
            logger.error("  Ubuntu/Debian: sudo apt update && sudo apt install ffmpeg")
            logger.error(
                "  CentOS/RHEL: sudo yum install epel-release && sudo yum install ffmpeg"
            )
            logger.error("  macOS: brew install ffmpeg")
            raise RuntimeError(
                "ffmpeg is not available. Media preprocessing consumer cannot start."
            )

        logger.info("✅ ffmpeg is available and ready for media preprocessing")

        self.app = shared_app  # 使用共享的 app 实例
        self.queue_service = None  # 延迟初始化
        self.storage = None  # 将在 app context 中初始化
        self.running = True
        self.stream_name = "tasks:media_preprocessing"
        self.consumer_group = "workers"
        self.consumer_name = "media-processor-1"

        # 并发处理配置
        self.max_workers = max_workers
        self.task_queue = queue.Queue(maxsize=max_workers * 2)  # 任务队列
        self.executor = None  # 线程池执行器

        # 统计信息
        self.stats = {"processed": 0, "failed": 0, "active_workers": 0}
        self.stats_lock = threading.Lock()

        # 设置信号处理
        signal.signal(signal.SIGTERM, self._signal_handler)
        signal.signal(signal.SIGINT, self._signal_handler)



    def _signal_handler(self, signum, frame):
        """处理退出信号"""
        logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.running = False

        # 停止接收新任务
        if self.executor:
            logger.info("Shutting down thread pool executor...")
            self.executor.shutdown(wait=True)
            logger.info("Thread pool executor shutdown complete")

    def _update_stats(self, operation):
        """更新统计信息"""
        with self.stats_lock:
            if operation == "processed":
                self.stats["processed"] += 1
            elif operation == "failed":
                self.stats["failed"] += 1
            elif operation == "worker_start":
                self.stats["active_workers"] += 1
            elif operation == "worker_end":
                self.stats["active_workers"] -= 1

    def _log_stats(self):
        """记录统计信息"""
        with self.stats_lock:
            logger.info(
                f"Stats - Processed: {self.stats['processed']}, "
                f"Failed: {self.stats['failed']}, "
                f"Active workers: {self.stats['active_workers']}"
            )

    def _parse_task_data(self, fields):
        """
        简化消息解析：直接从外层字段读取

        新的设计：所有字段都在外层，直接读取即可
        """
        logger.debug("Parsing task data from message fields")

        # 直接从字段解析，所有值都是字符串，需要转换类型
        task_data = {
            # 核心字段
            "id": int(fields.get("task_id", 0)),
            "task_type": int(fields.get("task_type", 0)),
            "file_id": int(fields.get("file_id", 0)),
            "priority": int(fields.get("priority", 1)),
            "created_time": fields.get("created_time", ""),
            # 现有字段
            "file_url": fields.get("file_url", ""),
            "transcription_text": fields.get("transcription_text", ""),
            "file_duration": float(fields.get("file_duration", 0)),
            "language_code": fields.get("language_code", ""),
            "language": fields.get("language", ""),
            "transcription_type": fields.get("transcription_type", ""),
            "requested_service_provider": fields.get("requested_service_provider", ""),
            # 扩展字段（新增）
            "user_id": int(fields.get("user_id", 0)),
            "youtube_url": fields.get("youtube_url", ""),
            "title": fields.get("title", ""),
            "preprocessing_type": fields.get("preprocessing_type", "youtube"),
        }

        return task_data

    @contextmanager
    def app_context(self):
        """Flask 应用上下文管理器"""
        with self.app.app_context():
            yield

    def consume_tasks(self):
        """消费媒体预处理任务 - 支持并发处理"""
        logger.info(
            f"Starting media preprocessing consumer with {self.max_workers} workers..."
        )

        with self.app_context():
            # 初始化队列服务（在 app context 中）
            if self.queue_service is None:
                self.queue_service = TaskQueueService()
                logger.info("Queue service initialized")

            # 启动线程池执行器
            self.executor = ThreadPoolExecutor(
                max_workers=self.max_workers, thread_name_prefix="MediaWorker"
            )
            logger.info(f"Thread pool executor started with {self.max_workers} workers")

            # 启动消息消费线程
            consumer_thread = threading.Thread(
                target=self._consume_messages, name="MessageConsumer"
            )
            consumer_thread.daemon = True
            consumer_thread.start()

            # 启动任务处理循环
            self._process_task_queue()

        logger.info("Media preprocessing consumer stopped.")

    def _consume_messages(self):
        """Consume messages from Redis Stream and put them into task queue"""
        logger.info("Starting message consumer thread...")

        while self.running:
            try:
                # Read messages from Redis Stream
                streams = self.queue_service.redis_client.xreadgroup(
                    self.consumer_group,
                    self.consumer_name,
                    {self.stream_name: ">"},
                    count=1,
                    block=1000,  # 1秒超时
                )

                if not streams:
                    continue

                # 将消息放入任务队列
                for stream_name, messages in streams:
                    logger.info(f"📨 从流 {stream_name} 收到 {len(messages)} 条消息")
                    for message_id, fields in messages:
                        # 提取任务ID用于日志
                        task_id = self._extract_task_id(fields)

                        logger.info(
                            f"📨 收到消息: message_id={message_id}, task_id={task_id}, stream={stream_name}"
                        )
                        logger.debug(f"📨 消息字段: {fields}")

                        # 将消息放入任务队列，如果队列满了会无限等待（像Go一样）
                        task_item = {
                            "message_id": message_id,
                            "fields": fields,
                            "stream_name": stream_name,
                        }
                        logger.info(
                            f"🔄 准备将消息放入任务队列: message_id={message_id}, task_id={task_id}"
                        )
                        # 移除timeout，无限等待直到队列有空间（与Go消费者行为一致）
                        self.task_queue.put(task_item)
                        logger.info(
                            f"✅ 消息已放入任务队列: message_id={message_id}, task_id={task_id}"
                        )

            except ResponseError as e:
                if "NOGROUP" in str(e):
                    logger.warning("Consumer group not found, will retry...")
                    time.sleep(5)
                else:
                    logger.error(f"Redis error: {e}")
                    time.sleep(1)
            except Exception as e:
                logger.error(f"Unexpected error in message consumer: {e}")
                time.sleep(1)

        logger.info("Message consumer thread stopped")

    def _process_task_queue(self):
        """处理任务队列中的任务"""
        logger.info("Starting task processing loop...")

        # 用于跟踪正在执行的任务
        futures = set()
        last_stats_time = time.time()

        while self.running or not self.task_queue.empty() or futures:
            try:
                # 清理已完成的任务
                completed_futures = set()
                for future in futures:
                    if future.done():
                        completed_futures.add(future)
                        try:
                            future.result()  # 获取结果，如果有异常会抛出
                            self._update_stats("processed")
                        except Exception as e:
                            logger.error(f"Task processing failed: {e}")
                            self._update_stats("failed")

                futures -= completed_futures

                # 如果有空闲的工作线程，从队列中获取新任务
                if len(futures) < self.max_workers and self.running:
                    try:
                        task_item = self.task_queue.get(timeout=1)
                        # 提交任务到线程池
                        future = self.executor.submit(
                            self._process_task_item, task_item
                        )
                        futures.add(future)
                        self._update_stats("worker_start")
                    except queue.Empty:
                        pass  # 队列为空，继续循环

                # 定期记录统计信息
                current_time = time.time()
                if current_time - last_stats_time > 30:  # 每30秒记录一次
                    self._log_stats()
                    last_stats_time = current_time

                # 短暂休眠避免CPU占用过高
                time.sleep(0.1)

            except Exception as e:
                logger.error(f"Unexpected error in task processing loop: {e}")
                time.sleep(1)

        logger.info("Task processing loop stopped")

    def _extract_task_id(self, fields):
        """从消息字段中提取任务ID"""
        for field_name in ["task_id", "id"]:
            if field_name in fields and fields[field_name]:
                try:
                    return int(fields[field_name])
                except (ValueError, TypeError):
                    continue
        return None

    def _process_task_item(self, task_item):
        """处理单个任务项（在工作线程中执行）"""
        message_id = task_item["message_id"]
        fields = task_item["fields"]
        stream_name = task_item["stream_name"]

        # 提前提取任务ID，供后续使用
        task_id = self._extract_task_id(fields)

        # 为每个工作线程创建独立的 Flask 应用上下文
        with self.app.app_context():
            logger.info(
                f"🚀 开始处理任务: message_id={message_id}, task_id={task_id}, thread={threading.current_thread().name}"
            )

            # 先确认并删除消息，避免重复处理和队列积压
            logger.info(
                f"🔍 准备确认并删除消息: message_id={message_id}, task_id={task_id}"
            )
            ack_success = self.queue_service.acknowledge_and_delete_message(
                "media_preprocessing", message_id, self.consumer_group
            )
            logger.info(
                f"🔍 确认删除结果: message_id={message_id}, task_id={task_id}, ack_success={ack_success}"
            )

            if not ack_success:
                logger.error(
                    f"❌ 确认删除消息失败: message_id={message_id}, task_id={task_id}"
                )
                self._update_stats("worker_end")
                return

            logger.info(
                f"✅ 消息已确认并删除，开始处理: message_id={message_id}, task_id={task_id}"
            )

            try:
                start_time = time.time()
                self._process_message(message_id, fields, task_id)

                processing_time = time.time() - start_time
                logger.info(
                    f"Message {message_id} (task_id={task_id}) processed successfully in {processing_time:.2f}s"
                )

                # 记录长时间处理的警告
                if processing_time > 300:  # 5分钟
                    logger.warning(
                        f"Long processing time for message {message_id} (task_id={task_id}): {processing_time:.2f}s"
                    )

            except Exception as e:
                processing_time = (
                    time.time() - start_time if "start_time" in locals() else 0
                )
                logger.error(
                    f"Error processing message {message_id} (task_id={task_id}) after {processing_time:.2f}s: {e}"
                )

                if task_id:
                    logger.error(
                        f"Task {task_id} failed processing, consider manual intervention"
                    )

            finally:
                self._update_stats("worker_end")

    def _process_message(self, message_id, fields, task_id):
        """Process a single message"""
        logger.info(f"Processing message {message_id} (task_id={task_id}): {fields}")

        preprocessing_type = fields.get("preprocessing_type", "youtube")

        if not task_id:
            logger.error(f"Invalid task_id in message {message_id}")
            logger.error(f"Available fields: {list(fields.keys())}")
            logger.error(
                f"Field values: task_id={fields.get('task_id')}, id={fields.get('id')}"
            )
            return

        # 更新任务状态为处理中
        self._update_task_status(task_id, TaskStatus.processing.id)

        try:
            if preprocessing_type == "youtube":
                self._process_youtube_task(task_id, fields)
            elif preprocessing_type == "ffmpeg":
                self._process_ffmpeg_task(task_id, fields)
            elif preprocessing_type == "url_download":
                self._process_url_download_task(task_id, fields)
            else:
                logger.error(f"Unsupported preprocessing type: {preprocessing_type}")
                self._update_task_status(
                    task_id,
                    TaskStatus.failed.id,
                    f"Unsupported preprocessing type: {preprocessing_type}",
                )

        except Exception as e:
            logger.error(f"Error processing task {task_id}: {e}")
            self._update_task_status(task_id, TaskStatus.failed.id, str(e))

    def _process_youtube_task(self, task_id, fields):
        """Process YouTube download task"""
        logger.info(f"Processing YouTube task {task_id}")

        # Parse task data: read directly from outer fields (single-layer structure design)
        task_data = self._parse_task_data(fields)

        # 提取任务参数
        file_id = int(task_data.get("file_id", 0))
        user_id = int(task_data.get("user_id", 0))
        youtube_url = task_data.get("youtube_url", "")

        # 校验参数是否为默认值
        if file_id == 0:
            raise ValueError(f"Invalid file_id: {file_id}, task_id: {task_id}")
        if user_id == 0:
            raise ValueError(f"Invalid user_id: {user_id}, task_id: {task_id}")
        if not youtube_url:
            raise ValueError(f"Invalid youtube_url: empty, task_id: {task_id}")
        title = task_data.get("title", "")
        duration = int(task_data.get("file_duration", 0))  # 统一使用 file_duration

        logger.info(
            f"YouTube task parameters: url={youtube_url}, file_id={file_id}, user_id={user_id}"
        )

        # 创建 YoutubeTranscriber 实例
        transcriber = YoutubeTranscriber(
            user_id, youtube_url, title, duration, self.app.storage
        )

        # 获取转录文件
        tf = TranscriptionFile.get_by_id(file_id)
        if not tf:
            raise Exception(f"Transcription file not found: {file_id}")

        transcriber.tf = tf

        try:
            # 执行 YouTube 下载和处理流程
            # 1. 下载音频/视频
            t1 = time.time()
            transcriber.download_audio()
            t2 = time.time()
            download_time = t2 - t1
            logger.info("Download audio time: %.2f seconds", download_time)

            # 2. 检查是否需要ffmpeg预处理
            ffmpeg_time = 0
            if transcriber.filepath and self._check_if_needs_ffmpeg_processing(
                transcriber.filepath
            ):
                logger.info("Downloaded file needs ffmpeg preprocessing")
                t3 = time.time()
                # 明确指定输出格式为 wav
                output_format = DEFAULT_AUDIO_FORMAT
                processed_file = self._process_with_ffmpeg(
                    transcriber.filepath, transcriber.temp_dir, output_format=output_format
                )
                transcriber.filepath = processed_file  # 更新为处理后的文件
                # 如果文件经过了ffmpeg处理，更新文件类型为输出格式
                if tf:
                    tf.file_type = output_format
                t4 = time.time()
                ffmpeg_time = t4 - t3
                logger.info("ffmpeg processing time: %.2f seconds", ffmpeg_time)

            # 3. 上传最终文件到R2
            t5 = time.time()
            transcriber.upload_to_r2()
            t6 = time.time()
            upload_time = t6 - t5
            logger.info("Upload to R2 time: %.2f seconds", upload_time)

            # 计算并保存总处理时间（下载+可选ffmpeg+上传）
            # TODO：后续废弃 media_download_time， 统一用 media_processing_time
            total_processing_time = int(download_time + ffmpeg_time + upload_time)
            tf.media_download_time = total_processing_time
            tf.media_processing_time = total_processing_time
            db.session.commit()
            logger.info(
                "Total media processing time: %d seconds (download: %.1f, ffmpeg: %.1f, upload: %.1f)",
                total_processing_time,
                download_time,
                ffmpeg_time,
                upload_time,
            )

            # 3. 触发转录
            t5 = time.time()
            transcriber.trigger_transcription()
            t6 = time.time()
            logger.info("Trigger transcription time: %.2f seconds", t6 - t5)

            # 标记媒体预处理任务为完成
            self._update_task_status(task_id, TaskStatus.completed.id)

            logger.info(f"YouTube task {task_id} completed successfully")

        except Exception as e:
            # 重新抛出异常让上层处理
            raise
        finally:
            # Clean up temporary files
            transcriber.cleanup()

    def _check_if_needs_ffmpeg_processing(self, file_path: str) -> bool:
        """Check if the downloaded file needs ffmpeg preprocessing"""
        if not file_path or not os.path.exists(file_path):
            return False

        # Check file extension
        file_ext = os.path.splitext(file_path)[1].lower()

        # If it's a video format, need to extract audio
        if file_ext in VIDEO_FORMATS_REQUIRING_PREPROCESSING:
            logger.info(f"Video format {file_ext} needs ffmpeg processing")
            return True

        # If it's webm format, convert to more universal format (for user download convenience)
        if file_ext == ".webm":
            logger.info(
                f"WebM format needs conversion to more universal format for user download"
            )
            return True

        # Check file size (although YouTube downloaded files are usually not too large, but just to be safe)
        file_size = os.path.getsize(file_path)
        if file_size > FFMPEG_PREPROCESSING_SIZE_THRESHOLD:
            logger.info(f"Large file {file_size} bytes needs ffmpeg processing")
            return True

        return False

    def _process_with_ffmpeg(self, input_file: str, transcriber_temp_dir: str, output_format: str = "wav") -> str:
        """
        使用ffmpeg处理文件

        Args:
            input_file: 输入文件路径
            transcriber_temp_dir: transcriber的临时目录，作为ffmpeg的父目录
            output_format: 输出格式，默认为wav

        Returns:
            str: 处理后的文件路径
        """
        # 在transcriber的临时目录下创建ffmpeg子目录
        processor = FFmpegProcessor(self.app.storage, parent_dir=transcriber_temp_dir)
        # 提取音频 - YouTube 下载的文件通常不需要格式提示
        processed_file = processor.extract_audio(
            input_file, output_format=output_format
        )
        logger.info(f"ffmpeg processing completed: {processed_file}")

        # 不需要调用 processor.cleanup()，因为ffmpeg子目录会随着transcriber目录一起被清理
        # 文件会在YouTube任务完成后由transcriber.cleanup()统一清理
        return processed_file

    def _update_task_status(self, task_id, status, error_message=None):
        """更新任务状态"""
        try:
            task = Task.get_by_id(task_id)
            if task:
                task.status = status
                if error_message:
                    task.error_message = error_message[:500]  # 限制错误消息长度
                if status == TaskStatus.processing.id:
                    task.started_time = datetime.now()
                elif status in [TaskStatus.completed.id, TaskStatus.failed.id]:
                    task.completed_time = datetime.now()
                db.session.commit()
                logger.info(f"Updated task {task_id} status to {status}")

                # 更新文件状态
                if task.file_id:
                    update_file_status(task.file_id)

        except Exception as e:
            logger.error(f"Error updating task status: {e}")
            db.session.rollback()

    def _process_ffmpeg_task(self, task_id, fields):
        """Process ffmpeg preprocessing task"""
        logger.info(f"Processing ffmpeg task {task_id}")

        # Parse task data
        task_data = self._parse_task_data(fields)
        file_id = int(task_data.get("file_id", 0))
        user_id = int(task_data.get("user_id", 0))
        preprocessing_reason = task_data.get("preprocessing_reason", "unknown")

        # 校验参数是否为默认值
        if file_id == 0:
            raise ValueError(f"Invalid file_id: {file_id}, task_id: {task_id}")
        if user_id == 0:
            raise ValueError(f"Invalid user_id: {user_id}, task_id: {task_id}")

        logger.info(
            f"ffmpeg task parameters: file_id={file_id}, user_id={user_id}, reason={preprocessing_reason}"
        )

        # 获取转录文件记录
        tf = TranscriptionFile.get_by_id(file_id)
        if not tf:
            raise Exception(f"Transcription file not found: {file_id}")

        # 更新文件状态为预处理中
        tf.status = TranscriptionFileStatus.preprocessing.id
        db.session.commit()

        processor = FFmpegProcessor(self.app.storage)
        local_file_path = None

        try:
            # 1. 下载原始文件到processor的临时目录
            t1 = time.time()
            local_file_path = self._download_file_from_storage(tf.file_key, tf.filename, processor.temp_dir)
            t2 = time.time()
            download_time = t2 - t1
            logger.info("Download original file time: %.2f seconds", download_time)

            # 2. 获取媒体信息
            media_info = processor.get_media_info(local_file_path)
            duration = processor.extract_duration_from_media_info(media_info)

            # 检查是否成功提取到 duration
            if duration <= 0:
                logger.error(f"Failed to extract valid duration from file: {duration}")
                # 标记文件状态为预处理失败
                tf.status = TranscriptionFileStatus.preprocessing_failed.id
                db.session.commit()
                raise Exception(f"Failed to extract valid duration from file: {duration}")

            logger.info(f"Successfully extracted duration: {duration} seconds")

            # 如果原文件的 duration 为 0，更新它（这种情况是前端无法读取 duration）
            if tf.duration == 0:
                logger.info(f"Updating file duration from 0 to {duration} seconds")
                tf.duration = duration
                tf.needs_preprocessing = False  # 标记预处理需求已完成

            # 3. ffmpeg 音频提取
            t3 = time.time()
            # 明确指定输出格式为 wav
            output_format = DEFAULT_AUDIO_FORMAT

            # 检查是否需要输入格式提示（基于媒体信息）
            input_format_hint = processor._detect_input_format_hint(media_info)
            if input_format_hint:
                logger.info(f"Using {input_format_hint.upper()} input format hint for problematic file")

            processed_audio_path = processor.extract_audio(
                local_file_path, output_format=output_format, input_format=input_format_hint
            )
            t4 = time.time()
            processing_time = t4 - t3
            logger.info("ffmpeg processing time: %.2f seconds", processing_time)

            # 4. 上传处理后的音频文件
            t5 = time.time()
            new_file_key = generate_processed_file_key(
                tf.file_key, output_format=output_format
            )
            self._upload_processed_file(processed_audio_path, new_file_key)
            t6 = time.time()
            upload_time = t6 - t5
            logger.info("Upload processed file time: %.2f seconds", upload_time)

            # 5. 更新文件记录
            old_file_key = tf.file_key  # 保存旧的文件键，用于后续删除
            tf.file_key = new_file_key
            tf.file_size = os.path.getsize(processed_audio_path)
            # 更新文件类型为处理后的音频格式
            tf.file_type = output_format  # 使用前面定义的 output_format 变量
            tf.media_processing_time = int(
                download_time + processing_time + upload_time
            )
            logger.info(
                "Total ffmpeg processing time: %.2f seconds (download: %.1f, processing: %.1f, upload: %.1f)",
                processing_time + upload_time,
                download_time,
                processing_time,
                upload_time,
            )
            tf.status = (
                TranscriptionFileStatus.uploaded.id
            )  # Preprocessing completed, ready for transcription
            db.session.commit()

            # 6. 删除旧的原始文件以节省存储空间
            try:
                logger.info(f"Deleting old file from storage: {old_file_key}")
                success = self.app.storage.permanently_delete_file(old_file_key)
                if success:
                    logger.info(f"Successfully deleted old file: {old_file_key}")
                else:
                    logger.warning(f"Failed to delete old file: {old_file_key}")
            except Exception as e:
                logger.error(f"Error deleting old file {old_file_key}: {e}")
                # 删除失败不影响主流程，继续执行

            # 7. Create transcription task (skip preprocessing check)
            from controllers.task import create_transcription_task_after_preprocessing

            create_transcription_task_after_preprocessing(user_id, file_id)

            # 8. Mark preprocessing task as completed
            self._update_task_status(task_id, TaskStatus.completed.id)

            logger.info(f"ffmpeg task {task_id} completed successfully")

        except Exception as e:
            # 标记文件状态为预处理失败
            tf.status = TranscriptionFileStatus.preprocessing_failed.id
            db.session.commit()
            raise
        finally:
            # Clean up temporary files (包括下载的文件和处理后的文件)
            processor.cleanup()

    def _process_url_download_task(self, task_id, fields):
        """Process URL download task - download file from URL and optionally preprocess with ffmpeg"""
        logger.info(f"Processing URL download task {task_id}")

        # Parse task data
        task_data = self._parse_task_data(fields)
        file_id = int(task_data.get("file_id", 0))
        user_id = int(task_data.get("user_id", 0))
        preprocessing_reason = task_data.get("preprocessing_reason", "remote_url")

        # Validate parameters
        if file_id == 0:
            raise ValueError(f"Invalid file_id: {file_id}, task_id: {task_id}")
        if user_id == 0:
            raise ValueError(f"Invalid user_id: {user_id}, task_id: {task_id}")

        logger.info(
            f"URL download task parameters: file_id={file_id}, user_id={user_id}, reason={preprocessing_reason}"
        )

        # Get transcription file
        tf = TranscriptionFile.get_by_id(file_id)
        if not tf:
            raise ValueError(f"TranscriptionFile not found: {file_id}")

        # Get source URL from task data or transcription file
        source_url = task_data.get("source_url") or tf.source_url
        if not source_url:
            raise ValueError(f"No source URL found for file {file_id}")

        # Update file status to preprocessing
        tf.status = TranscriptionFileStatus.preprocessing.id
        db.session.commit()

        # Create processor for temporary file management
        processor = FFmpegProcessor(self.app.storage)

        try:
            # Record start time for total processing time
            processing_start_time = time.time()

            # 1. Download file from URL
            t1 = time.time()
            local_file_path = self._download_file_from_url(source_url, tf.filename, processor.temp_dir)
            t2 = time.time()
            download_time = t2 - t1
            logger.info("Download from URL time: %.2f seconds", download_time)

            # 2. Get media info and extract format and duration information
            media_info = processor.get_media_info(local_file_path)
            duration = processor.extract_duration_from_media_info(media_info)

            # 3. Detect actual file type from media info
            actual_file_type = self._detect_file_type_from_media_info(media_info)
            if actual_file_type != tf.file_type:
                logger.info(f"Updating file type from {tf.file_type} to {actual_file_type}")
                tf.file_type = actual_file_type

            # Update file duration if not set
            if not tf.duration or tf.duration == 0:
                tf.duration = duration
                logger.info(f"Updated file duration to {duration} seconds")

            # 4. Always process external files with ffmpeg for security and consistency
            logger.info("Processing external file with ffmpeg (required for all URL downloads)")
            t3 = time.time()
            output_format = DEFAULT_AUDIO_FORMAT

            # 检查是否需要输入格式提示（基于媒体信息）
            input_format_hint = processor._detect_input_format_hint(media_info)
            if input_format_hint:
                logger.info(f"Using {input_format_hint.upper()} input format hint for external file")

            processed_file_path = processor.extract_audio(
                local_file_path, output_format=output_format, input_format=input_format_hint
            )
            # Update file type to processed format
            tf.file_type = output_format
            t4 = time.time()
            ffmpeg_time = t4 - t3
            logger.info("ffmpeg processing time: %.2f seconds", ffmpeg_time)

            # 5. Calculate MD5 fingerprint of processed file
            import hashlib
            with open(processed_file_path, 'rb') as f:
                new_fingerprint = hashlib.md5(f.read()).hexdigest()

            # 6. Update file_key and file_url with fingerprint
            # Format: user_id-fingerprint.extension
            new_file_key = f"{tf.user_id}-{new_fingerprint}.{tf.file_type}"
            old_file_key = tf.file_key
            tf.file_key = new_file_key
            tf.fingerprint = new_fingerprint

            # Update file_url to match the new file_key
            tf.file_url = self.app.storage.generate_file_url(new_file_key)

            logger.info(f"Updated file_key from {old_file_key} to {new_file_key}")
            logger.info(f"Updated file_url to {tf.file_url}")

            # 7. Upload processed file to storage with new key
            t5 = time.time()
            self.app.storage.upload_file(tf.file_key, processed_file_path)
            t6 = time.time()
            upload_time = t6 - t5
            logger.info("Upload to storage time: %.2f seconds", upload_time)

            # 8. Update file metadata
            file_size = os.path.getsize(processed_file_path)
            tf.file_size = file_size
            tf.uploaded_time = datetime.now(timezone.utc).replace(tzinfo=None)  # Update uploaded time

            # Record processing times
            processing_end_time = time.time()
            total_processing_time = int(processing_end_time - processing_start_time)
            tf.media_processing_time = total_processing_time
            # TODO: 后续废弃 media_download_time，统一用 media_processing_time
            tf.media_download_time = total_processing_time

            logger.info(f"Total processing time: {total_processing_time} seconds")

            # 9. Create or update FileStorage record with real fingerprint
            from models.file_storage import FileStorage
            FileStorage.create_or_update_storage(
                tf.user_id,
                tf.fingerprint,
                tf.file_type,
                tf.file_key,
                tf.file_size,
            )
            logger.info(f"Created/updated FileStorage record for fingerprint: {tf.fingerprint}")

            # 10. Clean up temporary FileStorage record if this was from OpenAPI upload
            self._cleanup_temporary_file_storage(tf.user_id, old_file_key)

            # 11. Update file status to uploaded (ready for transcription)
            tf.status = TranscriptionFileStatus.uploaded.id
            db.session.commit()

            # 12. Create transcription task (skip preprocessing check)
            from controllers.task import create_transcription_task_after_preprocessing
            create_transcription_task_after_preprocessing(user_id, file_id)

            # 9. Mark preprocessing task as completed
            self._update_task_status(task_id, TaskStatus.completed.id)

            logger.info(f"URL download task {task_id} completed successfully")

        except Exception as e:
            # Mark file status as preprocessing failed
            tf.status = TranscriptionFileStatus.preprocessing_failed.id
            db.session.commit()
            raise
        finally:
            # Clean up temporary files
            processor.cleanup()

    def _download_file_from_url(self, url: str, filename: str, temp_dir: str) -> str:
        """Download file from URL to temporary directory"""
        import requests

        # Create safe filename
        safe_filename = filename or "downloaded_file"
        local_file_path = os.path.join(temp_dir, safe_filename)

        logger.info(f"Downloading file from URL: {url}")

        try:
            response = requests.get(url, stream=True, timeout=DOWNLOAD_TIMEOUT)
            response.raise_for_status()

            with open(local_file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            file_size = os.path.getsize(local_file_path)
            logger.info(f"Downloaded file size: {file_size} bytes")

            return local_file_path

        except Exception as e:
            logger.error(f"Failed to download file from URL {url}: {e}")
            raise

    def _detect_file_type_from_media_info(self, media_info: dict) -> str:
        """Detect file type from media info (reusing ffprobe result)"""
        try:
            format_info = media_info.get('format', {})

            # Get format name (e.g., "mp3", "wav", "mp4")
            format_name = format_info.get('format_name', '')

            # ffprobe returns comma-separated format names, take the first one
            if ',' in format_name:
                format_name = format_name.split(',')[0]

            # Map some common ffprobe format names to our supported formats
            format_mapping = {
                'mp3': 'mp3',
                'wav': 'wav',
                'flac': 'flac',
                'aac': 'aac',
                'ogg': 'ogg',
                'opus': 'opus',
                'mov': 'mov',
                'mp4': 'mp4',
                'm4a': 'm4a',
                'matroska': 'webm',
                'webm': 'webm',
                'mpeg': 'mpeg',
            }

            # Check if detected format is in our mapping
            detected_format = format_mapping.get(format_name.lower())
            if detected_format:
                from constants.transcription import SUPPORTED_MEDIA_FORMATS
                if detected_format in SUPPORTED_MEDIA_FORMATS:
                    logger.info(f"Detected format from media info: {detected_format}")
                    return detected_format

            logger.warning(f"Unsupported format detected from media info: {format_name}")

        except Exception as e:
            logger.error(f"Error extracting format from media info: {e}")

        # Default to mp3 for unknown formats (will be converted by ffmpeg)
        logger.info("Using default format 'mp3' for unknown format")
        return "mp3"

    def _download_file_from_storage(self, file_key, filename, target_dir):
        """从存储服务下载文件到指定目录"""
        local_file_path = os.path.join(target_dir, filename)

        try:
            # 生成下载 URL
            download_url = self.app.storage.generate_presigned_url_for_read(
                file_key, 3600
            )

            # 下载文件
            logger.info(f"Downloading file from storage: {file_key}")
            response = requests.get(download_url, stream=True, timeout=DOWNLOAD_TIMEOUT)
            response.raise_for_status()

            with open(local_file_path, "wb") as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            return local_file_path

        except Exception as e:
            # 如果下载失败，删除可能创建的文件
            if os.path.exists(local_file_path):
                os.remove(local_file_path)
            raise Exception(f"Failed to download file {file_key}: {e}")

    def _upload_processed_file(self, local_file_path, file_key):
        """上传处理后的文件到存储服务"""
        try:
            logger.info(f"Uploading processed file to storage: {file_key}")

            # 检查存储服务是否有 upload_file 方法
            if hasattr(self.app.storage, "upload_file"):
                # 使用存储服务的 upload_file 方法（参数顺序：key, file_path）
                self.app.storage.upload_file(file_key, local_file_path)
            else:
                # 回退到使用预签名 URL 上传
                self._upload_via_presigned_url(local_file_path, file_key)

            logger.info(f"Processed file uploaded successfully: {file_key}")

        except Exception as e:
            raise Exception(f"Failed to upload processed file {file_key}: {e}")

    def _upload_via_presigned_url(self, local_file_path, file_key):
        """通过预签名 URL 上传文件（回退方案）"""
        import mimetypes

        # 猜测文件类型
        content_type, _ = mimetypes.guess_type(local_file_path)
        if not content_type:
            content_type = "application/octet-stream"

        # 生成上传 URL
        upload_url = self.app.storage.generate_presigned_url_for_upload(
            file_key, 3600, content_type=content_type
        )

        # 上传文件
        with open(local_file_path, "rb") as f:
            response = requests.put(
                upload_url, data=f, headers={"Content-Type": content_type}, timeout=300
            )
            response.raise_for_status()

    def _cleanup_temporary_file_storage(self, user_id, old_file_key):
        """Clean up temporary FileStorage records from OpenAPI upload workflow"""
        try:
            from models.file_storage import FileStorage
            from constants.file_storage import FileState

            # Find temporary FileStorage records that match the old file_key
            # This handles the case where OpenAPI upload created a TEMPORARY record
            temp_records = FileStorage.query.filter_by(
                user_id=user_id,
                file_key=old_file_key,
                state=FileState.TEMPORARY.value
            ).all()

            if temp_records:
                logger.info(f"Found {len(temp_records)} temporary FileStorage records to clean up for user {user_id}")
                for temp_record in temp_records:
                    logger.info(f"Cleaning up temporary FileStorage record: id={temp_record.id}, fingerprint={temp_record.fingerprint}")

                    # Use specialized method for TEMPORARY record cleanup
                    # This will set the state to PENDING_DELETION and the cleanup task will handle actual file deletion
                    FileStorage.cleanup_temporary_record(user_id, temp_record.fingerprint)

                logger.info(f"Successfully cleaned up {len(temp_records)} temporary FileStorage records")
            else:
                logger.debug(f"No temporary FileStorage records found for cleanup (user_id={user_id}, file_key={old_file_key})")

        except Exception as e:
            logger.error(f"Error cleaning up temporary FileStorage records: {e}")
            # Don't raise the exception as this is cleanup - the main workflow should continue


def main():
    """主函数"""
    import argparse

    # 解析命令行参数
    parser = argparse.ArgumentParser(description="Media Preprocessing Consumer")
    parser.add_argument(
        "--workers",
        type=int,
        default=3,
        help="Number of concurrent workers (default: 3)",
    )
    parser.add_argument(
        "--consumer-name",
        type=str,
        default="media-processor-1",
        help="Consumer name for Redis consumer group (default: media-processor-1)",
    )
    args = parser.parse_args()

    # 在 app context 中运行，就像其他 worker 一样
    with shared_app.app_context():
        try:
            logger.info(
                f"Starting media preprocessing consumer with {args.workers} workers..."
            )
            consumer = MediaPreprocessingConsumer(max_workers=args.workers)
            # 设置自定义消费者名称（支持多实例）
            consumer.consumer_name = args.consumer_name
            consumer.consume_tasks()
        except RuntimeError as e:
            logger.error(f"Failed to start media preprocessing consumer: {e}")
            logger.error(
                "Please check the error message above and fix the issue before retrying."
            )
            exit(1)
        except KeyboardInterrupt:
            logger.info("Media preprocessing consumer stopped by user.")
            exit(0)
        except Exception as e:
            logger.error(f"Unexpected error in media preprocessing consumer: {e}")
            logger.exception("Full traceback:")
            exit(1)


if __name__ == "__main__":
    main()
