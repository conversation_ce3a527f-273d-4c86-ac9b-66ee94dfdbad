#!/usr/bin/env python3
"""
Test cases for URL validation functionality
"""

import unittest
from unittest.mock import patch, Mock
import requests

from libs.url_validator import validate_url_for_media_download, get_suggested_filename_from_url, URLValidationResult, normalize_file_sharing_url


class TestURLValidator(unittest.TestCase):
    """Test URL validation functionality"""

    def test_invalid_url_format(self):
        """Test invalid URL formats"""
        # Test missing protocol
        result = validate_url_for_media_download("example.com/file.mp3")
        self.assertFalse(result.is_valid)
        self.assertIn("Invalid URL format", result.error_message)

        # Test invalid protocol
        result = validate_url_for_media_download("ftp://example.com/file.mp3")
        self.assertFalse(result.is_valid)
        self.assertIn("HTTP or HTTPS protocol", result.error_message)

        # Test empty URL
        result = validate_url_for_media_download("")
        self.assertFalse(result.is_valid)

    @patch('requests.head')
    def test_successful_validation_with_head(self, mock_head):
        """Test successful validation using HEAD request"""
        # Mock successful HEAD response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.headers = {
            'Content-Type': 'audio/mpeg',
            'Content-Length': '5000000'  # 5MB
        }
        mock_head.return_value = mock_response

        result = validate_url_for_media_download("https://example.com/audio.mp3")
        
        self.assertTrue(result.is_valid)
        self.assertEqual(result.content_type, 'audio/mpeg')
        self.assertEqual(result.content_length, 5000000)
        self.assertEqual(result.detected_extension, '.mp3')

    @patch('requests.head')
    @patch('requests.get')
    def test_fallback_to_get_when_head_not_allowed(self, mock_get, mock_head):
        """Test fallback to GET request when HEAD is not allowed"""
        # Mock HEAD returning 405 Method Not Allowed
        mock_head_response = Mock()
        mock_head_response.status_code = 405
        mock_head_response.raise_for_status.side_effect = requests.exceptions.HTTPError(response=mock_head_response)
        mock_head.return_value = mock_head_response

        # Mock successful GET response
        mock_get_response = Mock()
        mock_get_response.status_code = 200
        mock_get_response.headers = {
            'Content-Type': 'audio/wav',
            'Content-Length': '10000000'  # 10MB
        }
        mock_get_response.close = Mock()
        mock_get.return_value = mock_get_response

        result = validate_url_for_media_download("https://example.com/audio.wav")
        
        self.assertTrue(result.is_valid)
        self.assertEqual(result.content_type, 'audio/wav')
        self.assertEqual(result.content_length, 10000000)
        mock_get_response.close.assert_called_once()

    @patch('requests.head')
    def test_authentication_required_error(self, mock_head):
        """Test handling of authentication required error"""
        mock_response = Mock()
        mock_response.status_code = 401
        mock_response.reason = "Unauthorized"
        mock_head.return_value = mock_response
        mock_head.return_value.raise_for_status.side_effect = requests.exceptions.HTTPError(response=mock_response)

        result = validate_url_for_media_download("https://example.com/private.mp3")
        
        self.assertFalse(result.is_valid)
        self.assertIn("requires authentication", result.error_message)

    @patch('requests.head')
    def test_file_not_found_error(self, mock_head):
        """Test handling of 404 error"""
        mock_response = Mock()
        mock_response.status_code = 404
        mock_response.reason = "Not Found"
        mock_head.return_value = mock_response
        mock_head.return_value.raise_for_status.side_effect = requests.exceptions.HTTPError(response=mock_response)

        result = validate_url_for_media_download("https://example.com/nonexistent.mp3")
        
        self.assertFalse(result.is_valid)
        self.assertIn("not found (404)", result.error_message)

    @patch('requests.head')
    def test_connection_timeout(self, mock_head):
        """Test handling of connection timeout"""
        mock_head.side_effect = requests.exceptions.Timeout()

        result = validate_url_for_media_download("https://slow-server.com/audio.mp3")
        
        self.assertFalse(result.is_valid)
        self.assertIn("timed out", result.error_message)

    @patch('requests.head')
    def test_connection_error(self, mock_head):
        """Test handling of connection error"""
        mock_head.side_effect = requests.exceptions.ConnectionError()

        result = validate_url_for_media_download("https://unreachable.com/audio.mp3")
        
        self.assertFalse(result.is_valid)
        self.assertIn("Cannot connect", result.error_message)

    @patch('requests.head')
    def test_file_too_large(self, mock_head):
        """Test handling of files that are too large"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.headers = {
            'Content-Type': 'audio/mpeg',
            'Content-Length': str(6 * 1024 * 1024 * 1024)  # 6GB (over limit)
        }
        mock_head.return_value = mock_response

        result = validate_url_for_media_download("https://example.com/huge.mp3")
        
        self.assertFalse(result.is_valid)
        self.assertIn("too large", result.error_message)

    @patch('requests.head')
    def test_file_too_small(self, mock_head):
        """Test handling of files that are too small"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.headers = {
            'Content-Type': 'audio/mpeg',
            'Content-Length': '500'  # 500 bytes (too small)
        }
        mock_head.return_value = mock_response

        result = validate_url_for_media_download("https://example.com/tiny.mp3")
        
        self.assertFalse(result.is_valid)
        self.assertIn("too small", result.error_message)

    @patch('requests.head')
    def test_html_content_rejection(self, mock_head):
        """Test rejection of HTML content"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.headers = {
            'Content-Type': 'text/html',
            'Content-Length': '50000'
        }
        mock_head.return_value = mock_response

        result = validate_url_for_media_download("https://example.com/page.html")
        
        self.assertFalse(result.is_valid)
        self.assertIn("web page or document", result.error_message)

    @patch('requests.head')
    def test_generic_mime_type_with_valid_extension(self, mock_head):
        """Test acceptance of generic MIME type with valid extension"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.headers = {
            'Content-Type': 'application/octet-stream',
            'Content-Length': '5000000'
        }
        mock_head.return_value = mock_response

        result = validate_url_for_media_download("https://example.com/audio.flac")
        
        self.assertTrue(result.is_valid)
        self.assertEqual(result.content_type, 'application/octet-stream')
        self.assertEqual(result.detected_extension, '.flac')

    @patch('requests.head')
    def test_no_valid_mime_or_extension(self, mock_head):
        """Test rejection when neither MIME type nor extension is valid"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.headers = {
            'Content-Type': 'application/pdf',
            'Content-Length': '5000000'
        }
        mock_head.return_value = mock_response

        result = validate_url_for_media_download("https://example.com/document.pdf")
        
        self.assertFalse(result.is_valid)
        self.assertIn("not appear to point to a supported media file", result.error_message)

    @patch('requests.head')
    def test_file_sharing_service_with_json_response(self, mock_head):
        """Test acceptance of file sharing services that return JSON but have valid extensions"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.headers = {
            'Content-Type': 'application/json',
            'Content-Length': '5000000'
        }
        mock_head.return_value = mock_response

        # Test Dropbox URL with valid extension
        result = validate_url_for_media_download("https://www.dropbox.com/s/abc123/audio.mp3?dl=1")

        self.assertTrue(result.is_valid)
        self.assertEqual(result.content_type, 'application/json')
        self.assertEqual(result.detected_extension, '.mp3')

    @patch('requests.head')
    def test_non_file_sharing_service_json_rejection(self, mock_head):
        """Test rejection of non-file-sharing services that return JSON"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.headers = {
            'Content-Type': 'application/json',
            'Content-Length': '5000000'
        }
        mock_head.return_value = mock_response

        # Test regular website with JSON response - should be rejected
        result = validate_url_for_media_download("https://api.example.com/data.mp3")

        self.assertFalse(result.is_valid)
        self.assertIn("web page or document", result.error_message)

    def test_get_suggested_filename_from_url(self):
        """Test filename generation from URL"""
        # Test with clear filename in path
        filename = get_suggested_filename_from_url("https://example.com/path/audio.mp3", ".mp3")
        self.assertEqual(filename, "audio.mp3")

        # Test with no filename in path
        filename = get_suggested_filename_from_url("https://example.com/", ".wav")
        self.assertEqual(filename, "download_from_example.com.wav")

        # Test with domain and extension
        filename = get_suggested_filename_from_url("https://cdn.example.com/stream", ".m4a")
        self.assertEqual(filename, "download_from_cdn.example.com.m4a")

        # Test with no extension
        filename = get_suggested_filename_from_url("https://example.com/audio", None)
        self.assertEqual(filename, "download_from_example.com.unknown")

    def test_normalize_file_sharing_url(self):
        """Test URL normalization for file sharing services"""
        # Test Dropbox dl=0 to dl=1 conversion
        original_url = "https://www.dropbox.com/s/abc123/audio.mp3?dl=0"
        normalized = normalize_file_sharing_url(original_url)
        self.assertEqual(normalized, "https://www.dropbox.com/s/abc123/audio.mp3?dl=1")

        # Test Dropbox URL without dl parameter
        original_url = "https://www.dropbox.com/s/abc123/audio.mp3"
        normalized = normalize_file_sharing_url(original_url)
        self.assertEqual(normalized, "https://www.dropbox.com/s/abc123/audio.mp3?dl=1")

        # Test Dropbox URL with dl=1 (should remain unchanged)
        original_url = "https://www.dropbox.com/s/abc123/audio.mp3?dl=1"
        normalized = normalize_file_sharing_url(original_url)
        self.assertEqual(normalized, original_url)

        # Test Google Drive sharing URL conversion
        original_url = "https://drive.google.com/file/d/1abc123def456/view?usp=sharing"
        normalized = normalize_file_sharing_url(original_url)
        self.assertEqual(normalized, "https://drive.google.com/uc?id=1abc123def456&export=download")

        # OneDrive URLs are no longer supported - they should remain unchanged
        original_url = "https://1drv.ms/u/s!abc123/file.mp3?e=xyz"
        normalized = normalize_file_sharing_url(original_url)
        self.assertEqual(normalized, original_url)  # Should remain unchanged

        original_url = "https://onedrive.live.com/redir?resid=ABC123&authkey=xyz"
        normalized = normalize_file_sharing_url(original_url)
        self.assertEqual(normalized, original_url)  # Should remain unchanged

        # Test non-file-sharing URL (should remain unchanged)
        original_url = "https://example.com/audio.mp3"
        normalized = normalize_file_sharing_url(original_url)
        self.assertEqual(normalized, original_url)

    @patch('requests.head')
    def test_validation_with_url_normalization(self, mock_head):
        """Test that validation uses normalized URLs"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.headers = {
            'Content-Type': 'application/json',
            'Content-Length': '5000000'
        }
        mock_head.return_value = mock_response

        # Test with Dropbox dl=0 URL
        result = validate_url_for_media_download("https://www.dropbox.com/s/abc123/audio.mp3?dl=0")

        self.assertTrue(result.is_valid)
        self.assertEqual(result.detected_extension, '.mp3')
        self.assertEqual(result.normalized_url, "https://www.dropbox.com/s/abc123/audio.mp3?dl=1")

        # Verify that the HEAD request was made to the normalized URL
        mock_head.assert_called_once()
        called_url = mock_head.call_args[0][0]
        self.assertEqual(called_url, "https://www.dropbox.com/s/abc123/audio.mp3?dl=1")

    def test_onedrive_url_rejection(self):
        """Test that OneDrive URLs are properly rejected"""
        # Test OneDrive short URL
        result = validate_url_for_media_download("https://1drv.ms/u/s!abc123/file.mp3?e=xyz")
        self.assertFalse(result.is_valid)
        self.assertIn("OneDrive URLs are not currently supported", result.error_message)

        # Test OneDrive long URL
        result = validate_url_for_media_download("https://onedrive.live.com/redir?resid=ABC123&authkey=xyz")
        self.assertFalse(result.is_valid)
        self.assertIn("OneDrive URLs are not currently supported", result.error_message)


if __name__ == '__main__':
    # Set up logging for tests
    import logging
    logging.basicConfig(level=logging.INFO)
    
    unittest.main()
