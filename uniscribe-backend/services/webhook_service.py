"""
Webhook service for OpenAPI notifications
"""

import logging
import requests
from datetime import datetime
from typing import Dict, Any, Optional

from models.api_key import WebhookLog

logger = logging.getLogger(__name__)


class WebhookService:
    """Service for sending webhook notifications"""
    
    TIMEOUT_SECONDS = 30
    MAX_RESPONSE_LENGTH = 1000
    
    @staticmethod
    def send_webhook(webhook_url: str, event_type: str, transcription_data: Dict[str, Any]) -> bool:
        """
        Send webhook notification
        
        Args:
            webhook_url: Target webhook URL
            event_type: Event type (transcription.completed, transcription.failed)
            transcription_data: Transcription data to send
            
        Returns:
            bool: True if webhook was sent successfully
        """
        payload = {
            "event": event_type,
            "data": transcription_data,
            "timestamp": datetime.utcnow().isoformat() + "Z"
        }
        
        transcription_id = transcription_data.get("id", "unknown")
        
        try:
            logger.info(f"Sending webhook {event_type} for transcription {transcription_id} to {webhook_url}")
            
            response = requests.post(
                webhook_url,
                json=payload,
                timeout=WebhookService.TIMEOUT_SECONDS,
                headers={
                    'Content-Type': 'application/json',
                    'User-Agent': 'UniScribe-Webhook/1.0'
                }
            )
            
            # Log the webhook attempt
            WebhookLog.create(
                transcription_id=transcription_id,
                webhook_url=webhook_url,
                event_type=event_type,
                status_code=response.status_code,
                response_body=response.text[:WebhookService.MAX_RESPONSE_LENGTH]
            )
            
            success = response.status_code < 400
            if success:
                logger.info(f"Webhook sent successfully: {response.status_code}")
            else:
                logger.warning(f"Webhook failed with status {response.status_code}: {response.text[:200]}")
            
            return success
            
        except requests.exceptions.Timeout:
            error_msg = f"Webhook timeout after {WebhookService.TIMEOUT_SECONDS}s"
            logger.error(f"Webhook timeout for transcription {transcription_id}: {webhook_url}")
            
            WebhookLog.create(
                transcription_id=transcription_id,
                webhook_url=webhook_url,
                event_type=event_type,
                error_message=error_msg
            )
            return False
            
        except requests.exceptions.RequestException as e:
            error_msg = f"Webhook request failed: {str(e)}"
            logger.error(f"Webhook error for transcription {transcription_id}: {error_msg}")
            
            WebhookLog.create(
                transcription_id=transcription_id,
                webhook_url=webhook_url,
                event_type=event_type,
                error_message=error_msg
            )
            return False
            
        except Exception as e:
            error_msg = f"Unexpected webhook error: {str(e)}"
            logger.error(f"Unexpected webhook error for transcription {transcription_id}: {error_msg}")
            
            WebhookLog.create(
                transcription_id=transcription_id,
                webhook_url=webhook_url,
                event_type=event_type,
                error_message=error_msg
            )
            return False

    @staticmethod
    def send_completion_webhook(transcription_file, success: bool = True):
        """
        Send webhook notification for transcription completion/failure

        Args:
            transcription_file: TranscriptionFile instance
            success: Whether transcription was successful
        """
        if not transcription_file.webhook_url:
            return

        event_type = "transcription.completed" if success else "transcription.failed"

        webhook_data = {
            "id": str(transcription_file.id),
            "filename": transcription_file.filename,
            "status": transcription_file.status,
            "duration": transcription_file.duration,
            "language_code": transcription_file.language_code,
            "transcription_type": transcription_file.transcription_type,
            "created_at": transcription_file.created_time.isoformat() + "Z",
            "completed_at": transcription_file.updated_time.isoformat() + "Z" if transcription_file.updated_time else None,
            "file_size": transcription_file.file_size,
            "source_type": transcription_file.source_type,
        }

        # Add error message for failed transcriptions
        if not success:
            from controllers.task import get_transcription_file_error_message
            error_message = get_transcription_file_error_message(transcription_file.id)
            if error_message:
                webhook_data["error_message"] = error_message

        WebhookService.send_webhook(
            transcription_file.webhook_url,
            event_type,
            webhook_data
        )
