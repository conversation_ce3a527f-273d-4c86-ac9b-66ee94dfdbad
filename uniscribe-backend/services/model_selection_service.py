"""Model selection service for transcription tasks."""

import logging

logger = logging.getLogger(__name__)


class ModelSelectionService:
    """Service for selecting appropriate models based on various criteria."""

    # 模型常量
    FAL_WHISPER = "fal-ai/whisper"
    FAL_WIZPER = "fal-ai/wizper"
    FAL_WHISPER_DIARIZATION = "fal-ai/whisper-diarization"  # FAL Whisper 说话人识别版本
    REPLICATE_WHISPERX = "replicate/whisperx"
    REPLICATE_WHISPERX_DIARIZATION = (
        "replicate/whisperx-diarization"  # Replicate WhisperX 说话人识别版本
    )
    REPLICATE_WHISPER_DIARIZATION = "replicate/whisper-diarization"  # Replicate Whisper Diarization (内置说话人识别)
    DEEPINFRA_WHISPER_TURBO = "deepinfra/whisper-turbo"
    DEEPINFRA_WHISPER = "deepinfra/whisper"
    # Word 级别的 DeepInfra 模型，用于说话人识别等需要词级时间戳的场景
    DEEPINFRA_WHISPER_TURBO_WORD = "deepinfra/whisper-turbo-word"
    DEEPINFRA_WHISPER_WORD = "deepinfra/whisper-word"

    @classmethod
    def select_transcription_model(cls, user_id, transcription_file):
        """
        根据用户和转录类型选择合适的转录模型

        Args:
            user_id: 用户ID (保留参数以维持接口兼容性)
            transcription_file: 转录文件对象

        Returns:
            str: 选择的模型标识符
        """
        # 文件大于 2GB，强制使用  REPLICATE_WHISPERX， 因为其他模型可能会出错
        if transcription_file.file_size > 2 * 1024 * 1024 * 1024:
            return cls.REPLICATE_WHISPERX

        if transcription_file.enable_speaker_diarization:
            return cls.select_transcription_model_for_speaker_diarization(
                user_id, transcription_file
            )

        transcription_type = transcription_file.transcription_type
        if transcription_type == "subtitle":
            return cls.FAL_WHISPER

        if transcription_file.language_code in ["zh", "zh_tw", "yue"]:
            # DEEPINFRA_WHISPER 对中文支持较好，且价格不贵
            return cls.DEEPINFRA_WHISPER

        # 不管是匿名、免费、付费（含订阅、一次性、LTD），都用 fal/wizper
        # 不再使用 replicate/whisperx， 因为会丢失内容，但是还保留在 go 服务作为兜底策略。
        return cls.FAL_WIZPER

    @classmethod
    def select_transcription_model_for_speaker_diarization(
        cls, user_id, transcription_file
    ):
        """
        为说话人识别选择合适的转录模型

        Args:
            user_id: 用户ID (保留参数以维持接口兼容性)
            transcription_file: 转录文件对象

        Returns:
            str: 选择的模型标识符（支持说话人识别）
        """
        # 为了提升说话人识别的准确率，使用最好的模型（付费用户专享）
        return cls.DEEPINFRA_WHISPER_WORD

