#!/usr/bin/env python3
"""
脚本用于找到转录是英文但文本处理任务（summary/outline/qa_extraction）疑似为西班牙语或中文的任务，并重跑这些任务。

使用方法:
    python cli/retry_spanish_summary_tasks.py --start-date 2025-01-10 --end-date 2025-01-12
    python cli/retry_spanish_summary_tasks.py --start-date 2025-01-10 --end-date 2025-01-12 --task-types summary,outline
    python cli/retry_spanish_summary_tasks.py --start-date 2025-01-10 --end-date 2025-01-12 --dry-run
    python cli/retry_spanish_summary_tasks.py --start-date 2025-01-10 --end-date 2025-01-12 --force
"""

import click
import re
import time
from datetime import datetime, timedelta
from flask.cli import with_appcontext
import logging
from sqlalchemy import and_

from models import db
from models.task import Task
from models.task_result import TaskResult
from models.transcription_file import TranscriptionFile
from constants.task import TaskStatus, TaskType
from controllers.task import retry_task
from . import admin_cli

logger = logging.getLogger(__name__)

# 西班牙语常见词汇和短语模式
SPANISH_PATTERNS = [
    # 常见西班牙语词汇
    r'\b(el|la|los|las|un|una|de|del|en|con|por|para|que|es|son|está|están)\b',
    # 西班牙语动词变位
    r'\b\w+(ción|sión|mente|ando|endo|ado|ido)\b',
    # 西班牙语特殊字符
    r'[ñáéíóúü]',
    # 常见西班牙语短语
    r'\b(muy|más|también|pero|sin embargo|por ejemplo|además|finalmente)\b',
    # 西班牙语问候和常用表达
    r'\b(hola|gracias|por favor|buenos días|buenas tardes|buenas noches)\b',
]

# 中文常见词汇和短语模式
CHINESE_PATTERNS = [
    # 中文字符范围（包括简体和繁体）
    r'[\u4e00-\u9fff]',
    # 常见中文词汇和短语
    r'(这个|那个|我们|他们|可以|应该|需要|主要|重要|内容|问题|方面|情况|时候|地方|工作|公司|产品|服务|用户|客户)',
    # 中文标点符号
    r'[，。！？；：""''（）【】《》]',
    # 常见中文连接词
    r'(而且|但是|因为|所以|然后|首先|其次|最后|总之|另外|此外|同时|不过|虽然|如果|由于)',
]

def is_likely_spanish(text):
    """
    检查文本是否疑似为西班牙语

    Args:
        text: 要检查的文本

    Returns:
        bool: 如果文本疑似为西班牙语返回 True
    """
    # 处理不同类型的输入
    if isinstance(text, list):
        # 如果是列表，转换为字符串
        text = ' '.join(str(item) for item in text if item)
    elif not isinstance(text, str):
        # 如果不是字符串也不是列表，转换为字符串
        text = str(text) if text else ""

    if not text or len(text.strip()) < 10:
        return False

    text_lower = text.lower()
    matches = 0

    for pattern in SPANISH_PATTERNS:
        if re.search(pattern, text_lower, re.IGNORECASE):
            matches += 1

    # 如果匹配到多个西班牙语模式，认为是西班牙语
    return matches >= 2

def is_likely_chinese(text):
    """
    检查文本是否疑似为中文

    Args:
        text: 要检查的文本

    Returns:
        bool: 如果文本疑似为中文返回 True
    """
    # 处理不同类型的输入
    if isinstance(text, list):
        # 如果是列表，转换为字符串
        text = ' '.join(str(item) for item in text if item)
    elif not isinstance(text, str):
        # 如果不是字符串也不是列表，转换为字符串
        text = str(text) if text else ""

    if not text or len(text.strip()) < 10:
        return False

    matches = 0

    for pattern in CHINESE_PATTERNS:
        if re.search(pattern, text, re.IGNORECASE):
            matches += 1

    # 如果匹配到多个中文模式，认为是中文
    return matches >= 2

def is_non_english_summary(text):
    """
    检查文本是否疑似为非英文（西班牙语或中文）

    Args:
        text: 要检查的文本

    Returns:
        tuple: (is_non_english, detected_language)
               detected_language 可能是 'spanish', 'chinese', 或 None
    """
    if is_likely_spanish(text):
        return True, 'spanish'
    elif is_likely_chinese(text):
        return True, 'chinese'
    else:
        return False, None

def find_english_transcription_non_english_text_tasks(start_date, end_date, task_types):
    """
    找到转录是英文但文本处理任务疑似为西班牙语或中文的任务

    Args:
        start_date: 开始日期 (datetime)
        end_date: 结束日期 (datetime)
        task_types: 任务类型列表 (list of int)

    Returns:
        list: 符合条件的任务列表
    """
    logger.info(f"Searching for tasks between {start_date} and {end_date}")
    logger.info(f"Task types: {task_types}")

    # 查询条件：
    # 1. 任务类型在指定列表中
    # 2. 任务在指定日期范围内创建
    # 3. 任务已完成
    # 4. 通过 JOIN 只获取未删除的转录文件对应的任务
    text_tasks_with_files = db.session.query(Task, TranscriptionFile).join(
        TranscriptionFile, Task.file_id == TranscriptionFile.id
    ).filter(
        and_(
            Task.task_type.in_(task_types),
            Task.created_time >= start_date,
            Task.created_time < end_date,
            Task.status == TaskStatus.completed.id,
            TranscriptionFile.is_deleted == False,  # 只获取未删除的文件
            TranscriptionFile.language_code == 'en'  # 只获取英文转录
        )
    ).all()

    logger.info(f"Found {len(text_tasks_with_files)} completed text tasks with valid files in date range")

    problematic_tasks = []

    for task, transcription_file in text_tasks_with_files:
        try:
            # 获取语言信息（用于显示）
            detected_language = getattr(transcription_file, 'detected_language', None)
            language_code = getattr(transcription_file, 'language_code', None)
            
            # 获取任务结果
            task_result = TaskResult.get_by_file_id(task.file_id)
            if not task_result:
                logger.warning(f"No task result found for task {task.id}")
                continue

            # 根据任务类型获取相应的内容
            content = None
            content_type = None
            if task.task_type == TaskType.summary.id and task_result.summary:
                content = task_result.summary
                content_type = 'summary'
            elif task.task_type == TaskType.outline.id and task_result.outline:
                content = task_result.outline
                content_type = 'outline'
            elif task.task_type == TaskType.qa_extraction.id and task_result.qa_extraction:
                content = task_result.qa_extraction
                content_type = 'qa_extraction'

            if not content:
                logger.warning(f"No {content_type} content found for task {task.id}")
                continue

            # 检查内容是否疑似为非英文（西班牙语或中文）
            is_non_english, detected_language_in_content = is_non_english_summary(content)
            if is_non_english:
                # 获取转录文本预览
                transcript_preview = None
                if task_result.original_text:
                    transcript_preview = task_result.original_text[:200] + '...' if len(task_result.original_text) > 200 else task_result.original_text

                # 处理内容预览（可能是列表或字符串）
                if isinstance(content, list):
                    content_str = ' '.join(str(item) for item in content if item)
                else:
                    content_str = str(content) if content else ""

                content_preview = content_str[:200] + '...' if len(content_str) > 200 else content_str

                problematic_tasks.append({
                    'task': task,
                    'transcription_file': transcription_file,
                    'task_result': task_result,
                    'detected_language': detected_language,
                    'language_code': language_code,
                    'content_type': content_type,
                    'content_language': detected_language_in_content,
                    'content_preview': content_preview,
                    'transcript_preview': transcript_preview
                })
                logger.info(f"Found problematic task {task.id}: English transcription but {detected_language_in_content} {content_type}")
            
        except Exception as e:
            logger.exception(f"Error processing task {task.id}: {str(e)}")
            continue
    
    return problematic_tasks

@admin_cli.command("retry-spanish-summary-tasks")
@click.option("--start-date", required=True, help="开始日期，格式: YYYY-MM-DD")
@click.option("--end-date", required=True, help="结束日期，格式: YYYY-MM-DD")
@click.option("--task-types", default="summary,outline,qa_extraction", help="任务类型，用逗号分隔，默认: summary,outline,qa_extraction")
@click.option("--dry-run", is_flag=True, help="只显示符合条件的任务，不执行重试")
@click.option("--force", is_flag=True, help="强制重试任务，即使状态不是失败")
@click.option("--sleep-interval", type=float, default=1.0, help="任务重试之间的等待时间（秒），默认1.0秒")
@with_appcontext
def retry_spanish_summary_tasks_cli(start_date, end_date, task_types, dry_run, force, sleep_interval):
    """
    找到转录是英文但文本处理任务疑似为西班牙语或中文的任务，并重跑这些任务

    Args:
        start_date: 开始日期，格式 YYYY-MM-DD
        end_date: 结束日期，格式 YYYY-MM-DD
        task_types: 任务类型，用逗号分隔
        dry_run: 只显示符合条件的任务，不执行重试
        force: 强制重试任务，即使状态不是失败
        sleep_interval: 任务重试之间的等待时间（秒）
    """
    try:
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d") + timedelta(days=1)  # 包含结束日期
    except ValueError:
        click.echo("❌ 日期格式错误，请使用 YYYY-MM-DD 格式")
        return

    # 解析任务类型
    task_type_names = [t.strip() for t in task_types.split(',')]
    task_type_ids = []
    task_type_mapping = {
        'summary': TaskType.summary.id,
        'outline': TaskType.outline.id,
        'qa_extraction': TaskType.qa_extraction.id,
    }

    for task_type_name in task_type_names:
        if task_type_name not in task_type_mapping:
            click.echo(f"❌ 不支持的任务类型: {task_type_name}")
            click.echo(f"支持的任务类型: {', '.join(task_type_mapping.keys())}")
            return
        task_type_ids.append(task_type_mapping[task_type_name])

    logger.info(f"Starting to search for problematic text tasks from {start_date} to {end_date}")
    click.echo(f"🔍 搜索 {start_date} 到 {end_date} 的问题文本处理任务...")
    click.echo(f"📋 任务类型: {', '.join(task_type_names)}")

    # 查找符合条件的任务
    problematic_tasks = find_english_transcription_non_english_text_tasks(start_dt, end_dt, task_type_ids)

    if not problematic_tasks:
        click.echo(f"✅ 在指定日期范围内没有找到转录是英文但文本处理任务疑似为西班牙语或中文的任务")
        return
    
    click.echo(f"🚨 找到 {len(problematic_tasks)} 个问题任务:")
    click.echo()
    
    # 显示找到的任务
    for i, item in enumerate(problematic_tasks, 1):
        task = item['task']
        transcription_file = item['transcription_file']

        # 获取任务类型名称
        task_type_name = 'unknown'
        for name, type_id in {'summary': TaskType.summary.id, 'outline': TaskType.outline.id, 'qa_extraction': TaskType.qa_extraction.id}.items():
            if task.task_type == type_id:
                task_type_name = name
                break

        click.echo(f"📋 任务 {i}:")
        click.echo(f"   任务ID: {task.id}")
        click.echo(f"   任务类型: {task_type_name}")
        click.echo(f"   文件ID: {task.file_id}")
        click.echo(f"   文件名: {transcription_file.filename}")
        click.echo(f"   用户ID: {transcription_file.user_id}")
        click.echo(f"   检测语言: {item['detected_language'] or 'N/A'}")
        click.echo(f"   语言代码: {item['language_code'] or 'N/A'}")
        click.echo(f"   内容类型: {item['content_type']}")
        click.echo(f"   内容语言: {item['content_language']}")
        click.echo(f"   任务状态: {task.status}")
        click.echo(f"   创建时间: {task.created_time}")

        # 显示转录预览（如果有）
        if item.get('transcript_preview'):
            click.echo(f"   📝 转录预览: {item['transcript_preview']}")
        else:
            click.echo(f"   📝 转录预览: N/A")

        # 显示内容预览
        click.echo(f"   🎯 {item['content_type']}预览: {item['content_preview']}")
        click.echo()
    
    if dry_run:
        click.echo("🔍 这是预览模式，没有执行任何重试操作")
        return

    # 显示配置信息
    click.echo(f"⚙️  配置信息:")
    click.echo(f"   任务重试间隔: {sleep_interval} 秒")
    click.echo()

    # 确认是否继续
    if not click.confirm(f"确定要重试这 {len(problematic_tasks)} 个任务吗？"):
        click.echo("❌ 操作已取消")
        return
    
    # 执行重试
    success_count = 0
    failed_count = 0
    
    click.echo("🔄 开始重试任务...")
    
    for i, item in enumerate(problematic_tasks, 1):
        task = item['task']
        transcription_file = item['transcription_file']

        try:
            click.echo(f"[{i}/{len(problematic_tasks)}] 重试任务 {task.id}...")

            success = retry_task(transcription_file.user_id, task.id, force)

            if success:
                success_count += 1
                click.echo(f"   ✅ 任务 {task.id} 重试成功")
            else:
                failed_count += 1
                click.echo(f"   ❌ 任务 {task.id} 重试失败")

        except Exception as e:
            failed_count += 1
            logger.exception(f"重试任务 {task.id} 时发生异常: {str(e)}")
            click.echo(f"   ❌ 任务 {task.id} 重试异常: {str(e)}")

        # 如果不是最后一个任务，等待指定的间隔时间
        if i < len(problematic_tasks) and sleep_interval > 0:
            click.echo(f"   ⏳ 等待 {sleep_interval} 秒...")
            time.sleep(sleep_interval)
    
    # 显示结果统计
    click.echo()
    click.echo("📊 重试结果统计:")
    click.echo(f"   总任务数: {len(problematic_tasks)}")
    click.echo(f"   成功重试: {success_count}")
    click.echo(f"   重试失败: {failed_count}")
    
    if success_count > 0:
        click.echo(f"✅ 成功重试了 {success_count} 个任务")
    if failed_count > 0:
        click.echo(f"⚠️  有 {failed_count} 个任务重试失败")

if __name__ == "__main__":
    retry_spanish_summary_tasks_cli()
