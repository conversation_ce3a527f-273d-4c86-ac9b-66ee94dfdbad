"""
任务重试相关的CLI命令
"""

import click
from flask.cli import with_appcontext
import logging

from models.task import Task
from models.transcription_file import TranscriptionFile
from constants.task import TaskStatus, TaskType
from controllers.task import retry_task

from . import admin_cli

logger = logging.getLogger(__name__)


@admin_cli.command("retry-task")
@click.argument("task_id", type=int)
@click.option("--force", is_flag=True, help="强制重试任务，即使状态不是失败")
@with_appcontext
def retry_task_cli(task_id, force):
    """重新执行指定的任务
    
    参数:
        task_id: 任务ID
        --force: 强制重试标志，允许重试非失败状态的任务
    """
    logger.info(f"开始重试任务: {task_id}")
    
    # 获取任务信息
    task = Task.get_by_id(task_id)
    if not task:
        logger.error(f"任务不存在: {task_id}")
        click.echo(f"❌ 任务不存在: {task_id}")
        return
    
    # 检查任务状态
    if not force and task.status not in [TaskStatus.failed.id, TaskStatus.completed.id]:
        logger.warning(f"任务状态不允许重试: {task_id}, 状态: {task.status}")
        click.echo(f"⚠️  任务状态不允许重试: {task_id}")
        click.echo(f"   当前状态: {_get_status_name(task.status)}")
        click.echo(f"   使用 --force 参数可强制重试")
        return
    
    # 显示任务信息
    click.echo(f"📋 任务信息:")
    click.echo(f"   任务ID: {task.id}")
    click.echo(f"   文件ID: {task.file_id}")
    click.echo(f"   任务类型: {_get_task_type_name(task.task_type)}")
    click.echo(f"   当前状态: {_get_status_name(task.status)}")
    click.echo(f"   优先级: {task.priority}")
    if task.error_message:
        click.echo(f"   错误信息: {task.error_message}")
    
    try:
        # 获取文件的用户ID
        transcription_file = TranscriptionFile.get_by_id(task.file_id)
        if not transcription_file:
            logger.error(f"转录文件不存在: {task.file_id}")
            click.echo(f"❌ 转录文件不存在: {task.file_id}")
            return
        
        # 调用 controller 中的重试逻辑
        success = retry_task(transcription_file.user_id, task_id, force)
        
        if success:
            logger.info(f"任务重试成功: {task_id}")
            click.echo(f"✅ 任务已成功加入队列重新执行")
        else:
            logger.error(f"任务重试失败: {task_id}")
            click.echo(f"❌ 任务重试失败")
            
    except ValueError as e:
        # 业务逻辑错误
        logger.warning(f"任务重试业务逻辑错误: {task_id}, 错误: {str(e)}")
        click.echo(f"❌ {str(e)}")
    except Exception as e:
        logger.exception(f"重试任务时发生异常: {task_id}, 错误: {str(e)}")
        click.echo(f"❌ 重试任务时发生异常: {str(e)}")


def _get_status_name(status_id):
    """Get status name"""
    try:
        return TaskStatus.by_id(status_id).name
    except ValueError:
        return f"unknown_status({status_id})"


def _get_task_type_name(task_type_id):
    """Get task type name"""
    try:
        return TaskType.by_id(task_type_id).name
    except ValueError:
        return f"unknown_type({task_type_id})"


# 注意：重试逻辑已移至 controllers/task.py 中
# CLI 现在只是调用 controller 函数的薄薄接口层
