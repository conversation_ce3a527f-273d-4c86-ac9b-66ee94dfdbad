"""
CLI script to create forever free subscriptions for users using coupons.

This script creates a Stripe subscription with a forever free coupon (100% discount)
for a specified user and plan. It validates the coupon, creates/uses Stripe customer,
and creates the subscription with the coupon applied.

Usage:
    flask admin create-forever-free-subscription --user-id=123456 --plan-name=basic_monthly --coupon-id=coupon_abc123
    flask admin create-forever-free-subscription --user-id=123456 --plan-name=pro_yearly --coupon-id=coupon_abc123 --dry-run
"""

import logging
import time
from datetime import datetime

import click
import stripe
from flask.cli import with_appcontext

from config import CONFIG
from models import db, db_transaction
from models.user import User
from models.plan import Plan, PlanType
from models.subscription import Subscription
from constants.subscription import SubscriptionStatus
from services.stripe_service import StripeService
from libs.id_generator import id_generator

# Initialize Stripe
stripe.api_key = CONFIG.STRIPE["secret_key"]

logger = logging.getLogger(__name__)


@click.command("create-forever-free-subscription")
@click.option(
    "--user-id", type=int, required=True, help="User ID to create subscription for"
)
@click.option(
    "--plan-name", type=str, required=True, help="Plan name (e.g., basic_monthly, pro_yearly)"
)
@click.option(
    "--coupon-id", type=str, required=True, help="Stripe coupon ID to apply"
)
@click.option(
    "--dry-run", is_flag=True, help="Show what would happen without making changes"
)
@with_appcontext
def create_forever_free_subscription(user_id, plan_name, coupon_id, dry_run):
    """
    Create forever free subscription for a specified user using a coupon.

    This command validates the coupon is forever free (100% discount + forever duration),
    creates/uses Stripe customer, finds the plan, and creates a Stripe subscription
    with the coupon applied.

    Examples:
        flask admin create-forever-free-subscription --user-id=123456 --plan-name=basic_monthly --coupon-id=coupon_abc123
        flask admin create-forever-free-subscription --user-id=123456 --plan-name=pro_yearly --coupon-id=coupon_abc123 --dry-run
    """
    try:
        # 1. Validate user exists
        user = User.get_by_id(user_id)
        if not user:
            logger.error(f"User {user_id} not found")
            click.echo(f"❌ Error: User {user_id} not found", err=True)
            return

        logger.info(
            f"Found user: ID={user.id}, Email={user.email}, Name={user.full_name}"
        )
        click.echo(f"📋 User Info:")
        click.echo(f"   ID: {user.id}")
        click.echo(f"   Email: {user.email}")
        click.echo(f"   Name: {user.full_name}")
        click.echo(f"   Current Plan: {user.primary_plan}")
        click.echo(f"   Stripe Customer ID: {user.stripe_customer_id or 'None'}")

        # 2. Find subscription plan by name
        plan = Plan.query.filter_by(
            name=plan_name, plan_type=PlanType.SUBSCRIPTION, is_active=True
        ).first()

        if not plan:
            logger.error(f"No active subscription plan found with name: {plan_name}")
            click.echo(f"❌ Error: No active subscription plan found with name: {plan_name}", err=True)
            return

        logger.info(
            f"Found plan: ID={plan.id}, Name={plan.name}, Tier={plan.tier}, Credits={plan.credit_amount}"
        )
        click.echo(f"📦 Plan Info:")
        click.echo(f"   ID: {plan.id}")
        click.echo(f"   Name: {plan.name}")
        click.echo(f"   Tier: {plan.tier}")
        click.echo(f"   Interval: {plan.interval}")
        click.echo(f"   Credits: {plan.credit_amount}")
        click.echo(f"   Stripe Price ID: {plan.stripe_price_id}")

        # 3. Validate coupon is forever free
        is_valid, coupon_info = _validate_forever_free_coupon(coupon_id)
        if not is_valid:
            logger.error(f"Coupon {coupon_id} is not a valid forever free coupon")
            click.echo(f"❌ Error: Coupon {coupon_id} is not a valid forever free coupon", err=True)
            return

        click.echo(f"✅ Coupon Info:")
        click.echo(f"   ID: {coupon_info['id']}")
        click.echo(f"   Name: {coupon_info.get('name', 'N/A')}")
        click.echo(f"   Percent Off: {coupon_info['percent_off']}%")
        click.echo(f"   Duration: {coupon_info['duration']}")
        click.echo(f"   Valid: {coupon_info['valid']}")

        # 4. Create or use existing Stripe customer
        stripe_customer_id = user.stripe_customer_id
        if not stripe_customer_id:
            if not dry_run:
                stripe_customer_id = StripeService.create_customer(
                    user.email, 
                    metadata={"source": "forever_free_subscription"}
                )
                User.set_stripe_customer_id(user.id, stripe_customer_id)
                User.set_stripe_customer_source(user.id, "forever_free_subscription")
                click.echo(f"✅ Created Stripe customer: {stripe_customer_id}")
            else:
                stripe_customer_id = f"cus_fake_{user_id}"
                click.echo(f"🔍 Would create Stripe customer for {user.email}")
        else:
            click.echo(f"✅ Using existing Stripe customer: {stripe_customer_id}")

        # 5. Show summary and ask for confirmation
        click.echo(f"\n📋 Summary:")
        click.echo(f"   User: {user.email} (ID: {user_id})")
        click.echo(f"   Plan: {plan.name} ({plan.tier} - {plan.interval})")
        click.echo(f"   Coupon: {coupon_id} (100% off forever)")
        click.echo(f"   Stripe Customer: {stripe_customer_id}")

        if dry_run:
            click.echo(f"\n🔍 DRY RUN - No changes will be made")
            click.echo(f"✅ Would create Stripe subscription with coupon applied")
            click.echo(f"✅ Would create local Subscription record")
            return

        # 6. Ask for confirmation
        click.echo(f"\n⚠️  This will create a new forever free subscription for the user.")
        if not click.confirm("Do you want to proceed?", default=False):
            click.echo("❌ Operation cancelled")
            return

        # 7. Create Stripe subscription with coupon
        _create_stripe_subscription_with_coupon(user, plan, stripe_customer_id, coupon_id)

        click.echo(f"✅ Successfully created forever free subscription for user {user.email}")
        logger.info(
            f"Successfully created forever free subscription for user {user_id}, plan {plan_name}, coupon {coupon_id}"
        )

    except Exception as e:
        logger.error(f"Failed to create forever free subscription: {str(e)}")
        click.echo(f"❌ Error: {str(e)}", err=True)
        raise click.ClickException(str(e))


def _validate_forever_free_coupon(coupon_id):
    """
    Validate that the coupon is forever free (100% discount + forever duration).

    Args:
        coupon_id: Stripe coupon ID

    Returns:
        tuple: (is_valid, coupon_info_dict)
    """
    try:
        coupon = stripe.Coupon.retrieve(coupon_id)
        
        # Check if coupon is valid
        if not coupon.valid:
            return False, {"error": "Coupon is not valid"}
        
        # Check if it's 100% discount
        if coupon.percent_off != 100:
            return False, {"error": f"Coupon discount is {coupon.percent_off}%, not 100%"}
        
        # Check if it's forever duration
        if coupon.duration != "forever":
            return False, {"error": f"Coupon duration is '{coupon.duration}', not 'forever'"}
        
        coupon_info = {
            "id": coupon.id,
            "name": coupon.name,
            "percent_off": coupon.percent_off,
            "duration": coupon.duration,
            "valid": coupon.valid,
        }
        
        return True, coupon_info
        
    except stripe.error.StripeError as e:
        logger.error(f"Error validating coupon {coupon_id}: {str(e)}")
        return False, {"error": f"Stripe error: {str(e)}"}


@db_transaction()
def _create_stripe_subscription_with_coupon(user, plan, stripe_customer_id, coupon_id):
    """
    Create Stripe subscription with coupon and local subscription record.

    Args:
        user: User object
        plan: Plan object
        stripe_customer_id: Stripe customer ID
        coupon_id: Stripe coupon ID
    """
    try:
        # Create Stripe subscription with coupon
        stripe_subscription = stripe.Subscription.create(
            customer=stripe_customer_id,
            items=[{"price": plan.stripe_price_id}],
            coupon=coupon_id,
            metadata={
                "user_id": str(user.id),
                "plan_name": plan.name,
                "source": "forever_free_subscription"
            }
        )
        
        logger.info(
            f"Created Stripe subscription: ID={stripe_subscription.id}, Status={stripe_subscription.status}"
        )
        
        # Create local subscription record
        subscription = Subscription(
            id=id_generator.get_id(),
            user_id=user.id,
            plan_id=plan.id,
            stripe_subscription_id=stripe_subscription.id,
            status=stripe_subscription.status,
            current_period_start=datetime.fromtimestamp(stripe_subscription.current_period_start),
            current_period_end=datetime.fromtimestamp(stripe_subscription.current_period_end),
            cancel_at_period_end=stripe_subscription.cancel_at_period_end,
            source="forever_free_subscription",
        )
        db.session.add(subscription)
        db.session.flush()
        
        logger.info(
            f"Created local subscription record: ID={subscription.id}"
        )
        
        click.echo(f"✅ Stripe subscription created: {stripe_subscription.id}")
        click.echo(f"✅ Local subscription record created: ID={subscription.id}")
        click.echo(f"   Status: {subscription.status}")
        click.echo(f"   Period: {subscription.current_period_start} to {subscription.current_period_end}")
        click.echo(f"   Cancel at period end: {subscription.cancel_at_period_end}")
        
    except stripe.error.StripeError as e:
        logger.error(f"Stripe error creating subscription: {str(e)}")
        raise Exception(f"Failed to create Stripe subscription: {str(e)}")
    except Exception as e:
        logger.error(f"Error creating subscription: {str(e)}")
        raise Exception(f"Failed to create subscription: {str(e)}")
