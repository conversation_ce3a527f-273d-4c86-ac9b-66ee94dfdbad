# Forever Free Subscription Script

This script creates forever free subscriptions for users using Stripe coupons with 100% discount and forever duration.

## Usage

```bash
flask admin create-forever-free-subscription --user-id=<USER_ID> --plan-name=<PLAN_NAME> --coupon-id=<COUPON_ID> [--dry-run]
```

## Parameters

- `--user-id`: The user ID to create the subscription for (required)
- `--plan-name`: The subscription plan name (required)
- `--coupon-id`: The Stripe coupon ID to apply (required)
- `--dry-run`: Show what would happen without making changes (optional)

## Available Plan Names

The script supports the following subscription plans:

- `basic_monthly`: Basic plan with monthly billing (1200 credits)
- `basic_yearly`: Basic plan with yearly billing (1200 credits)
- `pro_monthly`: Pro plan with monthly billing (6000 credits)
- `pro_yearly`: Pro plan with yearly billing (6000 credits)

## Coupon Requirements

The coupon must meet the following criteria:

1. **100% discount**: The coupon must provide a 100% discount
2. **Forever duration**: The coupon must have a "forever" duration (not "once" or "repeating")
3. **Valid**: The coupon must be active and not expired

## Examples

### Dry Run (Recommended First)

```bash
# Test with a dry run first
flask admin create-forever-free-subscription \
  --user-id=7322592611297005568 \
  --plan-name=basic_monthly \
  --coupon-id=coupon_abc123 \
  --dry-run
```

### Create Actual Subscription

```bash
# Create the actual subscription
flask admin create-forever-free-subscription \
  --user-id=7322592611297005568 \
  --plan-name=pro_yearly \
  --coupon-id=coupon_abc123
```

## What the Script Does

1. **Validates the user**: Checks if the user exists in the database
2. **Validates the plan**: Finds the subscription plan by name
3. **Validates the coupon**: Ensures the coupon is forever free (100% discount + forever duration)
4. **Creates/uses Stripe customer**: Creates a new Stripe customer if the user doesn't have one
5. **Creates Stripe subscription**: Creates a Stripe subscription with the coupon applied
6. **Creates local record**: Creates a local subscription record in the database

## Error Handling

The script will fail with appropriate error messages if:

- User doesn't exist
- Plan doesn't exist or isn't active
- Coupon doesn't exist, isn't valid, or doesn't meet the forever free criteria
- Stripe API errors occur
- Database errors occur

## Prerequisites

1. Valid Stripe API keys configured in the environment
2. Database connection configured
3. User must have a valid email address
4. Coupon must be created in Stripe beforehand

## Creating a Forever Free Coupon in Stripe

To create a forever free coupon in Stripe:

1. Go to Stripe Dashboard > Products > Coupons
2. Click "Create coupon"
3. Set:
   - **Discount type**: Percentage
   - **Percentage**: 100%
   - **Duration**: Forever
   - **Name**: Give it a descriptive name
4. Save the coupon and use its ID in the script

## Notes

- Always test with `--dry-run` first
- The script creates both Stripe subscription and local database records
- The subscription will be active immediately with 0 cost due to the 100% coupon
- Users can manage their subscription through the Stripe customer portal
