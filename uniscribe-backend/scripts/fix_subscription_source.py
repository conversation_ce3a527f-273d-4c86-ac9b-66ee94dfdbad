#!/usr/bin/env python3
"""
Fix subscription source field by retrieving from Stripe metadata

This script identifies subscriptions with null source fields (created after 2025-07-26)
and attempts to update them by fetching the latest subscription data from Stripe.

Note: Only processes subscriptions created after 2025-07-26 since source tracking
was not implemented before that date.
"""

import sys
import os
import logging
from datetime import datetime

# Add the parent directory to the path so we can import modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from flask.cli import with_appcontext
import click
import stripe

logger = logging.getLogger(__name__)


@click.command()
@click.option('--dry-run', is_flag=True, help='Show what would be updated without making changes')
@click.option('--limit', type=int, default=100, help='Maximum number of subscriptions to process')
def fix_subscription_source(dry_run, limit):
    """Fix subscription source field by retrieving from Stripe metadata

    Only processes subscriptions created after 2025-07-26 since source tracking
    was not implemented before that date.
    """
    
    print("🔧 Fixing subscription source fields...")
    print("=" * 60)
    
    if dry_run:
        print("🔍 DRY RUN MODE - No changes will be made")
        print()
    
    # Define the start date for source tracking (2025-07-26)
    source_tracking_start_date = datetime(2025, 7, 26)

    # Find subscriptions with null source created after 2025-07-26
    subscriptions_with_null_source = Subscription.query.filter(
        Subscription.source.is_(None),
        Subscription.created_time >= source_tracking_start_date
    ).limit(limit).all()
    
    if not subscriptions_with_null_source:
        print("✅ No subscriptions found with null source field (created after 2025-07-26)")
        return

    print(f"📊 Found {len(subscriptions_with_null_source)} subscriptions with null source (created after 2025-07-26)")
    print()
    
    updated_count = 0
    error_count = 0
    
    for subscription in subscriptions_with_null_source:
        try:
            print(f"🔍 Processing subscription: {subscription.stripe_subscription_id}")
            
            # Retrieve subscription from Stripe
            stripe_subscription = stripe.Subscription.retrieve(subscription.stripe_subscription_id)
            
            # Check if metadata contains source
            if hasattr(stripe_subscription, 'metadata') and stripe_subscription.metadata:
                stripe_source = stripe_subscription.metadata.get('source')
                
                if stripe_source:
                    print(f"  ✓ Found source in Stripe metadata: {stripe_source}")
                    
                    if not dry_run:
                        subscription.source = stripe_source
                        db.session.add(subscription)
                        updated_count += 1
                    else:
                        print(f"  📝 Would update source to: {stripe_source}")
                        updated_count += 1
                else:
                    print(f"  ⚠️  No source found in Stripe metadata")
            else:
                print(f"  ⚠️  No metadata found in Stripe subscription")
                
        except stripe.error.StripeError as e:
            print(f"  ❌ Stripe API error: {e}")
            error_count += 1
        except Exception as e:
            print(f"  ❌ Unexpected error: {e}")
            error_count += 1
        
        print()
    
    if not dry_run and updated_count > 0:
        try:
            db.session.commit()
            print(f"✅ Successfully updated {updated_count} subscriptions")
        except Exception as e:
            db.session.rollback()
            print(f"❌ Failed to commit changes: {e}")
            return
    
    print("📊 Summary:")
    print(f"  - Processed: {len(subscriptions_with_null_source)} subscriptions")
    print(f"  - Updated: {updated_count} subscriptions")
    print(f"  - Errors: {error_count} subscriptions")
    
    if dry_run and updated_count > 0:
        print()
        print("💡 Run without --dry-run to apply the changes")


if __name__ == "__main__":
    # Import after path setup
    from models.subscription import Subscription
    from models import db
    from app import app

    with app.app_context():
        fix_subscription_source()
