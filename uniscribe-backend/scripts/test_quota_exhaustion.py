#!/usr/bin/env python3
"""
Test script to simulate API transcription with quota exhaustion

Usage:
    export API_KEY="your_api_key_here"
    python scripts/test_quota_exhaustion.py --file-url "https://example.com/audio.mp3"

This script will:
1. Call the OpenAPI transcription endpoint with speaker diarization enabled
2. Test the quota checking behavior when user is close to quota limit
3. Monitor the transcription status until completion or failure

Before running this script, manually adjust the user's quota to be close to exhaustion
in the database to test the quota checking behavior.
"""

import os
import sys
import time
import json
import argparse
import requests
from typing import Optional, Dict, Any


class OpenAPITranscriptionTester:
    def __init__(self, api_key: str, base_url: str = "http://localhost:8000"):
        self.api_key = api_key
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'X-API-Key': api_key,
            'Content-Type': 'application/json'
        })

    def create_transcription(self, file_url: str, filename: Optional[str] = None, 
                           webhook_url: Optional[str] = None) -> Dict[str, Any]:
        """Create a new transcription from file URL with speaker diarization enabled"""
        
        endpoint = f"{self.base_url}/api/v1/transcriptions"
        
        payload = {
            "file_url": file_url,
            "language_code": "en",
            "transcription_type": "transcript",
            "enable_speaker_diarization": True,  # Enable speaker diarization as requested
        }
        
        if filename:
            payload["filename"] = filename
        if webhook_url:
            payload["webhook_url"] = webhook_url
            
        print(f"🚀 Creating transcription...")
        print(f"   File URL: {file_url}")
        print(f"   Speaker Diarization: Enabled")
        print(f"   Language: {payload['language_code']}")
        
        try:
            response = self.session.post(endpoint, json=payload)
            
            print(f"📡 Response Status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Transcription created successfully!")
                print(f"   Transcription ID: {result['data']['id']}")
                print(f"   Status: {result['data']['status']}")
                return result
            else:
                print(f"❌ Failed to create transcription")
                print(f"   Status Code: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"   Error: {json.dumps(error_data, indent=2)}")
                except:
                    print(f"   Raw Response: {response.text}")
                return {"error": True, "status_code": response.status_code, "response": response.text}
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Network error: {e}")
            return {"error": True, "exception": str(e)}

    def get_transcription_status(self, transcription_id: str) -> Dict[str, Any]:
        """Get transcription status"""
        
        endpoint = f"{self.base_url}/api/v1/transcriptions/{transcription_id}/status"
        
        try:
            response = self.session.get(endpoint)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ Failed to get status (HTTP {response.status_code})")
                try:
                    error_data = response.json()
                    print(f"   Error: {json.dumps(error_data, indent=2)}")
                except:
                    print(f"   Raw Response: {response.text}")
                return {"error": True, "status_code": response.status_code}
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Network error: {e}")
            return {"error": True, "exception": str(e)}

    def get_transcription_result(self, transcription_id: str) -> Dict[str, Any]:
        """Get transcription result with segments"""
        
        endpoint = f"{self.base_url}/api/v1/transcriptions/{transcription_id}"
        
        try:
            response = self.session.get(endpoint)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ Failed to get result (HTTP {response.status_code})")
                return {"error": True, "status_code": response.status_code}
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Network error: {e}")
            return {"error": True, "exception": str(e)}

    def monitor_transcription(self, transcription_id: str, max_wait_minutes: int = 10) -> Dict[str, Any]:
        """Monitor transcription progress until completion or timeout"""
        
        print(f"🔍 Monitoring transcription {transcription_id}...")
        
        start_time = time.time()
        max_wait_seconds = max_wait_minutes * 60
        
        while True:
            status_result = self.get_transcription_status(transcription_id)
            
            if status_result.get("error"):
                return status_result
                
            status = status_result.get("data", {}).get("status", "unknown")
            print(f"   Status: {status}")
            
            # Check for completion states
            if status in ["completed", "failed", "cancelled"]:
                print(f"🏁 Transcription finished with status: {status}")
                
                if status == "completed":
                    # Get the full result with segments
                    result = self.get_transcription_result(transcription_id)
                    if not result.get("error"):
                        segments = result.get("data", {}).get("segments", [])
                        has_speaker_info = result.get("data", {}).get("hasSpeakerInfo", False)
                        print(f"   Segments count: {len(segments)}")
                        print(f"   Has speaker info: {has_speaker_info}")
                        
                        # Show first few segments as sample
                        if segments:
                            print(f"   Sample segments:")
                            for i, segment in enumerate(segments[:3]):
                                speaker = segment.get("speaker", "N/A")
                                text = segment.get("text", "")[:50] + "..." if len(segment.get("text", "")) > 50 else segment.get("text", "")
                                print(f"     [{i+1}] Speaker {speaker}: {text}")
                    
                return status_result
            
            # Check timeout
            elapsed = time.time() - start_time
            if elapsed > max_wait_seconds:
                print(f"⏰ Timeout after {max_wait_minutes} minutes")
                return {"error": True, "timeout": True, "last_status": status}
            
            # Wait before next check
            time.sleep(10)


def spam_api_requests(tester: OpenAPITranscriptionTester, file_url: str,
                     filename: Optional[str] = None, webhook_url: Optional[str] = None,
                     max_requests: int = 100, delay_seconds: float = 1.0):
    """
    Simulate bad behavior: spam API requests without caring about results
    This is the kind of behavior we want to prevent with quota checking
    """
    print("💀 STARTING BAD BEHAVIOR SIMULATION")
    print("   This simulates poorly written client code that:")
    print("   - Makes unlimited API requests")
    print("   - Doesn't check transcription results")
    print("   - Doesn't respect rate limits")
    print("   - Wastes server resources")
    print()

    successful_requests = 0
    failed_requests = 0
    quota_blocked_requests = 0

    for i in range(1, max_requests + 1):
        print(f"🔥 Request #{i} - Spamming API...")

        result = tester.create_transcription(
            file_url=file_url,
            filename=f"{filename}_{i}" if filename else f"spam_request_{i}.mp3",
            webhook_url=webhook_url
        )

        # Print raw response for debugging
        print(f"📄 Raw Response #{i}:")
        if result.get("error"):
            print(f"   ❌ ERROR: {json.dumps(result, indent=2)}")
            failed_requests += 1

            # Check if it's quota related
            response_text = result.get("response", "").lower()
            if (result.get("status_code") == 400 and
                ("quota" in response_text or "minutes" in response_text or "upgrade" in response_text)):
                quota_blocked_requests += 1
                print(f"   🎯 QUOTA BLOCKED! (Request #{i})")

                # In bad client code, they might just continue anyway...
                print(f"   💀 Bad client continues spamming despite quota block...")
        else:
            print(f"   ✅ SUCCESS: {json.dumps(result, indent=2)}")
            successful_requests += 1

            # Bad behavior: immediately make another request without waiting for completion
            print(f"   💀 Bad client doesn't wait for transcription to complete...")

        print(f"   📊 Stats so far: Success={successful_requests}, Failed={failed_requests}, Quota Blocked={quota_blocked_requests}")
        print("-" * 80)

        # Small delay to avoid overwhelming the server completely
        if delay_seconds > 0:
            time.sleep(delay_seconds)

    print("\n" + "=" * 80)
    print("💀 BAD BEHAVIOR SIMULATION COMPLETE")
    print(f"📊 Final Stats:")
    print(f"   Total Requests: {max_requests}")
    print(f"   Successful: {successful_requests}")
    print(f"   Failed: {failed_requests}")
    print(f"   Quota Blocked: {quota_blocked_requests}")
    print(f"   Success Rate: {successful_requests/max_requests*100:.1f}%")
    print(f"   Quota Block Rate: {quota_blocked_requests/max_requests*100:.1f}%")

    if quota_blocked_requests > 0:
        print(f"\n🎯 SUCCESS: Quota checking blocked {quota_blocked_requests} requests!")
        print("   This demonstrates that the quota system is working correctly")
        print("   to prevent resource waste from poorly written clients.")
    else:
        print(f"\n⚠️  WARNING: No quota blocks detected!")
        print("   Either the user has unlimited quota or quota checking isn't working.")


def main():
    parser = argparse.ArgumentParser(description="Test OpenAPI transcription with quota exhaustion")
    parser.add_argument("--file-url", required=True, help="URL of the audio file to transcribe")
    parser.add_argument("--filename", help="Optional filename for the transcription")
    parser.add_argument("--webhook-url", help="Optional webhook URL for notifications")
    parser.add_argument("--base-url", default="http://localhost:8000", help="API base URL")
    parser.add_argument("--max-wait", type=int, default=10, help="Maximum wait time in minutes")

    # Add spam mode options
    parser.add_argument("--spam-mode", action="store_true",
                       help="Enable spam mode: make unlimited requests without caring about results")
    parser.add_argument("--max-requests", type=int, default=50,
                       help="Maximum number of requests in spam mode (default: 50)")
    parser.add_argument("--delay", type=float, default=1.0,
                       help="Delay between requests in seconds (default: 1.0)")

    args = parser.parse_args()
    
    # Get API key from environment
    api_key = os.getenv("API_KEY")
    if not api_key:
        print("❌ Error: API_KEY environment variable is required")
        print("   Usage: export API_KEY='your_api_key_here'")
        sys.exit(1)

    # Initialize tester
    tester = OpenAPITranscriptionTester(api_key, args.base_url)

    if args.spam_mode:
        # SPAM MODE: Simulate bad client behavior
        print("💀 SPAM MODE ENABLED - Simulating Bad Client Behavior")
        print("=" * 80)
        print(f"File URL: {args.file_url}")
        print(f"API Base URL: {args.base_url}")
        print(f"Max Requests: {args.max_requests}")
        print(f"Delay Between Requests: {args.delay}s")
        print()
        print("⚠️  WARNING: This simulates poorly written client code!")
        print("   Real clients should NEVER behave this way.")
        print()

        # Start spamming
        spam_api_requests(
            tester=tester,
            file_url=args.file_url,
            filename=args.filename,
            webhook_url=args.webhook_url,
            max_requests=args.max_requests,
            delay_seconds=args.delay
        )

    else:
        # NORMAL MODE: Proper client behavior
        print("🧪 OpenAPI Transcription Quota Exhaustion Test")
        print("=" * 50)
        print(f"File URL: {args.file_url}")
        print(f"API Base URL: {args.base_url}")
        print(f"Max Wait Time: {args.max_wait} minutes")
        print()

        # Create transcription
        result = tester.create_transcription(
            file_url=args.file_url,
            filename=args.filename,
            webhook_url=args.webhook_url
        )

        if result.get("error"):
            print("\n❌ Test failed during transcription creation")
            if result.get("status_code") == 400 or "quota" in result.get("response", "").lower():
                print("🎯 SUCCESS: Quota exhaustion was properly detected and blocked!")
            sys.exit(1)

        # Extract transcription ID
        transcription_id = result.get("data", {}).get("id")
        if not transcription_id:
            print("❌ Error: Could not extract transcription ID from response")
            sys.exit(1)

        print()

        # Monitor transcription progress
        final_result = tester.monitor_transcription(transcription_id, args.max_wait)

        print("\n" + "=" * 50)
        print("🏁 Test Summary")

        if final_result.get("error"):
            if final_result.get("timeout"):
                print("⏰ Test timed out - transcription may still be processing")
            else:
                print("❌ Test failed during monitoring")
        else:
            final_status = final_result.get("data", {}).get("status", "unknown")
            if final_status == "completed":
                print("✅ Test completed successfully!")
                print("🎯 Transcription with speaker diarization was processed successfully")
            elif final_status == "failed":
                print("❌ Transcription failed")
                print("🎯 This might indicate quota exhaustion during processing")
            else:
                print(f"ℹ️  Test ended with status: {final_status}")


if __name__ == "__main__":
    main()
