#!/usr/bin/env python3
"""
Test script to verify URL validator compatibility with frontend supported formats
"""

import sys
import os

# Add the parent directory to the path so we can import from libs
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from libs.url_validator import VALID_MIME_TYPES, VALID_EXTENSIONS


def test_frontend_compatibility():
    """Test that our validator supports all frontend formats"""
    
    # Frontend MIME_TO_EXTENSION mapping
    frontend_mime_types = {
        "audio/mpeg": "mp3",
        "audio/mp4": "m4a",
        "audio/x-m4a": "m4a",
        "audio/wav": "wav",
        "audio/x-wav": "wav",
        "audio/webm": "webm",
        "audio/weba": "weba",
        "audio/amr": "amr",
        "audio/amr-wb": "awb",
        "video/mp4": "mp4",
        "video/webm": "webm",
        "video/quicktime": "mov",
        "video/mpeg": "mpg",
        "video/3gpp": "3gp",
        "video/x-ms-wmv": "wmv",
        "video/x-matroska": "mkv",
        "audio/x-matroska": "mka",
        "audio/x-ms-wma": "wma",
        "audio/vnd.dlna.adts": "aac",
        "audio/aac": "aac",
        "audio/ogg": "ogg",
        "audio/oga": "oga",
        "audio/opus": "opus",
        "audio/flac": "flac",
        "audio/x-flac": "flac",
    }
    
    print("🔍 Testing Frontend Format Compatibility")
    print("=" * 50)
    
    # Test MIME types
    print("\n📋 MIME Type Coverage:")
    missing_mime_types = []
    for mime_type in frontend_mime_types.keys():
        if mime_type in VALID_MIME_TYPES:
            print(f"✅ {mime_type}")
        else:
            print(f"❌ {mime_type} - MISSING")
            missing_mime_types.append(mime_type)
    
    # Test extensions
    print("\n📁 Extension Coverage:")
    frontend_extensions = set(f".{ext}" for ext in frontend_mime_types.values())
    missing_extensions = []
    for ext in frontend_extensions:
        if ext in VALID_EXTENSIONS:
            print(f"✅ {ext}")
        else:
            print(f"❌ {ext} - MISSING")
            missing_extensions.append(ext)
    
    # Summary
    print("\n📊 Summary:")
    print(f"Frontend MIME types: {len(frontend_mime_types)}")
    print(f"Validator MIME types: {len(VALID_MIME_TYPES)}")
    print(f"Missing MIME types: {len(missing_mime_types)}")
    
    print(f"Frontend extensions: {len(frontend_extensions)}")
    print(f"Validator extensions: {len(VALID_EXTENSIONS)}")
    print(f"Missing extensions: {len(missing_extensions)}")
    
    if missing_mime_types:
        print(f"\n⚠️  Missing MIME types: {missing_mime_types}")
    
    if missing_extensions:
        print(f"\n⚠️  Missing extensions: {missing_extensions}")
    
    if not missing_mime_types and not missing_extensions:
        print("\n🎉 Perfect compatibility! All frontend formats are supported.")
        return True
    else:
        print("\n❌ Some frontend formats are not supported by the validator.")
        return False


def test_example_urls():
    """Test some example URLs with different formats"""
    from libs.url_validator import validate_url_for_media_download
    
    print("\n\n🌐 Testing Example URLs:")
    print("=" * 50)
    
    # Test URLs for different formats (these are mock URLs for testing)
    test_urls = [
        ("https://example.com/audio.mp3", "MP3 Audio"),
        ("https://example.com/audio.m4a", "M4A Audio"),
        ("https://example.com/audio.wav", "WAV Audio"),
        ("https://example.com/audio.flac", "FLAC Audio"),
        ("https://example.com/audio.ogg", "OGG Audio"),
        ("https://example.com/audio.opus", "Opus Audio"),
        ("https://example.com/audio.aac", "AAC Audio"),
        ("https://example.com/audio.wma", "WMA Audio"),
        ("https://example.com/video.mp4", "MP4 Video"),
        ("https://example.com/video.mov", "MOV Video"),
        ("https://example.com/video.webm", "WebM Video"),
        ("https://example.com/video.mkv", "MKV Video"),
        ("https://example.com/video.3gp", "3GP Video"),
        ("https://example.com/audio.amr", "AMR Audio"),
    ]
    
    for url, description in test_urls:
        print(f"\n🔗 Testing {description}: {url}")
        try:
            # This will fail due to network, but we can check extension detection
            result = validate_url_for_media_download(url)
            if result.detected_extension:
                print(f"   ✅ Extension detected: {result.detected_extension}")
            else:
                print(f"   ❌ No extension detected")
        except Exception as e:
            # Expected to fail due to network, but check if extension was detected
            if "detected_extension" in str(e) or url.split('.')[-1]:
                ext = '.' + url.split('.')[-1]
                if ext in VALID_EXTENSIONS:
                    print(f"   ✅ Extension {ext} is supported")
                else:
                    print(f"   ❌ Extension {ext} is not supported")


if __name__ == "__main__":
    print("Frontend Format Compatibility Test")
    print("This script verifies that the URL validator supports all formats that the frontend expects")
    
    success = test_frontend_compatibility()
    test_example_urls()
    
    if success:
        print("\n🎉 All tests passed! The validator is compatible with frontend formats.")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed. Please update the validator to support missing formats.")
        sys.exit(1)
