#!/usr/bin/env python3
"""
Fix stripe_customer_source field by retrieving from Stripe customer metadata

This script identifies users with Stripe customer IDs but null stripe_customer_source fields
and attempts to update them by fetching the latest customer data from Stripe.

Note: Only processes users created after 2025-07-26 since source tracking
was not implemented before that date.
"""

import sys
import os
import logging
from datetime import datetime

# Add the parent directory to the path so we can import modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from flask.cli import with_appcontext
import click
import stripe

logger = logging.getLogger(__name__)


@click.command()
@click.option('--dry-run', is_flag=True, help='Show what would be updated without making changes')
@click.option('--limit', type=int, default=100, help='Maximum number of users to process')
def fix_stripe_customer_source(dry_run, limit):
    """Fix stripe_customer_source field by retrieving from Stripe customer metadata
    
    Only processes users created after 2025-07-26 since source tracking
    was not implemented before that date.
    """
    
    print("🔧 Fixing stripe customer source fields...")
    print("=" * 60)
    
    if dry_run:
        print("🔍 DRY RUN MODE - No changes will be made")
        print()
    
    # Define the start date for source tracking (2025-07-26)
    source_tracking_start_date = datetime(2025, 7, 26)
    
    # Find users with Stripe customer IDs but null stripe_customer_source created after 2025-07-26
    users_with_null_source = User.query.filter(
        User.stripe_customer_id.isnot(None),
        User.stripe_customer_source.is_(None),
        User.created_time >= source_tracking_start_date
    ).limit(limit).all()
    
    if not users_with_null_source:
        print("✅ No users found with null stripe_customer_source field (created after 2025-07-26)")
        return
    
    print(f"📊 Found {len(users_with_null_source)} users with null stripe_customer_source (created after 2025-07-26)")
    print()
    
    updated_count = 0
    error_count = 0
    
    for user in users_with_null_source:
        try:
            print(f"🔍 Processing user: {user.id} (customer: {user.stripe_customer_id})")
            
            # Retrieve customer from Stripe
            stripe_customer = stripe.Customer.retrieve(user.stripe_customer_id)
            
            # Check if metadata contains source
            if hasattr(stripe_customer, 'metadata') and stripe_customer.metadata:
                stripe_source = stripe_customer.metadata.get('source')
                
                if stripe_source:
                    print(f"  ✓ Found source in Stripe customer metadata: {stripe_source}")
                    
                    if not dry_run:
                        user.stripe_customer_source = stripe_source
                        db.session.add(user)
                        updated_count += 1
                    else:
                        print(f"  📝 Would update stripe_customer_source to: {stripe_source}")
                        updated_count += 1
                else:
                    print(f"  ⚠️  No source found in Stripe customer metadata")
            else:
                print(f"  ⚠️  No metadata found in Stripe customer")
                
        except stripe.error.StripeError as e:
            print(f"  ❌ Stripe API error: {e}")
            error_count += 1
        except Exception as e:
            print(f"  ❌ Unexpected error: {e}")
            error_count += 1
        
        print()
    
    if not dry_run and updated_count > 0:
        try:
            db.session.commit()
            print(f"✅ Successfully updated {updated_count} users")
        except Exception as e:
            db.session.rollback()
            print(f"❌ Failed to commit changes: {e}")
            return
    
    print("📊 Summary:")
    print(f"  - Processed: {len(users_with_null_source)} users")
    print(f"  - Updated: {updated_count} users")
    print(f"  - Errors: {error_count} users")
    
    if dry_run and updated_count > 0:
        print()
        print("💡 Run without --dry-run to apply the changes")


if __name__ == "__main__":
    # Import after path setup
    from models.user import User
    from models import db
    from app import app
    
    with app.app_context():
        fix_stripe_customer_source()
