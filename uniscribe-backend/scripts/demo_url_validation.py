#!/usr/bin/env python3
"""
Demo script showing URL validation functionality
This demonstrates how the URL validation works before creating transcription tasks
"""

import sys
import os
import logging

# Add the parent directory to the path so we can import from libs
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from libs.url_validator import validate_url_for_media_download, get_suggested_filename_from_url

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def simulate_openapi_request(file_url, filename=None):
    """
    Simulate the OpenAPI request flow with URL validation
    This shows how the validation integrates into the actual request handling
    """
    print(f"\n{'='*80}")
    print(f"🎵 SIMULATING OPENAPI TRANSCRIPTION REQUEST")
    print(f"{'='*80}")
    print(f"📥 Input URL: {file_url}")
    print(f"📝 Input Filename: {filename or 'Not provided'}")
    
    # Step 1: Basic URL format validation (existing logic)
    print(f"\n📋 Step 1: Basic URL format validation...")
    if not file_url.startswith(('http://', 'https://')):
        print("❌ REJECTED: Invalid URL format. URL must start with http:// or https://")
        return False
    print("✅ Basic URL format is valid")
    
    # Step 2: URL accessibility and media file validation (NEW FEATURE)
    print(f"\n🔍 Step 2: URL accessibility and media file validation...")
    print("   - Making HTTP HEAD request to check accessibility")
    print("   - Validating Content-Type header")
    print("   - Checking file size limits")
    print("   - Detecting file extension")
    
    try:
        validation_result = validate_url_for_media_download(file_url)
        
        if not validation_result.is_valid:
            print(f"❌ REJECTED: {validation_result.error_message}")
            print("\n💡 This error is now caught BEFORE creating any tasks!")
            print("   User gets immediate feedback instead of waiting for worker to fail.")
            return False
        
        print("✅ URL validation successful!")
        print(f"   📄 Content-Type: {validation_result.content_type}")
        if validation_result.content_length:
            size_mb = validation_result.content_length / (1024 * 1024)
            print(f"   📏 File Size: {size_mb:.1f} MB")
        print(f"   🏷️  Detected Extension: {validation_result.detected_extension}")
        
    except Exception as e:
        print(f"❌ REJECTED: Validation failed due to error: {e}")
        return False
    
    # Step 3: Filename processing (enhanced with validation results)
    print(f"\n📝 Step 3: Filename processing...")
    if filename:
        final_filename = filename
        print(f"   Using provided filename: {final_filename}")
    else:
        final_filename = get_suggested_filename_from_url(file_url, validation_result.detected_extension)
        print(f"   Generated filename from URL: {final_filename}")
    
    # Step 4: Create transcription task (would happen next)
    print(f"\n🚀 Step 4: Creating transcription task...")
    print("   ✅ All validations passed - creating transcription file record")
    print("   ✅ Queuing media preprocessing task")
    print("   ✅ Returning success response to user")
    
    print(f"\n🎉 SUCCESS: Request accepted and task created!")
    print(f"   📁 Final filename: {final_filename}")
    print(f"   🔄 Task will be processed by worker asynchronously")
    
    return True


def main():
    """Run demonstration with various URL examples"""
    print("🎵 URL Validation Demo for OpenAPI Transcription")
    print("This demonstrates the new early URL validation feature")
    
    # Demo cases showing different scenarios
    demo_cases = [
        {
            "url": "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",
            "filename": None,
            "description": "Valid audio file URL (no filename provided)"
        },
        {
            "url": "https://example.com/podcast/episode1.mp3",
            "filename": "My Podcast Episode.mp3",
            "description": "Valid audio URL with custom filename"
        },
        {
            "url": "https://www.google.com",
            "filename": None,
            "description": "Invalid: HTML page instead of media file"
        },
        {
            "url": "https://nonexistent-domain-12345.com/audio.mp3",
            "filename": None,
            "description": "Invalid: Non-existent domain"
        },
        {
            "url": "ftp://example.com/audio.mp3",
            "filename": None,
            "description": "Invalid: Wrong protocol"
        }
    ]
    
    for i, case in enumerate(demo_cases, 1):
        print(f"\n\n{'🔸' * 40}")
        print(f"DEMO CASE {i}: {case['description']}")
        print(f"{'🔸' * 40}")
        
        success = simulate_openapi_request(case['url'], case['filename'])
        
        if success:
            print("✨ This request would proceed to create a transcription task")
        else:
            print("🛑 This request would be rejected immediately with helpful error message")
    
    print(f"\n\n{'🎯' * 20}")
    print("SUMMARY OF BENEFITS:")
    print("🎯" * 20)
    print("✅ Users get immediate feedback on invalid URLs")
    print("✅ No wasted resources on invalid downloads")
    print("✅ Better error messages help users fix issues")
    print("✅ Reduced load on worker processes")
    print("✅ Improved user experience")


if __name__ == "__main__":
    main()
