#!/usr/bin/env python3
"""
Cleanup script: physically delete tasks and transcription_file rows created when
clients exhausted minutes and still spammed the API.

Safety:
- Default is DRY RUN (no deletion). Pass --no-dry-run to actually delete.
- By default we will NOT delete a transcription_file if there are other tasks
  (any type) tied to that file to avoid breaking references. You can override
  with --force-file-delete.

Filters (match the provided SQL intent):
- task.task_type = 1 (transcription)
- task.requested_service_provider = 'deepinfra/whisper-word'
- task.status = 4 (failed)
- task.error_message = 'You have used up all your transcription minutes. Please upgrade your plan to continue.'

Usage (inside Docker):
  export PYTHONPATH=/app
  python scripts/cleanup_failed_quota_transcripts.py \
    --provider deepinfra/whisper-word \
    --error "You have used up all your transcription minutes. Please upgrade your plan to continue." \
    --limit 200 \
    --no-dry-run
"""

import argparse
import sys
from typing import List, Tuple

from flask import Flask
from config import CONFIG
from models import db
from models.task import Task
from models.transcription_file import TranscriptionFile
from models.task_result import TaskResult
from constants.task import TaskStatus, TaskType


DEFAULT_ERROR = (
    "You have used up all your transcription minutes. Please upgrade your plan to continue."
)


def find_candidates(provider: str, error_message: str, limit: int | None) -> List[Tuple[Task, TranscriptionFile]]:
    q = (
        db.session.query(Task, TranscriptionFile)
        .join(TranscriptionFile, Task.file_id == TranscriptionFile.id)
        .filter(Task.task_type == TaskType.transcription.id)
        .filter(Task.requested_service_provider == provider)
        .filter(Task.status == TaskStatus.failed.id)
        .filter(Task.error_message == error_message)
        .order_by(Task.id.asc())
    )
    if limit:
        q = q.limit(limit)
    return q.all()


def has_other_tasks(file_id: int, only_excluding_task_id: int | None = None) -> bool:
    q = Task.query.filter(Task.file_id == file_id)
    if only_excluding_task_id:
        q = q.filter(Task.id != only_excluding_task_id)
    return db.session.query(q.exists()).scalar()


def delete_one(task: Task, tf: TranscriptionFile, delete_task_result: bool, force_file_delete: bool, dry_run: bool) -> dict:
    info = {
        "task_id": task.id,
        "file_id": tf.id,
        "user_id": tf.user_id,
        "deleted_task": False,
        "deleted_task_result": False,
        "deleted_file": False,
        "skipped_file_reason": None,
    }

    # TaskResult (if any) is heavy; safe to delete first (respect flag)
    tr = TaskResult.get_by_file_id(tf.id)
    if tr and delete_task_result:
        if not dry_run:
            db.session.delete(tr)
        info["deleted_task_result"] = True

    # Delete the matched task (transcription, failed)
    if not dry_run:
        db.session.delete(task)
    info["deleted_task"] = True

    # Decide whether to delete the transcription_file row
    # Exclude the current task from the existence check so we can delete the file
    # when there are no other tasks besides this one.
    if not force_file_delete and has_other_tasks(tf.id, only_excluding_task_id=task.id):
        info["skipped_file_reason"] = "other tasks still reference this file"
    else:
        if not dry_run:
            db.session.delete(tf)
        info["deleted_file"] = True

    return info


def main():
    parser = argparse.ArgumentParser(description="Cleanup failed quota-created transcripts (physical delete)")
    parser.add_argument("--provider", default="deepinfra/whisper-word", help="Filter by requested_service_provider")
    parser.add_argument("--error", default=DEFAULT_ERROR, help="Exact error_message to match")
    parser.add_argument("--limit", type=int, default=None, help="Max rows to process")
    parser.add_argument("--no-dry-run", action="store_true", help="Actually delete records (default is dry run)")
    parser.add_argument("--force-file-delete", action="store_true", help="Delete transcription_file even if other tasks exist")
    parser.add_argument("--keep-task-result", action="store_true", help="Keep task_result rows (do not delete)")

    args = parser.parse_args()
    dry_run = not args.no_dry_run

    # Build a minimal app context to avoid route registration / Flask-RESTful conflicts
    from flask import Flask
    app = Flask(__name__)
    app.config.from_object(CONFIG)
    db.init_app(app)

    with app.app_context():
        candidates = find_candidates(args.provider, args.error, args.limit)

        print("=== Cleanup Plan ({} mode) ===".format("DRY RUN" if dry_run else "EXECUTE"))
        print(f"Provider: {args.provider}")
        print(f"Error message: {args.error}")
        print(f"Candidates found: {len(candidates)}")
        if not candidates:
            print("No matching rows. Nothing to do.")
            return 0

        total_task_results = 0
        total_tasks = 0
        total_files = 0
        skipped_files = 0

        for task, tf in candidates:
            info = delete_one(
                task=task,
                tf=tf,
                delete_task_result=(not args.keep_task_result),
                force_file_delete=args.force_file_delete,
                dry_run=True,  # First pass: always dry run to print plan per row
            )
            # Print plan per row
            print(
                f"- Task {info['task_id']} (status=failed) for File {info['file_id']} (user={info['user_id']}): "
                f"delete_task={info['deleted_task']} delete_task_result={info['deleted_task_result']} "
                f"delete_file={info['deleted_file']}"
                + (f" [SKIP FILE: {info['skipped_file_reason']}]" if info["skipped_file_reason"] else "")
            )
            total_task_results += int(info["deleted_task_result"])
            total_tasks += int(info["deleted_task"])
            total_files += int(info["deleted_file"])
            skipped_files += int(info["skipped_file_reason"] is not None)

        print("--- Totals (planned) ---")
        print(f"TaskResults to delete: {total_task_results}")
        print(f"Tasks to delete:       {total_tasks}")
        print(f"Files to delete:       {total_files} (skipped={skipped_files})")

        if dry_run:
            print("DRY RUN complete. Re-run with --no-dry-run to execute.")
            return 0

        # Execute pass
        print("Executing deletion...")
        executed_task_results = 0
        executed_tasks = 0
        executed_files = 0
        for task, tf in candidates:
            info = delete_one(
                task=task,
                tf=tf,
                delete_task_result=(not args.keep_task_result),
                force_file_delete=args.force_file_delete,
                dry_run=False,
            )
            executed_task_results += int(info["deleted_task_result"])
            executed_tasks += int(info["deleted_task"])
            executed_files += int(info["deleted_file"])

        db.session.commit()
        print("Deletion committed.")
        print(f"Deleted TaskResults: {executed_task_results}")
        print(f"Deleted Tasks:       {executed_tasks}")
        print(f"Deleted Files:       {executed_files}")
        return 0


if __name__ == "__main__":
    sys.exit(main())

