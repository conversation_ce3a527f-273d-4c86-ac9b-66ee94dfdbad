#!/usr/bin/env python3
"""
Manual test script for URL validation functionality
This script tests the URL validation with real URLs to ensure it works correctly
"""

import sys
import os
import logging

# Add the parent directory to the path so we can import from libs
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from libs.url_validator import validate_url_for_media_download, get_suggested_filename_from_url

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_url(url, description):
    """Test a single URL and print results"""
    print(f"\n{'='*60}")
    print(f"Testing: {description}")
    print(f"URL: {url}")
    print(f"{'='*60}")
    
    try:
        result = validate_url_for_media_download(url)
        
        print(f"Valid: {result.is_valid}")
        if result.is_valid:
            print(f"Content-Type: {result.content_type}")
            print(f"Content-Length: {result.content_length} bytes" if result.content_length else "Content-Length: Unknown")
            print(f"Detected Extension: {result.detected_extension}")
            if result.normalized_url:
                print(f"Normalized URL: {result.normalized_url}")

            # Test filename generation
            suggested_filename = get_suggested_filename_from_url(url, result.detected_extension)
            print(f"Suggested Filename: {suggested_filename}")
        else:
            print(f"Error: {result.error_message}")
            
    except Exception as e:
        print(f"Exception occurred: {e}")


def main():
    """Run manual tests with various URL types"""
    print("URL Validation Manual Test")
    print("This script tests URL validation with various types of URLs")
    
    # Test cases - you can modify these URLs for testing
    test_cases = [
        # Valid audio URLs (these might not work without actual files)
        # ("https://www.soundjay.com/misc/sounds/bell-ringing-05.wav", "Valid WAV file"),
        # ("https://file-examples.com/storage/fe68c1b7c66d0b0ecfeaa52/2017/11/file_example_MP3_700KB.mp3", "Valid MP3 file"),
        
        # Invalid URLs
        # ("https://www.google.com", "HTML page (should be rejected)"),
        # ("https://code4.life.s3.amazonaws.com/videos/course/recordings/2023/GMT20230819-165931_Recording_1280x840.mp4", "Non-existent domain"),
        ("https://1drv.ms/u/c/f4948ac2b8264280/EQT_Cw-wVlRFgLzIudc3TCsBmniOXMZUc1mpSnrWb9ifJA?e=rfTcY8", "OneDrive URL (should be rejected)"),
        # ("not-a-url", "Invalid URL format"),
        
        # Edge cases
        # ("https://httpbin.org/status/404", "404 Not Found"),
        # ("https://httpbin.org/status/401", "401 Unauthorized"),
        # ("https://httpbin.org/delay/15", "Timeout test (15 second delay)"),
    ]
    
    for url, description in test_cases:
        test_url(url, description)
    
    print(f"\n{'='*60}")
    print("Manual testing completed!")
    print("Note: Some tests may fail due to network conditions or URL availability.")
    print("This is expected behavior for testing purposes.")


if __name__ == "__main__":
    main()
