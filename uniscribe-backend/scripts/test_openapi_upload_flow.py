#!/usr/bin/env python3
"""
End-to-end test script for UniScribe OpenAPI file-upload flow.

Steps:
1) POST /api/v1/files/upload-url to get pre-signed upload_url and file_key
2) PUT the local file to upload_url
3) POST /api/v1/transcriptions with file_key + language_code
4) Poll GET /api/v1/transcriptions/{id}/status until completed/failed
5) GET /api/v1/transcriptions/{id} and print a brief summary

Requirements:
- Environment variable API_KEY must be set
- 'requests' library available (already in backend requirements)
- Use inside Docker container for testing convenience

Usage examples:
  API_KEY=sk_xxx python scripts/test_openapi_upload_flow.py \
      --file /tmp/a16z_000.mp3 --language en

Optional:
  API_KEY=sk_xxx python scripts/test_openapi_upload_flow.py \
      --file /tmp/a16z_000.mp3 --language en --webhook-url https://example.com/hook
"""

import argparse
import json
import mimetypes
import os
import sys
import time
from typing import Any, Dict

import requests


def eprint(*args, **kwargs):
    print(*args, file=sys.stderr, **kwargs)


def require_api_key() -> str:
    api_key = os.getenv("API_KEY")
    if not api_key:
        eprint("ERROR: Please set API_KEY environment variable.")
        sys.exit(2)
    return api_key


def request_json_or_die(resp: requests.Response) -> Dict[str, Any]:
    try:
        data = resp.json()
    except Exception:
        eprint(f"Non-JSON response (status={resp.status_code}):\n{resp.text[:500]}")
        resp.raise_for_status()
        raise
    return data


def check_api_success(payload: Dict[str, Any]) -> Dict[str, Any]:
    eprint(payload)
    if not payload.get("success"):
        err = payload.get("error", {})
        code = err.get("code", "-")
        msg = err.get("message", "Unknown error")
        details = err.get("details")
        eprint(f"API Error ({code}): {msg}")
        if details:
            eprint(f"Details: {details}")
        sys.exit(1)
    return payload.get("data", {})


def get_upload_urls(base_url: str, api_key: str, filename: str, file_size: int) -> Dict[str, Any]:
    url = f"{base_url}/api/v1/files/upload-url"
    headers = {"X-API-Key": api_key, "Content-Type": "application/json"}
    body = {
        "filename": filename,
        "file_size": file_size,
        # Optional expirations omitted to use defaults
    }
    resp = requests.post(url, headers=headers, json=body, timeout=30)
    data = check_api_success(request_json_or_die(resp))
    return data


def upload_file_to_presigned_url(upload_url: str, file_path: str, content_type: str) -> None:
    with open(file_path, "rb") as f:
        resp = requests.put(upload_url, data=f, headers={"Content-Type": content_type}, timeout=300)
    if not (200 <= resp.status_code < 300):
        eprint(f"Upload failed with status {resp.status_code}: {resp.text[:500]}")
        resp.raise_for_status()
    else:
        print(f"Upload succeeded (status={resp.status_code}).")


def create_transcription(base_url: str, api_key: str, file_key: str, language_code: str, webhook_url: str | None, filename: str | None = None) -> Dict[str, Any]:
    url = f"{base_url}/api/v1/transcriptions"
    headers = {"X-API-Key": api_key, "Content-Type": "application/json"}
    body: Dict[str, Any] = {
        "file_key": file_key,
        "language_code": language_code,
        "transcription_type": "transcript",
        "enable_speaker_diarization": False,
    }
    if filename:
        body["filename"] = filename
    if webhook_url:
        body["webhook_url"] = webhook_url
    resp = requests.post(url, headers=headers, json=body, timeout=30)
    data = check_api_success(request_json_or_die(resp))
    return data


def get_status(base_url: str, api_key: str, transcription_id: str) -> Dict[str, Any]:
    url = f"{base_url}/api/v1/transcriptions/{transcription_id}/status"
    headers = {"X-API-Key": api_key}
    resp = requests.get(url, headers=headers, timeout=30)
    data = check_api_success(request_json_or_die(resp))
    return data


def get_details(base_url: str, api_key: str, transcription_id: str) -> Dict[str, Any]:
    url = f"{base_url}/api/v1/transcriptions/{transcription_id}"
    headers = {"X-API-Key": api_key}
    resp = requests.get(url, headers=headers, timeout=30)
    data = check_api_success(request_json_or_die(resp))
    return data


def main():
    parser = argparse.ArgumentParser(description="Test UniScribe OpenAPI upload flow")
    parser.add_argument("--base-url", default="http://127.0.0.1:8000", help="API base URL")
    parser.add_argument("--file", required=True, help="Path to local file to upload")
    parser.add_argument("--language", default="en", help="Language code (e.g. en, zh, es)")
    parser.add_argument("--webhook-url", default=None, help="Optional webhook URL for callbacks")
    parser.add_argument("--poll-interval", type=int, default=10, help="Seconds between status polls")
    parser.add_argument("--timeout", type=int, default=1800, help="Max seconds to wait for completion")

    args = parser.parse_args()

    api_key = require_api_key()

    file_path = args.file
    if not os.path.isfile(file_path):
        eprint(f"File not found: {file_path}")
        sys.exit(2)

    filename = os.path.basename(file_path)
    file_size = os.path.getsize(file_path)
    mime, _ = mimetypes.guess_type(filename)
    content_type = mime or "application/octet-stream"

    print(f"1) Requesting upload URL for filename={filename}, size={file_size} bytes")
    urls = get_upload_urls(args.base_url, api_key, filename, file_size)
    upload_url = urls["upload_url"]
    file_key = urls["file_key"]
    print(f"Received file_key={file_key}")

    print("2) Uploading file to pre-signed URL...")
    upload_file_to_presigned_url(upload_url, file_path, content_type)

    print("3) Creating transcription task...")
    # Extract filename from file path
    filename = os.path.basename(file_path)
    task = create_transcription(args.base_url, api_key, file_key, args.language, args.webhook_url, filename)
    transcription_id = str(task.get("id"))
    status = task.get("status")
    print(f"Created transcription id={transcription_id}, initial status={status}")

    print("4) Polling status until completion...")
    start = time.time()
    while True:
        info = get_status(args.base_url, api_key, transcription_id)
        status = info.get("status")
        err_msg = info.get("error_message")
        print(f"Status: {status}")

        if status in ("completed", "failed"):
            if status == "failed":
                eprint(f"Transcription failed: {err_msg}")
                sys.exit(1)
            break

        if time.time() - start > args.timeout:
            eprint("Timed out waiting for transcription to complete.")
            sys.exit(124)

        time.sleep(args.poll_interval)

    print("Fetching final transcription details...")
    details = get_details(args.base_url, api_key, transcription_id)

    # Print a short summary
    result = details.get("result", {}) or {}
    text = (result.get("text") or "")
    print("\n=== Transcription Summary ===")
    print(f"ID: {transcription_id}")
    print(f"Filename: {details.get('filename')}")
    print(f"Language: {details.get('language_code')}")
    print(f"Duration: {details.get('duration')} sec")
    print(f"Text length: {len(text)} chars")
    if text:
        preview = text[:500].replace("\n", " ")
        print(f"Preview: {preview}{'...' if len(text) > 500 else ''}")


if __name__ == "__main__":
    main()

