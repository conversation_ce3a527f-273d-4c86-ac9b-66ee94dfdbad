version: "3.9"

services:
  mysql_db: # if you change this, you need to change the `SQLALCHEMY_DATABASE_URI` and `external_links` in the app service
    image: mysql:8.0
    command: --default-authentication-plugin=mysql_native_password --default-time-zone='+00:00'
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: shiyin_root # Change this to your database root password
      MYSQL_DATABASE: shiyin # Change this to your database name
      MYSQL_USER: shiyin_developer # Change this to your database user
      MYSQL_PASSWORD: shiyin_developer # Change this to your database password
      TZ: UTC
    ports:
      - "3307:3306" # Change this to your desired port
    volumes:
      - db_data:/var/lib/mysql

  redis:
    image: redis:7.0
    restart: always
    ports:
      - "6379:6379"
    command: redis-server --requirepass shiyin_redis # 设置 Redis 密码
    environment:
      - TZ=UTC
    volumes:
      - redis_data:/data

  app:
    # Option 1: Use pre-built image (recommended for development)
    # Build first: ./dev-setup.sh build-dev
    image: uniscribe-dev-image:latest

    # Option 2: Build directly with docker-compose (uncomment below, comment image above)
    # build:
    #   context: .
    #   dockerfile: Dockerfile
    restart: always
    environment: # read it from the .env file
      - APP_SETTINGS
      - SQLALCHEMY_DATABASE_URI
      - TENCENT_CLOUD_SECRET_ID
      - TENCENT_CLOUD_SECRET_KEY
      - OPENAI_API_KEY
      - CLOUDFLARE_R2_ACCESS_KEY
      - CLOUDFLARE_R2_SECRET_KEY
      - CLOUDFLARE_R2_ENDPOINT_URL
      - STRIPE_SECRET_KEY
      - STRIPE_WEBHOOK_SECRET
      - SUPABASE_URL
      - SUPABASE_ANON_KEY
      - SUPABASE_JWT_SECRET
      - YOUTUBE_PROXY
      - TZ=UTC
      - AXIOM_TOKEN
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=shiyin_redis
      - USERCHECK_API_KEY
      - APPSUMO_CLIENT_ID
      - APPSUMO_CLIENT_SECRET
      - APPSUMO_REDIRECT_URI
      - APPSUMO_API_KEY
      - APPSUMO_WEBHOOK_URL

    ports:
      - "8000:8000"
    external_links:
      - mysql_db:mysql_db
    depends_on:
      - mysql_db
      - redis
    volumes:
      - .:/app

  stripe_consumer:
    image: uniscribe-dev-image:latest # 使用相同的镜像
    restart: always
    environment: # 使用与 app 服务相同的环境变量
      - PYTHONPATH=/app # 添加这行来设置 PYTHONPATH
      - APP_SETTINGS
      - SQLALCHEMY_DATABASE_URI
      - TENCENT_CLOUD_SECRET_ID
      - TENCENT_CLOUD_SECRET_KEY
      - OPENAI_API_KEY
      - CLOUDFLARE_R2_ACCESS_KEY
      - CLOUDFLARE_R2_SECRET_KEY
      - CLOUDFLARE_R2_ENDPOINT_URL
      - STRIPE_SECRET_KEY
      - STRIPE_WEBHOOK_SECRET
      - SUPABASE_URL
      - SUPABASE_ANON_KEY
      - SUPABASE_JWT_SECRET
      - YOUTUBE_PROXY
      - TZ=UTC
      - AXIOM_TOKEN
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=shiyin_redis
      - USERCHECK_API_KEY
      - RESEND_API_KEY
      - APPSUMO_CLIENT_ID
      - APPSUMO_CLIENT_SECRET
      - APPSUMO_REDIRECT_URI
      - APPSUMO_API_KEY
      - APPSUMO_WEBHOOK_URL
    external_links:
      - mysql_db:mysql_db
    depends_on:
      - mysql_db
      - redis
      - app
    volumes:
      - .:/app
    command: ["python", "-u", "workers/stripe_consumer.py"] # -u 参数确保日志实时输出

  alignment_worker:
    image: uniscribe-dev-image:latest # 使用相同的镜像
    restart: always
    environment: # 使用与 app 服务相同的环境变量
      - PYTHONPATH=/app # 添加这行来设置 PYTHONPATH
      - APP_SETTINGS
      - SQLALCHEMY_DATABASE_URI
      - TENCENT_CLOUD_SECRET_ID
      - TENCENT_CLOUD_SECRET_KEY
      - OPENAI_API_KEY
      - CLOUDFLARE_R2_ACCESS_KEY
      - CLOUDFLARE_R2_SECRET_KEY
      - CLOUDFLARE_R2_ENDPOINT_URL
      - STRIPE_SECRET_KEY
      - STRIPE_WEBHOOK_SECRET
      - SUPABASE_URL
      - SUPABASE_ANON_KEY
      - SUPABASE_JWT_SECRET
      - YOUTUBE_PROXY
      - TZ=UTC
      - AXIOM_TOKEN
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=shiyin_redis
      - USERCHECK_API_KEY
      - RESEND_API_KEY
      - APPSUMO_CLIENT_ID
      - APPSUMO_CLIENT_SECRET
      - APPSUMO_REDIRECT_URI
      - APPSUMO_API_KEY
      - APPSUMO_WEBHOOK_URL

    external_links:
      - mysql_db:mysql_db
    depends_on:
      - mysql_db
      - redis
      - app
    volumes:
      - .:/app
    command: ["python", "-u", "workers/alignment_worker.py"] # -u 参数确保日志实时输出

  media_processor:
    image: uniscribe-dev-image:latest # 使用相同的镜像
    restart: always
    environment: # 使用与 app 服务相同的环境变量
      - PYTHONPATH=/app # 添加这行来设置 PYTHONPATH
      - APP_SETTINGS
      - SQLALCHEMY_DATABASE_URI
      - TENCENT_CLOUD_SECRET_ID
      - TENCENT_CLOUD_SECRET_KEY
      - OPENAI_API_KEY
      - CLOUDFLARE_R2_ACCESS_KEY
      - CLOUDFLARE_R2_SECRET_KEY
      - CLOUDFLARE_R2_ENDPOINT_URL
      - STRIPE_SECRET_KEY
      - STRIPE_WEBHOOK_SECRET
      - SUPABASE_URL
      - SUPABASE_ANON_KEY
      - SUPABASE_JWT_SECRET
      - YOUTUBE_PROXY
      - TZ=UTC
      - AXIOM_TOKEN
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=shiyin_redis
      - USERCHECK_API_KEY
      - RESEND_API_KEY
      - APPSUMO_CLIENT_ID
      - APPSUMO_CLIENT_SECRET
      - APPSUMO_REDIRECT_URI
      - APPSUMO_API_KEY
      - APPSUMO_WEBHOOK_URL
    external_links:
      - mysql_db:mysql_db
    depends_on:
      - mysql_db
      - redis
      - app
    volumes:
      - .:/app
    command: ["python", "-u", "workers/media_preprocessing_consumer.py"] # -u 参数确保日志实时输出

volumes:
  db_data:
  redis_data: # 添加 Redis 数据卷
