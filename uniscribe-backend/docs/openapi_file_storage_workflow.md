# OpenAPI 文件上传流程中的 FileStorage 业务流程

本文档详细描述了 OpenAPI 文件上传流程中 FileStorage 记录的状态流转和业务逻辑。

## 概述

OpenAPI 文件上传采用预签名 URL 的方式，允许用户直接上传文件到 R2 存储，然后创建转录任务。这个过程中涉及复杂的 FileStorage 状态管理，确保文件引用计数正确和存储资源不泄漏。

## 业务流程

### 1. 获取上传 URL 阶段

**API 端点**: `POST /api/v1/files/upload-url`

**业务逻辑**:
```python
# 1. 生成临时文件标识
fingerprint = str(uuid.uuid4())  # 使用 UUID 作为临时指纹
file_key = f"{user_id}-{fingerprint}.{file_ext}"

# 2. 创建 TEMPORARY 状态的 FileStorage 记录
file_storage = FileStorage(
    user_id=g.user.id,
    fingerprint=fingerprint,        # UUID，不是真实的文件指纹
    file_type=file_ext,
    file_key=file_key,
    file_size=file_size,
    reference_count=0,              # 临时文件从 0 开始
    state=FileState.TEMPORARY.value # 标记为临时状态
)
```

**状态**: `TEMPORARY`
**特点**:
- 使用 UUID 作为临时 fingerprint（不是文件的真实 MD5）
- reference_count 为 0（因为还没有实际的转录任务引用）
- 文件还未上传到存储

### 2. 用户上传文件阶段

**操作**: 用户使用预签名 URL 直接上传文件到 R2 存储

**FileStorage 状态**: 保持 `TEMPORARY`
**说明**: 此时文件已存在于 R2 存储中，但 FileStorage 记录状态不变

### 3. 创建转录任务阶段

**API 端点**: `POST /api/v1/transcriptions`

**业务逻辑**:
```python
# 1. 查找 TEMPORARY 状态的文件
temp_file_storage = FileStorage.query.filter_by(
    file_key=file_key,
    state=FileState.TEMPORARY.value
).first()

# 2. 创建 TranscriptionFile 记录（fingerprint 为空）
transcription_file = TranscriptionFile(
    user_id=user_id,
    fingerprint="",  # 空值，等待媒体预处理后填入真实指纹
    file_key=file_key,
    # ... 其他字段
)

# 3. 启动媒体预处理任务
```

**FileStorage 状态**: 保持 `TEMPORARY`
**说明**: 转录任务已创建，但 FileStorage 状态暂时不变，等待媒体预处理

### 4. 媒体预处理阶段

**处理器**: `MediaPreprocessingConsumer._process_url_download_task()`

**业务逻辑**:
```python
# 1. 下载文件并计算真实 MD5 指纹
real_fingerprint = calculate_md5(downloaded_file)

# 2. 更新 TranscriptionFile
tf.fingerprint = real_fingerprint
tf.file_key = f"{user_id}-{real_fingerprint}.{file_ext}"

# 3. 创建新的 ACTIVE FileStorage 记录
FileStorage.create_or_update_storage(
    tf.user_id,
    tf.fingerprint,  # 真实的 MD5 指纹
    tf.file_type,
    tf.file_key,     # 新的 file_key
    tf.file_size,
)

# 4. 清理临时 FileStorage 记录
self._cleanup_temporary_file_storage(tf.user_id, old_file_key)
```

**状态变化**:
- 创建新的 `ACTIVE` 状态记录（真实 fingerprint）
- 原 `TEMPORARY` 记录 → `PENDING_DELETION`

### 5. 临时记录清理阶段

**清理逻辑**:
```python
def _cleanup_temporary_file_storage(self, user_id, old_file_key):
    # 1. 查找临时记录
    temp_records = FileStorage.query.filter_by(
        user_id=user_id,
        file_key=old_file_key,
        state=FileState.TEMPORARY.value
    ).all()
    
    # 2. 使用正确的清理方法
    for temp_record in temp_records:
        FileStorage.decrement_reference(user_id, temp_record.fingerprint)
        # 这会将状态设置为 PENDING_DELETION
```

**状态变化**: `TEMPORARY` → `PENDING_DELETION`

### 6. 定时清理阶段

**清理任务**: `tasks/file_cleanup_task.py`

**业务逻辑**:
```python
# 1. 查找 PENDING_DELETION 状态的文件
pending_files = FileStorage.query.filter(
    FileStorage.state == FileState.PENDING_DELETION.value
).all()

# 2. 从 R2 存储删除实际文件
for file in pending_files:
    storage.permanently_delete_file(file.file_key)
    
    # 3. 更新状态为已删除
    file.state = FileState.DELETED.value
```

**状态变化**: `PENDING_DELETION` → `DELETED`

## 状态流转图

```
TEMPORARY (UUID fingerprint, ref_count=0)
    ↓ (媒体预处理完成)
PENDING_DELETION (通过 decrement_reference)
    ↓ (定时清理任务)
DELETED (文件已从 R2 删除)

同时创建:
ACTIVE (真实 MD5 fingerprint, ref_count=1)
```

## 数据库记录对比

| 阶段 | TEMPORARY 记录 | ACTIVE 记录 |
|------|----------------|-------------|
| 上传 URL 生成 | ✅ UUID fingerprint | ❌ 不存在 |
| 文件上传完成 | ✅ UUID fingerprint | ❌ 不存在 |
| 转录任务创建 | ✅ UUID fingerprint | ❌ 不存在 |
| 媒体预处理完成 | ✅ → PENDING_DELETION | ✅ 真实 MD5 fingerprint |
| 定时清理后 | ✅ → DELETED | ✅ ACTIVE |

## 关键设计要点

### 1. 为什么使用 TEMPORARY 状态？

- **预防存储泄漏**: 如果用户获取上传 URL 但从未上传文件，TEMPORARY 记录可以被定时清理
- **处理异常情况**: 如果媒体预处理失败，TEMPORARY 记录确保文件最终会被清理
- **状态追踪**: 明确区分临时上传和正式使用的文件

### 2. 为什么使用 UUID 作为临时 fingerprint？

- **唯一性**: 确保临时记录的唯一标识
- **区分性**: 与真实 MD5 fingerprint 明确区分
- **简单性**: 无需在上传阶段计算文件哈希

### 3. 为什么需要两个 FileStorage 记录？

- **临时记录**: 管理上传阶段的文件生命周期
- **正式记录**: 管理转录任务的文件引用和共享

### 4. 清理机制的重要性

- **使用 decrement_reference**: 遵循系统的文件管理规范
- **避免直接删除**: 确保 R2 存储文件也被正确清理
- **定时任务配合**: 实现完整的文件生命周期管理

## 异常处理

### 1. 用户获取上传 URL 但未上传文件
- TEMPORARY 记录会被定时清理任务处理
- 预签名 URL 过期后无法上传

### 2. 文件上传成功但未创建转录任务
- TEMPORARY 记录会被定时清理任务处理
- R2 中的文件会被删除

### 3. 媒体预处理失败
- TEMPORARY 记录保持，等待重试或清理
- 如果最终失败，定时任务会清理

### 4. 清理过程中的异常
- 使用 try-catch 包装，不影响主流程
- 记录错误日志，便于排查

## 监控和维护

### 1. 监控指标
- TEMPORARY 状态记录的数量和存在时间
- PENDING_DELETION 状态记录的处理速度
- 定时清理任务的执行情况

### 2. 日志记录
- 每个状态转换都有详细日志
- 清理操作的成功/失败记录
- 异常情况的错误日志

### 3. 定期检查
- 检查是否有长期停留在 TEMPORARY 状态的记录
- 验证 R2 存储与数据库记录的一致性
- 监控存储空间使用情况

## 注意事项

1. **不要直接删除 FileStorage 记录**: 必须使用 `decrement_reference()` 方法
2. **确保定时清理任务正常运行**: 否则会导致存储泄漏
3. **监控 TEMPORARY 记录的数量**: 异常增长可能表示系统问题
4. **备份重要数据**: 在修改清理逻辑前确保数据安全
