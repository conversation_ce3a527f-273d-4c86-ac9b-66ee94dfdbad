# 任务系统架构分析

## 概述

当前的任务系统是一个分布式异步处理架构，主要由 `uniscribe-backend`（Python Flask）和 `uniscribe-service`（Go）两个服务组成。系统负责处理音频转录、文本处理、说话人识别等多种类型的任务。

## 系统架构

### 整体架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │ uniscribe-      │    │ uniscribe-      │
│                 │───▶│ backend         │───▶│ service         │
│                 │    │ (Python Flask)  │    │ (Go)            │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │                        │
                              ▼                        ▼
                       ┌─────────────┐         ┌─────────────┐
                       │   MySQL     │         │ External    │
                       │ Database    │         │ AI Services │
                       └─────────────┘         └─────────────┘
                              │
                              ▼
                       ┌─────────────┐
                       │   Redis     │
                       │ (队列服务)   │
                       └─────────────┘
```

## 核心组件分析

### 1. 任务类型定义

#### 任务类型枚举

- **转录任务** (transcription = 1): 音频转文字
- **校对任务** (correction = 2): 文本校对 (已废弃)
- **摘要任务** (summary = 3): 生成摘要
- **关键词任务** (keywords = 4): 提取关键词 (已废弃)
- **大纲任务** (outline = 5): 生成大纲
- **问答任务** (qa_extraction = 6): 提取问答对
- **翻译任务** (translation = 7): 文本翻译
- **YouTube下载任务** (youtube_download = 8): 下载YouTube视频
- **说话人识别任务** (speaker_diarization = 9): 识别说话人

#### 任务状态

- **pending** (1): 等待执行
- **processing** (2): 处理中
- **completed** (3): 处理完成
- **failed** (4): 处理失败

#### 任务依赖关系

```
音频文件 → 转录任务 → 其他任务（并行执行）
         ↘
          说话人识别任务 → 对齐任务
```

**注意**: 校对任务和关键词任务已废弃，其他文本处理任务直接依赖转录任务。

### 2. 数据库设计

#### 核心表结构

**task 表**

```sql
- id: bigint (主键)
- file_id: bigint (关联转录文件)
- task_type: int (任务类型)
- status: int (任务状态)
- priority: int (优先级: 0=免费用户, 1=付费用户)
- started_time: timestamp
- completed_time: timestamp
- error_message: text
- transcription_type: varchar(20) (transcript/subtitle)
- requested_service_provider: varchar(50) (请求的AI模型)
- actual_service_provider: varchar(50) (实际使用的AI模型)
```

**task_result 表**

```sql
- id: bigint (主键)
- file_id: bigint (关联转录文件，唯一约束)
- duration: float (文件时长)
- language: varchar(20) (语言)
- detected_language: varchar(20) (检测到的语言)
- original_text: longtext (原始转录文本)
- segments: json (分段数据)
- corrected_text: longtext (校对后文本)
- summary: text (摘要)
- keywords: json (关键词)
- outline: text (大纲)
- qa_extraction: json (问答对)
- diarization_segments: json (说话人识别分段)
- is_aligned: boolean (是否已完成对齐)
```

**transcription_file 表**

```sql
- id: bigint (主键)
- user_id: bigint (用户ID)
- filename: varchar(100)
- file_url: varchar(255)
- file_size: int
- duration: float
- language_code: varchar(20)
- status: int (文件状态)
- transcription_type: varchar(20)
- enable_speaker_diarization: boolean
- folder_id: bigint (文件夹ID)
```

### 3. Backend (Python Flask) 组件

#### 控制器层 (controllers/task.py)

- **create_transcription_task()**: 创建转录任务
- **create_text_tasks()**: 创建文本处理任务
- **save_task_result()**: 保存任务结果
- **update_file_status()**: 更新文件状态

#### 资源层 (resources/task.py)

- **CreateTranscriptionTaskResource**: 前端创建转录任务接口
- **CreateTextTasksResource**: Go服务创建文本任务接口
- **TaskResultResource**: Go服务提交任务结果接口

#### 模型层

- **Task**: 任务模型，包含任务查询方法
- **TaskResult**: 任务结果模型
- **TranscriptionFile**: 转录文件模型

#### 服务层

- **ModelSelectionService**: 模型选择服务，根据文件特征选择合适的AI模型
- **AlignmentQueueService**: 对齐队列服务，使用Redis Streams管理说话人对齐任务
- **AlignmentService**: 对齐服务，处理转录文本与说话人识别结果的对齐
- **EntitlementService**: 权益服务，检查用户配额

### 4. Service (Go) 组件

#### 主程序 (cmd/uniscribe-service/main.go)

- 启动三个独立的任务队列：
  - 音频任务队列 (MAX_AUDIO_TASK_COUNT = 10)
  - 文本任务队列 (MAX_TEXT_TASK_COUNT = 10)
  - 说话人识别任务队列 (MAX_SPEAKER_DIARIZATION_TASK_COUNT = 3)
- 每个队列都有独立的获取器和处理器

#### 任务处理器

- **AudioTaskProcessor**: 处理音频转录任务
- **TextTaskProcessor**: 处理文本相关任务（校对、摘要、关键词等）
- **SpeakerDiarizationProcessor**: 处理说话人识别任务

#### 任务获取与结果发送

- **TaskReceiver**: 从Backend API获取任务
- **ResultSender**: 向Backend API发送处理结果

## 任务流程分析

### 1. 转录任务流程

```
1. 用户上传音频文件
2. Frontend调用Backend创建转录任务
3. Backend检查用户配额和权限
4. Backend选择合适的AI模型
5. Backend创建Task记录（状态：pending）并加入Redis队列
6. Go Service从Redis队列消费转录任务
7. Go Service调用AI服务进行转录
8. Go Service将结果发送回Backend
9. Backend保存转录结果到TaskResult
10. Backend创建后续文本处理任务
```

### 2. 文本处理任务流程

```
1. 转录任务完成后自动创建文本处理任务并加入Redis队列
2. Go Service从Redis队列消费文本任务
3. Go Service并行处理多种文本任务：
   - 校对任务
   - 摘要任务
   - 关键词提取
   - 大纲生成
   - 问答提取
4. Go Service将各任务结果发送回Backend
5. Backend更新TaskResult对应字段
```

### 3. 说话人识别与对齐流程

```
1. 如果启用说话人识别，创建说话人识别任务
2. Go Service处理说话人识别任务
3. 转录和说话人识别都完成后，触发对齐任务
4. 对齐任务通过Redis Streams队列处理
5. AlignmentService将转录文本与说话人信息对齐
6. 更新TaskResult的segments字段
```

## 当前架构的优势

1. **职责分离**: Backend负责业务逻辑和数据管理，Go Service专注于AI任务处理
2. **可扩展性**: 支持多种任务类型，易于添加新的处理类型
3. **容错性**: 任务失败时有错误记录和重试机制
4. **优先级支持**: 付费用户任务优先处理
5. **模型选择**: 根据文件特征智能选择最适合的AI模型
6. **并发处理**: Go Service支持多任务并发处理

## 当前架构的问题

1. **状态管理复杂**: 任务状态分散在多个地方，难以统一管理
2. **错误处理**: 缺乏统一的错误处理和重试机制
3. **监控不足**: 缺乏任务执行的详细监控和指标
4. **资源管理**: 没有动态的资源分配和负载均衡
5. **数据一致性**: 跨服务的数据一致性保证不够强

## API接口分析

### Backend API接口

#### 1. 任务创建接口

```python
# POST /tasks/transcription/create
# 前端调用，创建转录任务
CreateTranscriptionTaskResource
- 输入: transcriptionFileId
- 输出: taskId, status
- 权限: 需要用户认证
```

#### 2. 任务结果提交接口

```python
# POST /tasks/result
# Go Service调用，提交任务处理结果
TaskResultResource
- 输入: 根据任务类型不同的结果数据
- 处理: 更新任务状态和结果
- 权限: 内部服务调用（需要添加认证）
```

#### 3. 文本任务创建接口

```python
# POST /tasks/text/create
# Go Service调用，转录完成后创建文本处理任务
CreateTextTasksResource
- 输入: transcriptionFileId
- 输出: 创建的任务ID列表
- 权限: 内部服务调用（需要添加认证）
```

### Go Service处理流程

#### 1. 任务获取流程

```go
// 三个独立的goroutine分别处理不同类型的任务
// 1. 音频任务消费者
go redisConsumer.ConsumeTranscriptionTasks(ctx, audioTaskQueue)

// 2. 文本任务消费者
go redisConsumer.ConsumeTextTasks(ctx, textTaskQueue)

// 3. 说话人识别任务消费者
go redisConsumer.ConsumeSpeakerDiarizationTasks(ctx, speakerDiarizationTaskQueue)
```

#### 2. 任务处理流程

```go
// 根据任务类型分发到不同的处理器
switch task.TaskType {
case TaskTypeTranscription:
    audioTaskProcessor.Process(ctx, task)
case TaskTypeCorrection, TaskTypeSummary, ...:
    textTaskProcessor.Process(ctx, task)
case TaskTypeSpeakerDiarization:
    speakerDiarizationProcessor.Process(ctx, task)
}
```

#### 3. 结果发送流程

```go
// 统一的结果发送接口
type ResultSender interface {
    Send(result ResultPayload) error
}

// 不同类型的结果载荷
- TranscriptionResultPayload
- CorrectionResultPayload
- SummaryResultPayload
- KeywordResultPayload
- OutlineResultPayload
- QAResultPayload
- TaskFailedPayload
```

## 技术细节分析

### 1. 任务调度机制

#### 当前实现

- Go Service通过HTTP轮询Backend获取任务
- 轮询间隔：无任务时5秒，有错误时1秒
- 三个独立的轮询器分别处理不同类型的任务

#### 存在问题

- 轮询造成不必要的网络开销
- 任务响应延迟较高（最多5秒）
- 无法实现真正的负载均衡

### 2. 模型选择策略

#### 当前策略 (ModelSelectionService)

```python
# 文件大小 > 2GB 强制使用 REPLICATE_WHISPERX
# 启用说话人识别时的特殊处理
# 字幕类型使用 FAL_WHISPER
# 中文优先使用 DEEPINFRA_WHISPER
# 其他情况使用 OPENAI_WHISPER
```

#### 优化空间

- 可以根据历史成功率动态调整
- 考虑成本和速度的平衡
- 支持用户自定义模型偏好

### 3. 错误处理机制

#### 当前实现

- 任务失败时设置error_message字段
- Go Service有基本的重试机制（3次，指数退避）
- 使用Sentry进行错误追踪

#### 改进方向

- 实现更细粒度的错误分类
- 支持不同错误类型的不同重试策略
- 增加熔断器模式防止级联失败

### 4. 数据存储设计

#### 优势

- 使用JSON字段存储复杂数据结构
- 通过唯一约束防止重复任务
- 支持优先级排序

#### 问题

- task_result表承载过多职责
- 缺乏历史版本管理
- 大文本字段可能影响查询性能

## 重构建议

### 阶段一：消息队列改造

#### 1. 引入Redis Streams

```python
# 替换当前的轮询机制
class TaskQueue:
    def enqueue_task(self, task_type, task_data):
        # 使用Redis Streams发布任务
        pass

    def consume_tasks(self, task_type, consumer_group):
        # Go Service订阅任务流
        pass
```

#### 2. 任务路由优化

- 按任务类型分流到不同的Stream
- 支持任务优先级队列
- 实现死信队列处理失败任务

### 阶段二：架构重构

#### 1. 服务拆分

```
uniscribe-backend (API Gateway + 业务逻辑)
├── task-scheduler (任务调度服务)
├── transcription-service (转录服务)
├── text-processing-service (文本处理服务)
└── speaker-diarization-service (说话人识别服务)
```

#### 2. 状态管理统一

```python
class TaskStateMachine:
    def transition(self, task_id, from_state, to_state):
        # 状态转换验证和记录
        pass

    def get_task_status(self, task_id):
        # 统一的状态查询接口
        pass
```

### 阶段三：监控与运维

#### 1. 指标监控

- 任务处理时长分布
- 各AI模型成功率统计
- 队列长度和处理速度
- 资源使用情况

#### 2. 健康检查

- 各服务健康状态
- AI服务可用性检查
- 数据库连接状态
- Redis队列状态

#### 3. 告警机制

- 任务失败率过高告警
- 队列积压告警
- 服务不可用告警
- 资源使用率告警

### 阶段四：性能优化

#### 1. 数据库优化

```sql
-- 分离历史数据
CREATE TABLE task_history (
    id BIGINT PRIMARY KEY,
    task_id BIGINT,
    status_change JSON,
    created_time TIMESTAMP
);

-- 优化索引
CREATE INDEX idx_task_status_priority ON task(status, priority, created_time);
CREATE INDEX idx_task_type_status ON task(task_type, status);
```

#### 2. 缓存策略

- 任务结果缓存
- 用户配额缓存
- 模型选择结果缓存

#### 3. 并发优化

- 数据库连接池调优
- Go Service协程池管理
- Redis连接复用

## 实施计划

### 第一周：基础设施准备

1. 搭建Redis Streams环境
2. 设计新的任务队列接口
3. 编写迁移脚本

### 第二周：核心功能迁移

1. 实现基于Redis Streams的任务分发
2. 修改Go Service的任务获取逻辑
3. 保持向后兼容性

### 第三周：监控与测试

1. 添加监控指标
2. 压力测试新架构
3. 性能对比分析

### 第四周：上线与优化

1. 灰度发布新架构
2. 监控线上表现
3. 根据反馈进行优化

## 风险评估

### 高风险

- 数据迁移过程中的数据丢失
- 新架构的性能不如预期
- 第三方AI服务的兼容性问题

### 中风险

- 开发周期可能延长
- 团队学习新技术的成本
- 监控系统的复杂性增加

### 低风险

- 用户体验的短期影响
- 运维成本的增加
- 文档更新的工作量

## 总结

当前的任务系统虽然功能完整，但在架构设计上存在一些可以改进的地方。通过引入消息队列、优化状态管理、增强监控等手段，可以显著提升系统的性能、可靠性和可维护性。

建议采用渐进式重构的方式，分阶段实施改进，既能保证系统的稳定运行，又能逐步提升架构质量。
