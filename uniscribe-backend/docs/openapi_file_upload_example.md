# OpenAPI File Upload Example

This document provides examples of how to use the new OpenAPI file upload functionality.

## Overview

The file upload API allows users to:

1. Get pre-signed URLs for uploading files directly to storage
2. Create transcription tasks using uploaded files
3. Automatic cleanup of unused temporary files

## API Endpoints

### 1. Get Upload URLs

**Endpoint**: `POST /api/v1/files/upload-url`

**Request**:

```json
{
  "filename": "my-audio.mp3",
  "file_size": 1048576,
  "upload_expires_in": 3600,
  "download_expires_in": 1800
}
```

**Response**:

```json
{
  "success": true,
  "data": {
    "upload_url": "https://r2.cloudflare.com/bucket/12345-abc123.mp3?X-Amz-Signature=...",
    "download_url": "https://r2.cloudflare.com/bucket/12345-abc123.mp3?X-Amz-Signature=...",
    "file_key": "12345-abc123def456.mp3",
    "upload_expires_at": "2024-01-16T11:30:00Z",
    "download_expires_at": "2024-01-16T10:30:00Z"
  }
}
```

### 2. Upload File

Use the `upload_url` from step 1 to upload your file:

```bash
curl -X PUT "https://r2.cloudflare.com/bucket/12345-abc123.mp3?X-Amz-Signature=..." \
  -H "Content-Type: audio/mpeg" \
  --data-binary @my-audio.mp3
```

### 3. Create Transcription

**Endpoint**: `POST /api/v1/transcriptions`

**Request**:

```json
{
  "file_key": "12345-abc123def456.mp3",
  "language_code": "en",
  "transcription_type": "transcript",
  "enable_speaker_diarization": false,
  "webhook_url": "https://your-webhook-url.com"
}
```

**Response**:

```json
{
  "success": true,
  "data": {
    "id": "1234567890",
    "status": "queued",
    "created_at": "2024-01-15T10:30:00Z"
  }
}
```

## Complete Example

### Python Example

```python
import requests
import json

API_BASE = "https://api.uniscribe.co"
API_KEY = "your_api_key_here"
headers = {"X-API-Key": API_KEY, "Content-Type": "application/json"}

# Step 1: Get upload URLs
upload_request = {
    "filename": "interview.mp3",
    "file_size": 5242880,  # 5MB
    "upload_expires_in": 3600,    # 1 hour
    "download_expires_in": 1800   # 30 minutes
}

response = requests.post(
    f"{API_BASE}/api/v1/files/upload-url",
    headers=headers,
    json=upload_request
)

if response.status_code != 200:
    print(f"Failed to get upload URL: {response.text}")
    exit(1)

upload_data = response.json()["data"]
upload_url = upload_data["upload_url"]
file_key = upload_data["file_key"]

print(f"Got upload URL for file_key: {file_key}")

# Step 2: Upload file
with open("interview.mp3", "rb") as f:
    upload_response = requests.put(
        upload_url,
        data=f,
        headers={"Content-Type": "audio/mpeg"}
    )

if upload_response.status_code not in [200, 204]:
    print(f"Failed to upload file: {upload_response.text}")
    exit(1)

print("File uploaded successfully!")

# Step 3: Create transcription
transcription_request = {
    "file_key": file_key,
    "language_code": "en",
    "transcription_type": "transcript",
    "enable_speaker_diarization": True,
    "webhook_url": "https://your-app.com/webhook"
}

transcription_response = requests.post(
    f"{API_BASE}/api/v1/transcriptions",
    headers=headers,
    json=transcription_request
)

if transcription_response.status_code != 200:
    print(f"Failed to create transcription: {transcription_response.text}")
    exit(1)

transcription_data = transcription_response.json()["data"]
print(f"Transcription created with ID: {transcription_data['id']}")
print(f"Status: {transcription_data['status']}")
```

### JavaScript Example

```javascript
const API_BASE = "https://api.uniscribe.co";
const API_KEY = "your_api_key_here";

async function uploadAndTranscribe(file) {
  const headers = {
    "X-API-Key": API_KEY,
    "Content-Type": "application/json",
  };

  // Step 1: Get upload URLs
  const uploadUrlResponse = await fetch(`${API_BASE}/api/v1/files/upload-url`, {
    method: "POST",
    headers: headers,
    body: JSON.stringify({
      filename: file.name,
      file_size: file.size,
      upload_expires_in: 3600,
      download_expires_in: 1800,
    }),
  });

  if (!uploadUrlResponse.ok) {
    throw new Error(
      `Failed to get upload URL: ${await uploadUrlResponse.text()}`
    );
  }

  const uploadData = (await uploadUrlResponse.json()).data;
  console.log(`Got upload URL for file_key: ${uploadData.file_key}`);

  // Step 2: Upload file
  const uploadResponse = await fetch(uploadData.upload_url, {
    method: "PUT",
    body: file,
    headers: {
      "Content-Type": file.type,
    },
  });

  if (!uploadResponse.ok) {
    throw new Error(`Failed to upload file: ${await uploadResponse.text()}`);
  }

  console.log("File uploaded successfully!");

  // Step 3: Create transcription
  const transcriptionResponse = await fetch(
    `${API_BASE}/api/v1/transcriptions`,
    {
      method: "POST",
      headers: headers,
      body: JSON.stringify({
        file_key: uploadData.file_key,
        language_code: "en",
        transcription_type: "transcript",
        enable_speaker_diarization: true,
        webhook_url: "https://your-app.com/webhook",
      }),
    }
  );

  if (!transcriptionResponse.ok) {
    throw new Error(
      `Failed to create transcription: ${await transcriptionResponse.text()}`
    );
  }

  const transcriptionData = (await transcriptionResponse.json()).data;
  console.log(`Transcription created with ID: ${transcriptionData.id}`);
  console.log(`Status: ${transcriptionData.status}`);

  return transcriptionData;
}

// Usage
const fileInput = document.getElementById("file-input");
fileInput.addEventListener("change", async (event) => {
  const file = event.target.files[0];
  if (file) {
    try {
      await uploadAndTranscribe(file);
    } catch (error) {
      console.error("Error:", error);
    }
  }
});
```

## Error Handling

### Common Error Responses

1. **Invalid file size**:

```json
{
  "success": false,
  "error": {
    "message": "File size cannot exceed 5GB",
    "code": 10001
  }
}
```

2. **Unsupported file format**:

```json
{
  "success": false,
  "error": {
    "message": "Unsupported file type: xyz. Supported formats: mp3, wav, m4a, ...",
    "code": 10001
  }
}
```

3. **File not found**:

```json
{
  "success": false,
  "error": {
    "message": "File not found: 12345-abc123.mp3",
    "code": 10001
  }
}
```

## File Cleanup

- Temporary files are automatically cleaned up after 48 hours if not used for transcription
- Once a file is used for transcription, it becomes permanent and follows the normal file lifecycle
- Duplicate files are automatically deduplicated based on content hash

## Best Practices

1. **Set appropriate expiration times**: Use shorter times for better security
2. **Handle upload failures**: Implement retry logic for network issues
3. **Verify upload success**: Check the upload response status before creating transcription
4. **Use webhooks**: For better user experience with async processing
5. **Validate file types**: Check file format on client side before upload
