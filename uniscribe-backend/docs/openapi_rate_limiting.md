# OpenAPI Rate Limiting Implementation

## 📊 概述

为 OpenAPI 接口实现了基于 API Key 的频率限制功能，支持每分钟和每日请求限制。

## 🎯 设计方案

### API Key 级别 vs 用户级别

**选择：API Key 级别的 Rate Limit**

**优势：**
- ✅ **精细控制**：每个 API Key 可以有不同的限制
- ✅ **业务隔离**：同一用户的不同应用可以独立限制
- ✅ **已有基础**：API Key 模型已定义了 rate limit 字段
- ✅ **扩展性好**：未来可以支持不同套餐的不同限制

## 🏗️ 架构实现

### 1. 核心组件

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   OpenAPI       │    │  Rate Limit      │    │   Redis         │
│   Endpoints     │───▶│  Decorator       │───▶│   Backend       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         │                       ▼                       │
         │              ┌──────────────────┐             │
         └─────────────▶│  API Key Model   │◀────────────┘
                        │  (rate limits)   │
                        └──────────────────┘
```

### 2. 数据库字段

API Key 模型已包含：
```python
rate_limit_per_minute = Column(Integer, default=60)   # 每分钟请求数
rate_limit_per_day = Column(Integer, default=1000)    # 每日请求数
```

### 3. Redis 存储结构

```
Key Pattern: openapi_rate_limit:api_key:{api_key_id}:minute
Key Pattern: openapi_rate_limit:api_key:{api_key_id}:day
Value: List of request timestamps (sliding window)
TTL: 2 * window_size
```

## 🚀 使用方法

### 1. 基本装饰器

```python
from libs.openapi_rate_limit import openapi_rate_limit_standard

class MyAPIResource(Resource):
    @openapi_rate_limit_standard  # 同时应用每分钟和每日限制
    @openapi_auth_required
    def post(self):
        return {"message": "success"}
```

### 2. 自定义装饰器

```python
from libs.openapi_rate_limit import api_key_rate_limit

class MyAPIResource(Resource):
    @api_key_rate_limit(per_minute=True, per_day=False)  # 仅每分钟限制
    @openapi_auth_required
    def get(self):
        return {"data": "..."}
```

### 3. 便捷装饰器

```python
from libs.openapi_rate_limit import (
    openapi_rate_limit_per_minute_only,
    openapi_rate_limit_per_day_only
)

# 仅每分钟限制
@openapi_rate_limit_per_minute_only
@openapi_auth_required
def quick_endpoint():
    pass

# 仅每日限制
@openapi_rate_limit_per_day_only  
@openapi_auth_required
def batch_endpoint():
    pass
```

## ⚙️ 配置管理

### 1. 默认限制

```python
# 在 API Key 创建时设置默认值
api_key = APIKey(
    user_id=user_id,
    name=name,
    key_hash=key_hash,
    rate_limit_per_minute=60,    # 默认每分钟 60 次
    rate_limit_per_day=1000,     # 默认每日 1000 次
)
```

### 2. 动态调整

```python
# 为特定 API Key 调整限制
api_key = APIKey.get_by_id(api_key_id)
api_key.rate_limit_per_minute = 120  # 提升到每分钟 120 次
api_key.rate_limit_per_day = 5000    # 提升到每日 5000 次
db.session.commit()
```

### 3. 套餐级别配置

```python
# 根据用户套餐设置不同限制
RATE_LIMITS_BY_PLAN = {
    "basic": {"per_minute": 60, "per_day": 1000},
    "pro": {"per_minute": 120, "per_day": 5000},
    "enterprise": {"per_minute": 300, "per_day": 20000},
}

def create_api_key_with_plan_limits(user_id, plan_type):
    limits = RATE_LIMITS_BY_PLAN.get(plan_type, RATE_LIMITS_BY_PLAN["basic"])
    api_key = APIKey(
        user_id=user_id,
        rate_limit_per_minute=limits["per_minute"],
        rate_limit_per_day=limits["per_day"],
    )
```

## 🔧 错误处理

### 1. 异常类型

```python
from libs.openapi_rate_limit import OpenAPIRateLimitExceededError

try:
    # API 调用
    pass
except OpenAPIRateLimitExceededError as e:
    print(f"Rate limit exceeded: {e}")
    print(f"Retry after: {e.retry_after} seconds")
```

### 2. HTTP 响应

当触发 rate limit 时，返回：

```json
{
  "success": false,
  "message": "API rate limit exceeded (per minute). Maximum 60 requests per minute allowed. Try again in 45.2 seconds.",
  "code": 41009
}
```

**HTTP Headers:**
```
HTTP/1.1 429 Too Many Requests
Retry-After: 45
X-RateLimit-Retry-After: 45.2
```

## 📊 监控和统计

### 1. 获取限制状态

```python
from libs.openapi_rate_limit import get_api_key_rate_limit_status

status = get_api_key_rate_limit_status(api_key_id)
# 返回当前使用情况和重置时间
```

### 2. 日志记录

Rate limit 触发时会自动记录日志：

```
INFO - Rate limit exceeded for API key 123: per minute, max=60, retry_after=45.2s
```

## 🧪 测试

### 1. 单元测试

```python
def test_rate_limit_per_minute():
    # 模拟 API Key 设置
    api_key = create_test_api_key(rate_limit_per_minute=2)
    
    # 前两次请求应该成功
    for i in range(2):
        response = client.post('/api/v1/transcriptions', 
                             headers={'X-API-Key': api_key.key})
        assert response.status_code == 200
    
    # 第三次请求应该被限制
    response = client.post('/api/v1/transcriptions',
                         headers={'X-API-Key': api_key.key})
    assert response.status_code == 429
```

### 2. 集成测试

使用现有的 spam 测试脚本：

```bash
# 测试 rate limit 是否生效
python scripts/test_quota_exhaustion.py \
  --spam-mode \
  --max-requests 100 \
  --delay 0.1 \
  --file-url "https://example.com/audio.mp3"
```

## 🔄 部署和迁移

### 1. 数据库迁移

API Key 表已包含 rate limit 字段，无需额外迁移。

### 2. 现有 API Key 更新

```sql
-- 为现有 API Key 设置默认限制
UPDATE api_keys 
SET rate_limit_per_minute = 60, 
    rate_limit_per_day = 1000 
WHERE rate_limit_per_minute IS NULL 
   OR rate_limit_per_day IS NULL;
```

### 3. Redis 配置

确保 Redis 实例有足够内存存储 rate limit 数据：

```
# 估算内存使用
# 每个 API Key 每分钟: ~1KB
# 每个 API Key 每日: ~24KB  
# 1000 个活跃 API Key: ~25MB
```

## 🎛️ 高级配置

### 1. 自定义策略

```python
# 使用固定窗口策略（默认是滑动窗口）
@api_key_rate_limit(per_minute=True, strategy="fixed_window")
@openapi_auth_required
def my_endpoint():
    pass
```

### 2. 自定义后端

```python
from libs.rate_limit import RedisRateLimitBackend

# 使用自定义 Redis 配置
custom_backend = RedisRateLimitBackend(prefix="custom_rate_limit")

@api_key_rate_limit(per_minute=True, backend=custom_backend)
@openapi_auth_required  
def my_endpoint():
    pass
```

## 📈 性能考虑

1. **Redis 性能**：每次请求需要 1-2 次 Redis 操作
2. **内存使用**：滑动窗口需要存储请求时间戳
3. **网络延迟**：Redis 访问延迟影响响应时间
4. **清理策略**：TTL 自动清理过期数据

## 🔮 未来扩展

1. **动态限制**：根据系统负载动态调整限制
2. **地理位置**：基于地区的不同限制
3. **端点级别**：不同 API 端点的不同限制
4. **突发处理**：允许短时间突发请求
5. **白名单**：特定 API Key 免除限制
