# Duplicate Task Creation Root Cause Analysis and Fix

## Problem Description

The system was experiencing `IntegrityError` exceptions when trying to create tasks:

```
(pymysql.err.IntegrityError) (1062, "Duplicate entry '7370062152469712896-3' for key 'task.uniq_fileId_type'")
```

This error occurred when attempting to insert a task with a `(file_id, task_type)` combination that already existed in the database, violating the unique constraint `uniq_fileId_type`.

## Root Cause Analysis

### The Real Problem: Task Processing Flow Issue in Go Service

The duplicate task creation was not caused by concurrent requests or race conditions in the Python backend, but by a **fundamental flaw in the Go service's task processing flow**.

#### Task Processing Sequence

<augment_code_snippet path="uniscribe-service/internal/handlers/audio_processor.go" mode="EXCERPT">

```go
func (p *AudioTaskProcessorImpl) Process(ctx context.Context, task Task) error {
    // 1. Update task status to processing
    err := p.resultSender.UpdateTaskStatus(task.TaskID, constants.TaskStatusProcessing)

    // 2. Process transcription
    payload, err := p.processTranscription(ctx, task)
    if err != nil {
        return p.sendTaskFailedResult(task, err)
    }

    // 3. Send transcription result (sets status to completed)
    err = p.resultSender.Send(payload)
    if err != nil {
        return p.sendTaskFailedResult(task, err)
    }

    // 4. Create subsequent tasks - THIS IS THE PROBLEM!
    err = p.createSubsequentTasks(ctx, task)
    if err != nil {
        return p.sendTaskFailedResult(task, err)  // Causes entire task to retry
    }
    return nil
}
```

</augment_code_snippet>

#### The Critical Issue

**When `createSubsequentTasks` fails:**

1. ✅ **Transcription is already completed and saved** (step 3 succeeded)
2. ❌ **Creating subsequent tasks fails** (step 4 fails - network timeout, backend unavailable, etc.)
3. 🔄 **Entire task is marked as failed and retried**
4. 🔄 **On retry: transcription result already exists, but subsequent tasks are attempted again**
5. 💥 **Duplicate task creation causes IntegrityError**

#### Why createSubsequentTasks Fails

The `createSubsequentTasks` function can fail due to:

- **Network issues**: Timeout, connection refused, DNS resolution failures
- **Backend unavailability**: Python backend temporarily down or overloaded
- **Database connection issues**: MySQL connection pool exhausted
- **HTTP retry mechanism**: The retry client may give up after multiple attempts

<augment_code_snippet path="uniscribe-service/internal/httpclient/retry_client.go" mode="EXCERPT">

```go
// RetryClient with exponential backoff
for attempt := 0; attempt <= c.config.MaxRetries; attempt++ {
    // ... retry logic
    if attempt < c.config.MaxRetries && c.isRetryableError(err, 0) {
        time.Sleep(c.config.RetryDelay)
        continue
    }
    // Final failure after all retries
    return nil, fmt.Errorf("request failed after %d attempts: %v", attempt+1, err)
}
```

</augment_code_snippet>

### The Cascade Effect

1. **Go Service**: `createSubsequentTasks` fails → entire transcription task marked as failed
2. **Task Retry System**: Failed transcription task gets retried
3. **Go Service**: On retry, transcription already exists, but tries to create subsequent tasks again
4. **Python Backend**: `create_text_tasks` tries to insert duplicate tasks
5. **Database**: Unique constraint violation → IntegrityError

## Two-Layer Solution

### Layer 1: Go Service - Prevent Unnecessary Task Retries

**Problem**: Failing to create subsequent tasks should not cause the entire transcription task to fail and retry.

**Solution**: Make `createSubsequentTasks` more resilient and prevent task retries when transcription is already complete.

<augment_code_snippet path="uniscribe-service/internal/handlers/audio_processor.go" mode="EXCERPT">

```go
func (p *AudioTaskProcessorImpl) createSubsequentTasks(ctx context.Context, task Task) error {
    // ... HTTP request logic ...

    if err != nil {
        // Log the error but don't fail the entire task
        // The backend's create_text_tasks function now handles duplicates gracefully
        log.Printf("Warning: Failed to create subsequent tasks for file %d: %v", task.TranscriptionFileId, err)

        // Add monitoring but don't cause task retry
        sentryutil.CaptureException(err)

        // Return nil to prevent task retry - transcription is already complete
        return nil
    }

    // ... handle HTTP status codes similarly ...
    return nil
}
```

</augment_code_snippet>

**Key Changes**:

- **Graceful degradation**: Log errors but don't fail the entire task
- **Prevent cascading failures**: Return `nil` instead of error to prevent task retry
- **Enhanced monitoring**: Use Sentry for visibility without breaking the flow

### Layer 2: Python Backend - Handle Duplicate Insertions Gracefully

**Problem**: When duplicate tasks are attempted, the system crashes with IntegrityError.

**Solution**: Use `INSERT IGNORE` to handle duplicates gracefully.

<augment_code_snippet path="uniscribe-backend/controllers/task.py" mode="EXCERPT">

```python
@db_transaction_with_callbacks
def create_text_tasks(transcription_file_id):
    # ... setup code ...

    # Use INSERT IGNORE to handle duplicate insertions gracefully
    for task_type in task_types:
        task_id = id_generator.get_id()

        values = {
            "id": task_id,
            "file_id": transcription_file_id,
            "status": TaskStatus.pending.id,
            "task_type": task_type,
            "priority": priority,
        }

        stmt = insert(Task).prefix_with("IGNORE").values(**values)
        db.session.execute(stmt)

    # Get actually created tasks (some may have been skipped due to duplicates)
    actual_tasks = []
    for task_type in task_types:
        task = Task.get_by_file_id_and_type(
            file_id=transcription_file_id, task_type=task_type
        )
        if task:
            actual_tasks.append(task)

    return actual_tasks
```

</augment_code_snippet>

## Benefits of the Two-Layer Approach

### Immediate Benefits

1. **No More IntegrityErrors**: Duplicate task creation attempts are handled gracefully
2. **Improved Reliability**: Transcription tasks don't fail due to subsequent task creation issues
3. **Better User Experience**: Users get their transcription results even if text processing has temporary issues
4. **Reduced Resource Waste**: No unnecessary task retries consuming compute resources

### Long-term Benefits

1. **System Resilience**: Better handling of partial failures and network issues
2. **Operational Visibility**: Enhanced monitoring and logging for debugging
3. **Graceful Degradation**: Core functionality (transcription) continues even when auxiliary features fail
4. **Easier Maintenance**: Clear separation of concerns between transcription and text processing

## Prevention Strategies

### Code Review Guidelines

1. **Idempotency**: Ensure all operations can be safely retried
2. **Error Handling**: Distinguish between recoverable and non-recoverable errors
3. **Transaction Boundaries**: Keep transaction scope minimal and focused
4. **Monitoring**: Add comprehensive logging and error tracking

### Testing Strategies

1. **Failure Injection**: Test behavior when downstream services fail
2. **Network Simulation**: Test with network delays and timeouts
3. **Concurrent Testing**: Verify behavior under concurrent load
4. **Recovery Testing**: Ensure system recovers gracefully from failures

### Monitoring and Alerting

1. **Task Retry Rates**: Monitor for unusual retry patterns
2. **Subsequent Task Creation**: Track success/failure rates
3. **Database Constraint Violations**: Alert on any integrity errors
4. **End-to-End Latency**: Monitor complete task processing times

This comprehensive fix addresses both the immediate symptom (IntegrityError) and the root cause (flawed error handling in task processing flow), making the system more robust and reliable.
